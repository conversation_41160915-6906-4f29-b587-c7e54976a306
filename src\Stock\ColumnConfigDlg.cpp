﻿#include "pch.h"
#include "Stock.h"
#include "ColumnConfigDlg.h"
#include "afxdialogex.h"

IMPLEMENT_DYNAMIC(CColumnConfigDlg, CDialog)

// 构造函数
CColumnConfigDlg::CColumnConfigDlg(CWnd* pParent /*=NULL*/)
    : CDialog(CColumnConfigDlg::IDD, pParent)
    , m_columnCount(0)
    , m_currentSelIndex(-1)
{
    memset(m_columnInfos, 0, sizeof(m_columnInfos));
}

// 析构函数
CColumnConfigDlg::~CColumnConfigDlg()
{
}

// DoDataExchange - 数据交换与验证
void CColumnConfigDlg::DoDataExchange(CDataExchange* pDX)
{
    CDialog::DoDataExchange(pDX);
    DDX_Control(pDX, IDC_LIST_COLUMNS, m_listColumns);
    DDX_Control(pDX, IDC_COMBO_COLUMN_TYPE, m_comboColumnType);
    DDX_Control(pDX, IDC_COMBO_ALIGNMENT, m_comboAlignment);
    DDX_Control(pDX, IDC_EDIT_COLUMN_WIDTH, m_editColumnWidth);
    DDX_Control(pDX, IDC_EDIT_COLUMN_NAME, m_editColumnName);
}

// 消息映射
BEGIN_MESSAGE_MAP(CColumnConfigDlg, CDialog)
    ON_BN_CLICKED(IDC_BTN_MOVE_UP, &CColumnConfigDlg::OnBnClickedBtnMoveUp)
    ON_BN_CLICKED(IDC_BTN_MOVE_DOWN, &CColumnConfigDlg::OnBnClickedBtnMoveDown)
    ON_BN_CLICKED(IDC_BTN_SHOW, &CColumnConfigDlg::OnBnClickedBtnShow)
    ON_BN_CLICKED(IDC_BTN_HIDE, &CColumnConfigDlg::OnBnClickedBtnHide)
    ON_BN_CLICKED(IDC_BTN_RESET, &CColumnConfigDlg::OnBnClickedBtnReset)
    ON_BN_CLICKED(IDC_BTN_ADD, &CColumnConfigDlg::OnBnClickedBtnAdd)
    ON_BN_CLICKED(IDC_BTN_DELETE, &CColumnConfigDlg::OnBnClickedBtnDelete)
    ON_LBN_SELCHANGE(IDC_LIST_COLUMNS, &CColumnConfigDlg::OnLbnSelchangeListColumns)
    ON_CBN_SELCHANGE(IDC_COMBO_COLUMN_TYPE, &CColumnConfigDlg::OnCbnSelchangeComboColumnType)
    ON_CBN_SELCHANGE(IDC_COMBO_ALIGNMENT, &CColumnConfigDlg::OnCbnSelchangeComboAlignment)
    ON_EN_CHANGE(IDC_EDIT_COLUMN_WIDTH, &CColumnConfigDlg::OnEnChangeEditColumnWidth)
    ON_EN_CHANGE(IDC_EDIT_COLUMN_NAME, &CColumnConfigDlg::OnEnChangeEditColumnName)
END_MESSAGE_MAP()

// 设置列信息数组
void CColumnConfigDlg::SetColumnInfos(COLUMN_INFO* pColInfos, int count)
{
    if (pColInfos == NULL || count <= 0 || count > NUM_COLS_MAX)
        return;
    
    // 复制列信息
    memcpy(m_columnInfos, pColInfos, sizeof(COLUMN_INFO) * count);
    m_columnCount = count;
}

// 获取修改后的列信息数组
const COLUMN_INFO* CColumnConfigDlg::GetColumnInfos() const
{
    return m_columnInfos;
}

// 获取列数
int CColumnConfigDlg::GetColumnCount() const
{
    return m_columnCount;
}

// 对话框初始化
BOOL CColumnConfigDlg::OnInitDialog()
{
    CDialog::OnInitDialog();
    
    // 初始化列类型下拉框
    InitColumnTypeCombo();
    
    // 初始化对齐方式下拉框
    InitAlignmentCombo();
    
    // 更新列表框
    UpdateColumnList();
    
    // 默认选中第一项
    if (m_listColumns.GetCount() > 0)
    {
        m_listColumns.SetCurSel(0);
        OnLbnSelchangeListColumns();
    }
    
    // 更新控件状态
    UpdateControlStates();
    
    return TRUE;
}

// 更新列表框显示
void CColumnConfigDlg::UpdateColumnList()
{
    m_listColumns.ResetContent();
    
    for (int i = 0; i < m_columnCount; i++)
    {
        CString strItem;
        COLUMN_INFO& colInfo = m_columnInfos[i];
        
        // 格式化显示内容
        strItem.Format(_T("%d. %s (%s, %s, %dpx)%s"), 
            i + 1, 
            CString(colInfo._FieldName), 
            GetColumnTypeName(colInfo._DataType),
            GetAlignmentName(colInfo._Alignment),
            colInfo._Width,
            colInfo._Visible ? _T("") : _T(" [隐藏]"));
        
        m_listColumns.AddString(strItem);
    }
}

// 更新控件状态
void CColumnConfigDlg::UpdateControlStates()
{
    BOOL bHasSelection = (m_currentSelIndex >= 0 && m_currentSelIndex < m_columnCount);
    
    // 启用/禁用按钮
    GetDlgItem(IDC_BTN_MOVE_UP)->EnableWindow(bHasSelection && m_currentSelIndex > 0);
    GetDlgItem(IDC_BTN_MOVE_DOWN)->EnableWindow(bHasSelection && m_currentSelIndex < m_columnCount - 1);
    GetDlgItem(IDC_BTN_SHOW)->EnableWindow(bHasSelection && !m_columnInfos[m_currentSelIndex]._Visible);
    GetDlgItem(IDC_BTN_HIDE)->EnableWindow(bHasSelection && m_columnInfos[m_currentSelIndex]._Visible);
    GetDlgItem(IDC_BTN_RESET)->EnableWindow(TRUE);
    
    // 控制新增和删除按钮状态
    GetDlgItem(IDC_BTN_ADD)->EnableWindow(m_columnCount < NUM_COLS_MAX);
    GetDlgItem(IDC_BTN_DELETE)->EnableWindow(bHasSelection && m_currentSelIndex >= 3); // 不允许删除前3列
    
    // 列属性编辑控件
    m_comboColumnType.EnableWindow(bHasSelection);
    m_comboAlignment.EnableWindow(bHasSelection);
    m_editColumnWidth.EnableWindow(bHasSelection);
    m_editColumnName.EnableWindow(bHasSelection);
    
    // 如果有选择，更新属性显示
    if (bHasSelection)
    {
        // 设置列名称编辑框
        m_editColumnName.SetWindowText(m_columnInfos[m_currentSelIndex]._FieldName);
        
        // 设置列类型下拉框
        m_comboColumnType.SetCurSel(m_columnInfos[m_currentSelIndex]._DataType);
        
        // 设置对齐方式下拉框
        m_comboAlignment.SetCurSel(m_columnInfos[m_currentSelIndex]._Alignment);
        
        // 设置列宽度编辑框
        CString strWidth;
        strWidth.Format(_T("%d"), m_columnInfos[m_currentSelIndex]._Width);
        m_editColumnWidth.SetWindowText(strWidth);
    }
    else
    {
        m_editColumnName.SetWindowText(_T(""));
        m_comboColumnType.SetCurSel(-1);
        m_comboAlignment.SetCurSel(-1);
        m_editColumnWidth.SetWindowText(_T(""));
    }
}

// 初始化列类型下拉框
void CColumnConfigDlg::InitColumnTypeCombo()
{
    m_comboColumnType.ResetContent();
    
    m_comboColumnType.AddString(_T("字符串"));
    m_comboColumnType.AddString(_T("数字"));
    m_comboColumnType.AddString(_T("价格"));
    m_comboColumnType.AddString(_T("百分比"));
    m_comboColumnType.AddString(_T("日期"));
    m_comboColumnType.AddString(_T("状态"));
}

// 初始化对齐方式下拉框
void CColumnConfigDlg::InitAlignmentCombo()
{
    m_comboAlignment.ResetContent();
    
    m_comboAlignment.AddString(_T("左对齐"));
    m_comboAlignment.AddString(_T("居中"));
    m_comboAlignment.AddString(_T("右对齐"));
}

// 获取列类型名称
CString CColumnConfigDlg::GetColumnTypeName(int typeIndex)
{
    switch (typeIndex)
    {
    case COL_TYPE_STRING:
        return _T("字符串");
    case COL_TYPE_NUMBER:
        return _T("数字");
    case COL_TYPE_PRICE:
        return _T("价格");
    case COL_TYPE_PERCENT:
        return _T("百分比");
    case COL_TYPE_DATE:
        return _T("日期");
    case COL_TYPE_STATUS:
        return _T("状态");
    default:
        return _T("未知");
    }
}

// 获取对齐方式名称
CString CColumnConfigDlg::GetAlignmentName(int alignIndex)
{
    switch (alignIndex)
    {
    case COL_ALIGN_LEFT:
        return _T("左对齐");
    case COL_ALIGN_CENTER:
        return _T("居中");
    case COL_ALIGN_RIGHT:
        return _T("右对齐");
    default:
        return _T("未知");
    }
}

// 上移按钮事件
void CColumnConfigDlg::OnBnClickedBtnMoveUp()
{
    if (m_currentSelIndex <= 0 || m_currentSelIndex >= m_columnCount)
        return;
    
    // 交换当前选中项与上一项
    COLUMN_INFO tempInfo = m_columnInfos[m_currentSelIndex];
    m_columnInfos[m_currentSelIndex] = m_columnInfos[m_currentSelIndex - 1];
    m_columnInfos[m_currentSelIndex - 1] = tempInfo;
    
    // 更新显示顺序
    m_columnInfos[m_currentSelIndex]._DisplayOrder = m_currentSelIndex;
    m_columnInfos[m_currentSelIndex - 1]._DisplayOrder = m_currentSelIndex - 1;
    
    // 更新列表
    UpdateColumnList();
    
    // 选择移动后的项
    m_currentSelIndex--;
    m_listColumns.SetCurSel(m_currentSelIndex);
    
    // 更新控件状态
    UpdateControlStates();
}

// 下移按钮事件
void CColumnConfigDlg::OnBnClickedBtnMoveDown()
{
    if (m_currentSelIndex < 0 || m_currentSelIndex >= m_columnCount - 1)
        return;
    
    // 交换当前选中项与下一项
    COLUMN_INFO tempInfo = m_columnInfos[m_currentSelIndex];
    m_columnInfos[m_currentSelIndex] = m_columnInfos[m_currentSelIndex + 1];
    m_columnInfos[m_currentSelIndex + 1] = tempInfo;
    
    // 更新显示顺序
    m_columnInfos[m_currentSelIndex]._DisplayOrder = m_currentSelIndex;
    m_columnInfos[m_currentSelIndex + 1]._DisplayOrder = m_currentSelIndex + 1;
    
    // 更新列表
    UpdateColumnList();
    
    // 选择移动后的项
    m_currentSelIndex++;
    m_listColumns.SetCurSel(m_currentSelIndex);
    
    // 更新控件状态
    UpdateControlStates();
}

// 显示按钮事件
void CColumnConfigDlg::OnBnClickedBtnShow()
{
    if (m_currentSelIndex < 0 || m_currentSelIndex >= m_columnCount)
        return;
    
    // 显示选中列
    m_columnInfos[m_currentSelIndex]._Visible = true;
    
    // 更新列表
    UpdateColumnList();
    
    // 保持选择
    m_listColumns.SetCurSel(m_currentSelIndex);
    
    // 更新控件状态
    UpdateControlStates();
}

// 隐藏按钮事件
void CColumnConfigDlg::OnBnClickedBtnHide()
{
    if (m_currentSelIndex < 0 || m_currentSelIndex >= m_columnCount)
        return;
    
    // 隐藏选中列
    m_columnInfos[m_currentSelIndex]._Visible = false;
    
    // 更新列表
    UpdateColumnList();
    
    // 保持选择
    m_listColumns.SetCurSel(m_currentSelIndex);
    
    // 更新控件状态
    UpdateControlStates();
}

// 重置按钮事件
void CColumnConfigDlg::OnBnClickedBtnReset()
{
    // 确认是否重置
    if (MessageBox(_T("确定要恢复默认列配置吗？"), _T("确认"), MB_YESNO | MB_ICONQUESTION) != IDYES)
        return;
    
    // 告诉父窗口重置列配置
    // 使用负值表示需要重置
    m_columnCount = -1;
    
    // 关闭对话框
    OnOK();
}

// 列表框选择变更事件
void CColumnConfigDlg::OnLbnSelchangeListColumns()
{
    m_currentSelIndex = m_listColumns.GetCurSel();
    UpdateControlStates();
}

// 列类型下拉框选择变更事件
void CColumnConfigDlg::OnCbnSelchangeComboColumnType()
{
    if (m_currentSelIndex < 0 || m_currentSelIndex >= m_columnCount)
        return;
    
    // 获取当前选择的列类型
    int typeIndex = m_comboColumnType.GetCurSel();
    if (typeIndex < 0)
        return;
    
    // 更新列信息
    m_columnInfos[m_currentSelIndex]._DataType = typeIndex;
    
    // 根据列类型自动设置默认对齐方式
    switch (typeIndex)
    {
    case COL_TYPE_STRING:
    case COL_TYPE_DATE:
    case COL_TYPE_STATUS:
        m_columnInfos[m_currentSelIndex]._Alignment = COL_ALIGN_LEFT;
        break;
    case COL_TYPE_NUMBER:
    case COL_TYPE_PRICE:
    case COL_TYPE_PERCENT:
        m_columnInfos[m_currentSelIndex]._Alignment = COL_ALIGN_RIGHT;
        break;
    }
    
    // 更新UI
    m_comboAlignment.SetCurSel(m_columnInfos[m_currentSelIndex]._Alignment);
    
    // 更新列表显示
    UpdateColumnList();
}

// 对齐方式变更事件
void CColumnConfigDlg::OnCbnSelchangeComboAlignment()
{
    if (m_currentSelIndex < 0 || m_currentSelIndex >= m_columnCount)
        return;
    
    // 获取当前选择的对齐方式
    int alignIndex = m_comboAlignment.GetCurSel();
    if (alignIndex < 0)
        return;
    
    // 更新列信息
    m_columnInfos[m_currentSelIndex]._Alignment = alignIndex;
    
    // 更新列表显示
    UpdateColumnList();
}

// 列宽度编辑框变更事件
void CColumnConfigDlg::OnEnChangeEditColumnWidth()
{
    if (m_currentSelIndex < 0 || m_currentSelIndex >= m_columnCount)
        return;
    
    // 获取输入的列宽度
    CString strWidth;
    m_editColumnWidth.GetWindowText(strWidth);
    
    int width = _ttoi(strWidth);
    
    // 限制列宽度范围
    if (width >= 20 && width <= 300)
    {
        m_columnInfos[m_currentSelIndex]._Width = width;
        
        // 更新列表
        UpdateColumnList();
        
        // 保持选择
        m_listColumns.SetCurSel(m_currentSelIndex);
    }
}

// 添加新列按钮事件
void CColumnConfigDlg::OnBnClickedBtnAdd()
{
    // 检查是否已达到最大列数
    if (m_columnCount >= NUM_COLS_MAX)
    {
        MessageBox(_T("已达到最大列数限制，无法添加更多列。"), _T("提示"), MB_ICONINFORMATION);
        return;
    }
    
    // 创建新列信息
    COLUMN_INFO& newColInfo = m_columnInfos[m_columnCount];
    
    // 设置默认值
    strcpy_s(newColInfo._FieldName, "新列");
    strcpy_s(newColInfo._DataField, "new_field");
    newColInfo._Width = 80;
    newColInfo._Visible = true;
    newColInfo._DataType = COL_TYPE_STRING;
    newColInfo._Alignment = COL_ALIGN_LEFT;
    newColInfo._DisplayOrder = m_columnCount;
    newColInfo._UserDefined = true;
    
    // 增加列数
    m_columnCount++;
    
    // 更新列表
    UpdateColumnList();
    
    // 选择新添加的列
    m_currentSelIndex = m_columnCount - 1;
    m_listColumns.SetCurSel(m_currentSelIndex);
    
    // 更新控件状态
    UpdateControlStates();
}

// 删除列按钮事件
void CColumnConfigDlg::OnBnClickedBtnDelete()
{
    if (m_currentSelIndex < 0 || m_currentSelIndex >= m_columnCount)
        return;
    
    // 不允许删除必需的列（前3列：序号、代码、名称）
    if (m_currentSelIndex < 3)
    {
        MessageBox(_T("无法删除基本列（序号、代码、名称）。"), _T("提示"), MB_ICONWARNING);
        return;
    }
    
    // 移动后续列以填补空缺
    for (int i = m_currentSelIndex; i < m_columnCount - 1; i++)
    {
        m_columnInfos[i] = m_columnInfos[i + 1];
        m_columnInfos[i]._DisplayOrder = i;
    }
    
    // 清空最后一个位置
    memset(&m_columnInfos[m_columnCount - 1], 0, sizeof(COLUMN_INFO));
    
    // 减少列数
    m_columnCount--;
    
    // 更新列表
    UpdateColumnList();
    
    // 选择合适的列
    if (m_currentSelIndex >= m_columnCount)
        m_currentSelIndex = m_columnCount - 1;
    
    if (m_currentSelIndex >= 0)
        m_listColumns.SetCurSel(m_currentSelIndex);
    
    // 更新控件状态
    UpdateControlStates();
}

// 确定按钮事件
void CColumnConfigDlg::OnBnClickedOk()
{
    CDialog::OnOK();
}

// 处理列名称编辑变更事件
void CColumnConfigDlg::OnEnChangeEditColumnName()
{
    // 检查是否有选中的项
    if (m_currentSelIndex < 0 || m_currentSelIndex >= m_columnCount)
        return;
    
    // 获取当前编辑框内容
    CString strColumnName;
    m_editColumnName.GetWindowText(strColumnName);
    
    // 限制名称长度
    if (strColumnName.GetLength() > 31) // 考虑到 _FieldName 数组大小为 32
    {
        strColumnName = strColumnName.Left(31);
        m_editColumnName.SetWindowText(strColumnName);
        m_editColumnName.SetSel(31, 31);
    }
    
    // 更新列信息中的名称
    strcpy_s(m_columnInfos[m_currentSelIndex]._FieldName, strColumnName.GetBuffer());
    
    // 更新列表显示
    UpdateColumnList();
    
    // 重新选中当前项
    m_listColumns.SetCurSel(m_currentSelIndex);
} 