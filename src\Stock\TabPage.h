﻿#pragma once

// CTabPage.h: CTabPage 类的接口
// 自绘选项卡控件：背景色为黑色，选项卡无边框，与整个控件等宽
// 支持垂直文字排列，适合放置在应用程序左侧

// 声明回调函数类型
typedef void (CALLBACK* TABPAGE_NOTIFY_CALLBACK)(int nIndex, LPARAM lParam);

class CTabPage : public CWnd
{
	DECLARE_DYNAMIC(CTabPage)

// 构造
public:
	CTabPage();
	virtual ~CTabPage();

// 属性
protected:
	int m_nCurSel;              // 当前选中项
	int m_nTabHeight;           // 选项卡高度
	CStringArray m_arrTabNames; // 选项卡名称数组
	CObArray m_arrChildWnds;    // 子窗口数组
	COLORREF m_clrBackground;   // 背景色
	COLORREF m_clrText;         // 文字颜色
	COLORREF m_clrSelText;      // 选中项文字颜色
	COLORREF m_clrSelTab;       // 选中项背景色
	CFont m_fontTab;            // 选项卡字体
	bool m_bInitialized;        // 是否已初始化
	COLORREF m_clrSeparator;    // 分隔符颜色
	BOOL m_bShowSeparator;      // 是否显示分隔符
	
	// 回调函数相关
	TABPAGE_NOTIFY_CALLBACK m_pfnCallback;  // 回调函数指针
	LPARAM m_lParamCallback;                // 回调函数参数

// 操作
public:
	// 创建选项卡控件
	BOOL Create(const RECT& rect, CWnd* pParentWnd, UINT nID);
	
	// 添加一个选项卡页面
	BOOL AddPage(LPCTSTR lpszTitle, CWnd* pChildWnd = NULL);
	
	// 设置当前选中的选项卡
	void SetCurSel(int nSel);
	
	// 获取当前选中的选项卡
	int GetCurSel() const;
	
	// 获取选项卡总数
	int GetPageCount() const;
	
	// 设置背景色
	void SetBkColor(COLORREF clr);
	
	// 设置文字颜色
	void SetTextColor(COLORREF clr);
	
	// 设置选中项文字颜色
	void SetSelTextColor(COLORREF clr);
	
	// 设置选中项背景色
	void SetSelTabColor(COLORREF clr);
	
	// 设置分隔符颜色
	void SetSeparatorColor(COLORREF clr);
	
	// 设置是否显示分隔符
	void SetShowSeparator(BOOL bShow);
	
	// 设置选项卡点击回调函数
	void SetTabClickCallback(TABPAGE_NOTIFY_CALLBACK pfnCallback, LPARAM lParam = 0);
	
	// 设置选项卡高度
	void SetTabHeight(int nHeight);
	
	// 获取选项卡高度
	int GetTabHeight() const;

// 实现
protected:
	// 初始化控件
	void Initialize();
	
	// 绘制选项卡
	void DrawTabs(CDC* pDC);
	
	// 绘制垂直文字
	void DrawVerticalText(CDC* pDC, const CString& text, CRect rect, COLORREF color);
	
	// 获取选项卡矩形区域
	CRect GetTabRect(int nIndex) const;
	
	// 显示当前页面
	void ShowCurPage();
	
	// 创建选项卡字体
	void CreateTabFont();
	
	// 触发回调函数
	void NotifyTabClick(int nIndex);

// 消息映射
protected:
	DECLARE_MESSAGE_MAP()
	afx_msg void OnPaint();
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnSize(UINT nType, int cx, int cy);
}; 