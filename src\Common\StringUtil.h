﻿#pragma once

#include <string>
#include <vector>
#include <algorithm>
#include <locale>
#include <codecvt>
#include <sstream>

namespace StringUtil
{
    // 字符串分割函数
    inline std::vector<std::string> Split(const std::string& str, char delimiter)
    {
        std::vector<std::string> tokens;
        std::string token;
        std::istringstream tokenStream(str);
        while (std::getline(tokenStream, token, delimiter))
        {
            tokens.push_back(token);
        }
        return tokens;
    }

    // 去除字符串前后空白
    inline std::string Trim(const std::string& str)
    {
        const auto start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos)
            return "";

        const auto end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }

    // 转换为小写
    inline std::string ToLower(const std::string& str)
    {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](unsigned char c){ return std::tolower(c); });
        return result;
    }

    // 转换为大写
    inline std::string ToUpper(const std::string& str)
    {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](unsigned char c){ return std::toupper(c); });
        return result;
    }

    // 替换字符串中的所有指定子串
    inline std::string ReplaceAll(const std::string& str, const std::string& from, const std::string& to)
    {
        std::string result = str;
        size_t pos = 0;
        while ((pos = result.find(from, pos)) != std::string::npos)
        {
            result.replace(pos, from.length(), to);
            pos += to.length();
        }
        return result;
    }

    // 检查字符串是否以某个子串开始
    inline bool StartsWith(const std::string& str, const std::string& prefix)
    {
        if (str.length() < prefix.length())
            return false;
        return str.substr(0, prefix.length()) == prefix;
    }

    // 检查字符串是否以某个子串结束
    inline bool EndsWith(const std::string& str, const std::string& suffix)
    {
        if (str.length() < suffix.length())
            return false;
        return str.substr(str.length() - suffix.length()) == suffix;
    }
} 