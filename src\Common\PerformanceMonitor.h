﻿#pragma once
#include <chrono>
#include <string>
#include <unordered_map>
#include <mutex>
#include <vector>
#include <limits>
#include <algorithm>

// 性能监控工具类
class PerformanceMonitor {
public:
    // 性能统计数据
    struct PerfStats {
        std::string name;
        size_t callCount = 0;
        double totalTime = 0.0;  // 毫秒
        double minTime = (std::numeric_limits<double>::max)();
        double maxTime = 0.0;
        double avgTime = 0.0;
        
        void Update(double time) {
            callCount++;
            totalTime += time;
            minTime = (std::min)(minTime, time);
            maxTime = (std::max)(maxTime, time);
            avgTime = totalTime / callCount;
        }
    };
    
private:
    static std::unordered_map<std::string, PerfStats> s_stats;
    static std::mutex s_mutex;
    
public:
    // 性能计时器RAII类
    class Timer {
    private:
        std::string m_name;
        std::chrono::high_resolution_clock::time_point m_start;
        
    public:
        explicit Timer(const std::string& name) 
            : m_name(name), m_start(std::chrono::high_resolution_clock::now()) {}
        
        ~Timer() {
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - m_start);
            double timeMs = duration.count() / 1000.0;
            
            std::lock_guard<std::mutex> lock(s_mutex);
            s_stats[m_name].Update(timeMs);
        }
    };
    
    // 获取性能统计
    static std::vector<PerfStats> GetStats() {
        std::lock_guard<std::mutex> lock(s_mutex);
        std::vector<PerfStats> result;
        result.reserve(s_stats.size());
        
        for (const auto& pair : s_stats) {
            result.push_back(pair.second);
        }
        
        return result;
    }
    
    // 清除统计数据
    static void ClearStats() {
        std::lock_guard<std::mutex> lock(s_mutex);
        s_stats.clear();
    }
    
    // 获取特定函数的统计
    static PerfStats GetStat(const std::string& name) {
        std::lock_guard<std::mutex> lock(s_mutex);
        auto it = s_stats.find(name);
        if (it != s_stats.end()) {
            return it->second;
        }
        return PerfStats{name, 0, 0.0, 0.0, 0.0, 0.0};
    }
    
    // 打印性能报告
    static std::string GenerateReport() {
        auto stats = GetStats();
        std::string report = "=== 性能监控报告 ===\n";
        report += "函数名\t\t调用次数\t总时间(ms)\t平均时间(ms)\t最小时间(ms)\t最大时间(ms)\n";
        report += "--------------------------------------------------------------------------------\n";
        
        for (const auto& stat : stats) {
            char buffer[256];
            sprintf_s(buffer, "%-20s\t%zu\t\t%.2f\t\t%.2f\t\t%.2f\t\t%.2f\n",
                     stat.name.c_str(), stat.callCount, stat.totalTime, 
                     stat.avgTime, stat.minTime, stat.maxTime);
            report += buffer;
        }
        
        return report;
    }
};

// 静态成员定义
std::unordered_map<std::string, PerformanceMonitor::PerfStats> PerformanceMonitor::s_stats;
std::mutex PerformanceMonitor::s_mutex;

// 性能监控宏
#ifdef _DEBUG
    #define PERF_MONITOR(name) PerformanceMonitor::Timer _perf_timer(name)
    #define PERF_MONITOR_FUNC() PerformanceMonitor::Timer _perf_timer(__FUNCTION__)
#else
    #define PERF_MONITOR(name)
    #define PERF_MONITOR_FUNC()
#endif

// 内存使用监控类
class MemoryMonitor {
private:
    static size_t s_currentMemoryUsage;
    static size_t s_peakMemoryUsage;
    static std::mutex s_mutex;
    
public:
    // 记录内存分配
    static void RecordAllocation(size_t size) {
        std::lock_guard<std::mutex> lock(s_mutex);
        s_currentMemoryUsage += size;
        s_peakMemoryUsage = (std::max)(s_peakMemoryUsage, s_currentMemoryUsage);
    }
    
    // 记录内存释放
    static void RecordDeallocation(size_t size) {
        std::lock_guard<std::mutex> lock(s_mutex);
        s_currentMemoryUsage = (s_currentMemoryUsage >= size) ? 
                              (s_currentMemoryUsage - size) : 0;
    }
    
    // 获取当前内存使用量
    static size_t GetCurrentUsage() {
        std::lock_guard<std::mutex> lock(s_mutex);
        return s_currentMemoryUsage;
    }
    
    // 获取峰值内存使用量
    static size_t GetPeakUsage() {
        std::lock_guard<std::mutex> lock(s_mutex);
        return s_peakMemoryUsage;
    }
    
    // 重置统计
    static void Reset() {
        std::lock_guard<std::mutex> lock(s_mutex);
        s_currentMemoryUsage = 0;
        s_peakMemoryUsage = 0;
    }
    
    // 生成内存使用报告
    static std::string GenerateReport() {
        auto current = GetCurrentUsage();
        auto peak = GetPeakUsage();
        
        char buffer[256];
        sprintf_s(buffer, "=== 内存使用报告 ===\n当前使用: %.2f MB\n峰值使用: %.2f MB\n",
                 current / (1024.0 * 1024.0), peak / (1024.0 * 1024.0));
        
        return std::string(buffer);
    }
};

// 静态成员定义
size_t MemoryMonitor::s_currentMemoryUsage = 0;
size_t MemoryMonitor::s_peakMemoryUsage = 0;
std::mutex MemoryMonitor::s_mutex;

// 网络性能监控类
class NetworkMonitor {
private:
    struct NetworkStats {
        size_t requestCount = 0;
        size_t successCount = 0;
        size_t failureCount = 0;
        double totalTime = 0.0;
        size_t totalBytes = 0;
    };
    
    static std::unordered_map<std::string, NetworkStats> s_networkStats;
    static std::mutex s_networkMutex;
    
public:
    // 记录网络请求
    static void RecordRequest(const std::string& url, bool success, 
                             double timeMs, size_t bytes = 0) {
        std::lock_guard<std::mutex> lock(s_networkMutex);
        auto& stats = s_networkStats[url];
        stats.requestCount++;
        if (success) {
            stats.successCount++;
            stats.totalBytes += bytes;
        } else {
            stats.failureCount++;
        }
        stats.totalTime += timeMs;
    }
    
    // 获取网络统计
    static std::string GenerateReport() {
        std::lock_guard<std::mutex> lock(s_networkMutex);
        std::string report = "=== 网络性能报告 ===\n";
        report += "URL\t\t请求数\t成功数\t失败数\t总时间(ms)\t总字节数\n";
        report += "--------------------------------------------------------------------------------\n";
        
        for (const auto& pair : s_networkStats) {
            const auto& stats = pair.second;
            char buffer[512];
            sprintf_s(buffer, "%-30s\t%zu\t%zu\t%zu\t%.2f\t\t%zu\n",
                     pair.first.c_str(), stats.requestCount, stats.successCount,
                     stats.failureCount, stats.totalTime, stats.totalBytes);
            report += buffer;
        }
        
        return report;
    }
    
    // 清除统计
    static void ClearStats() {
        std::lock_guard<std::mutex> lock(s_networkMutex);
        s_networkStats.clear();
    }
};

// 静态成员定义
std::unordered_map<std::string, NetworkMonitor::NetworkStats> NetworkMonitor::s_networkStats;
std::mutex NetworkMonitor::s_networkMutex;
