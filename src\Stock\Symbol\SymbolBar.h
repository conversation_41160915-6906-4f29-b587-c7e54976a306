﻿#pragma once

// CSymbolBar - 股票列表视图顶部导航栏，自绘实现
class CSymbolBar : public CView
{
protected:
	DECLARE_DYNCREATE(CSymbolBar)

	// 构造/析构
	CSymbolBar();
	virtual ~CSymbolBar();

public:
	// 消息响应
	afx_msg void OnPaint();
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	afx_msg void OnLButtonUp(UINT nFlags, CPoint point);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	
	// 导航按钮点击回调函数
	typedef void (CALLBACK* ON_BUTTON_CLICKED)(int nButtonID, LPARAM lParam);

	// 设置回调函数
	void SetButtonClickCallback(ON_BUTTON_CLICKED pCallback, LPARAM lParam) {
		m_pCallback = pCallback;
		m_lParam = lParam;
	}

	// 复选框点击回调函数
	typedef void (CALLBACK* ON_CHECKBOX_CLICKED)(int nCheckBoxID, bool bChecked, LPARAM lParam);

	// 设置复选框回调函数
	void SetCheckBoxCallback(ON_CHECKBOX_CLICKED pCallback, LPARAM lParam);
	
	// 获取复选框状态
	bool GetCheckBoxState(int nCheckBoxID) const;

	// 重写
	virtual void OnDraw(CDC* pDC);
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);

	// 获取复选框状态掩码
	DWORD GetCheckBoxState() const { return m_dwCheckBoxState; }
	
	// 设置复选框状态
	void SetCheckBoxState(DWORD dwState);

private:
	// 按钮定义
	enum ButtonID {
		BTN_FAVORITE = 0,    // 自选股
		BTN_LIMIT_UP,        // 涨停个股
		BTN_LIMIT_DOWN,      // 跌停个股
		BTN_NEW_HIGH,        // 近期新高
		BTN_NEW_LOW,         // 近期新低
		BTN_STRONG,          // 近期强势
		BTN_COUNT            // 按钮总数
	};

	// 复选框定义
	enum CheckBoxID {
		CHK_SH_A = 0,            // 上证A
		CHK_SZ_A,            // 深证A
		CHK_GEM,             // 创业板
		CHK_STAR,            // 科创板
		CHK_BJ_A,            // 北证A
		CHK_COUNT            // 复选框总数
	};

	// 按钮结构
	struct ButtonInfo {
		CRect rect;          // 按钮区域
		CString text;        // 按钮文本
		bool isHover;        // 鼠标悬停状态
		bool isPressed;      // 按钮按下状态
		bool isSelected;     // 按钮选中状态
	};

	// 复选框结构
	struct CheckBoxInfo {
		CRect rect;          // 复选框区域
		CString text;        // 复选框文本
		bool isHover;        // 鼠标悬停状态
		bool isChecked;      // 复选框选中状态
	};

	// 按钮数组
	ButtonInfo m_buttons[BTN_COUNT];

	// 复选框数组
	CheckBoxInfo m_checkBoxes[CHK_COUNT];

	// 按钮回调函数
	ON_BUTTON_CLICKED m_pCallback;
	LPARAM m_lParam;

	// 复选框回调函数
	ON_CHECKBOX_CLICKED m_pCheckBoxCallback;
	LPARAM m_lCheckBoxParam;

	// 鼠标相关
	int m_nHoverButton;      // 鼠标悬停的按钮索引
	int m_nPressedButton;    // 鼠标按下的按钮索引
	int m_nSelectedButton;   // 当前选中的按钮索引
	int m_nHoverCheckBox;    // 鼠标悬停的复选框索引
	int m_nPressedCheckBox;  // 鼠标按下的复选框索引

	// 视觉样式相关
	COLORREF m_clrBackground;    // 背景色
	COLORREF m_clrText;          // 文字颜色
	COLORREF m_clrHover;         // 悬停颜色
	COLORREF m_clrPressed;       // 按下颜色
	COLORREF m_clrSelected;      // 选中颜色
	COLORREF m_clrBorder;        // 边框颜色
	COLORREF m_clrCheckBox;      // 复选框颜色
	COLORREF m_clrChecked;       // 复选框选中颜色
	int m_nButtonWidth;          // 按钮宽度
	int m_nButtonHeight;         // 按钮高度
	int m_nButtonMargin;         // 按钮间距
	int m_nCheckBoxWidth;        // 复选框宽度
	int m_nCheckBoxHeight;       // 复选框高度
	int m_nCheckBoxMargin;       // 复选框间距
	int m_nCheckBoxSize;         // 复选框选择框大小
	
	// 复选框状态掩码
	DWORD m_dwCheckBoxState;     // 保存所有复选框状态

	// 绘制函数
	void DrawButton(CDC* pDC, int nIndex);
	void DrawCheckBox(CDC* pDC, int nIndex);
	void UpdateButtonRects();
	void UpdateCheckBoxRects();
	int HitTestButton(CPoint point);   // 返回点击的按钮索引，-1表示没有点击到按钮
	int HitTestCheckBox(CPoint point); // 返回点击的复选框索引，-1表示没有点击到复选框

	DECLARE_MESSAGE_MAP()
}; 