﻿// TimeHistory.h: CTimeLine 类的接口
// 历史分时视图

#pragma once

#include "../StockDoc.h"
#include "../StockDef.h"
#include <vector>
#include <string>

// 使用TimeData.h中已定义的TimeLineData结构体

// 集合竞价时间数据结构
struct AUC_TIME
{
    int _Time;              // 时间
    float _Price;           // 价格
    float _MatchedVol;      // 匹配量(股)
    float _UnMatchedVol;    // 未匹配量(股)
    byte _Direction;        // 方向（买-1 卖-0）

    AUC_TIME() :
        _Time(0),
        _Price(0.0f),
        _MatchedVol(0.0f),
        _UnMatchedVol(0.0f),
        _Direction(0)
    {
    }
};

// 集合竞价扩展数据结构
struct AUCEX
{
    DWORD _Date;            // 日期
    float _AucPrice;        // 集合竞价价格
    float _MaxPrice;        // 最高价
    float _MinPrice;        // 最低价
    double _MaxVol;         // 最大成交量
    double _MatchedVol;     // 匹配量
    double _UnMatchedVol;   // 未匹配量
    double _OpenVol;        // 开盘成交量

    AUCEX() :
        _Date(0),
        _AucPrice(0.0f),
        _MaxPrice(0.0f),
        _MinPrice(0.0f),
        _MaxVol(0.0),
        _MatchedVol(0.0),
        _UnMatchedVol(0.0),
        _OpenVol(0.0)
    {
    }
};

class CTimeInfo;  // 添加CTimeInfo类的前向声明

class CTimeLine : public CView
{
protected: // 仅从序列化创建
	CTimeLine() noexcept;
	DECLARE_DYNCREATE(CTimeLine)

// 特性
public:
    CStockDoc* GetDocument() const;

// 操作
public:
    // 设置股票代码
    void SetStockCode(const std::string& strCode);
    
    // 显示/隐藏个股资讯区
    void ShowInfoArea(BOOL bShow);
    
    // 获取个股资讯区显示状态
    BOOL IsInfoAreaVisible() const;
    
    // 切换个股资讯区显示状态
    void ToggleInfoArea();
    
    // 显示/隐藏集合竞价区
    void ShowAuctionArea(BOOL bShow);
    
    // 切换集合竞价区显示状态
    void ToggleAuctionArea();
    
    // 显示/隐藏涨跌停价格坐标
    void ShowLimitPriceCoordinates(BOOL bShow);
   
    // 切换涨跌停价格坐标显示状态
    void ToggleLimitPriceCoordinates();

    // 显示/隐藏十字光标
    void ShowCrossCursor(BOOL bShow);
    
    // 切换十字光标显示状态
    void ToggleCrossCursor();
   
// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint); // 重写以响应文档更新

// 实现
public:
	virtual ~CTimeLine();

protected:
 
    // 菜单处理
    virtual void OnInitialUpdate();
    
    // 股票代码
    CString m_strCode;   // 当前股票代码
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);

    afx_msg BOOL OnEraseBkgnd(CDC* pDC);
    afx_msg void OnSize(UINT nType, int cx, int cy);
    
    // 键盘消息处理
    afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
    
    // 窗口销毁处理
    afx_msg void OnDestroy();
    
    // 定时器消息处理
    afx_msg void OnTimer(UINT_PTR nIDEvent);

    // 鼠标消息处理
    afx_msg void OnMouseMove(UINT nFlags, CPoint point);
    afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
    afx_msg void OnLButtonUp(UINT nFlags, CPoint point);
    afx_msg void OnLButtonDblClk(UINT nFlags, CPoint point);
   
// 生成的消息映射函数
protected:
	DECLARE_MESSAGE_MAP()

private:
    // 区域划分
    CRect m_rcClient;           // 客户区矩形
    CRect m_rcTimelineArea;     // 分时行情区
    CRect m_rcInfoArea;         // 个股资讯区
    
    // 分时行情区子区域
    CRect m_rcMainChart;        // 主图表区域
    CRect m_rcTopInfo;          // 顶部信息区域
    CRect m_rcBottomTime;       // 底部时间坐标区域
    CRect m_rcLeftPrice;        // 左侧价格坐标区域
    CRect m_rcRightPercent;     // 右侧百分比坐标区域
    
    // 主图表子区域
    CRect m_rcPriceArea;        // 价格区域
    CRect m_rcVolumeArea;       // 成交量区域
    CRect m_rcAuctionArea;      // 集合竞价区域
    
    // 区域控制参数
    int m_nInfoAreaHeight;      // 个股资讯区高度
    BOOL m_bShowInfoArea;       // 是否显示个股资讯区
    float m_fVolumeAreaRatio;   // 成交量区域占主图表区域的比例（默认0.25）
    BOOL m_bShowAuctionArea;    // 是否显示集合竞价区
    float m_fAuctionAreaRatio;  // 集合竞价区域占主图表宽度的比例（默认0.15）

    
    // 实时价格线相关
    float m_fCurrentPrice;           // 当前实时价格
    
    // 涨跌停价格坐标相关
    BOOL m_bShowLimitPriceCoordinates;   // 是否显示涨跌停价格坐标

    // 十字光标相关
    BOOL m_bShowCrossCursor;        // 是否显示十字光标
    BOOL m_bCrossCursorFixed;       // 十字光标是否固定
    CPoint m_ptCrossCursor;         // 十字光标位置
    TLINE_DATA m_cursorData;        // 十字光标位置对应的数据
    BOOL m_bCursorDataValid;        // 光标数据是否有效
    
    // 绘制函数
    void DrawTimelineArea(CDC* pDC);       // 绘制分时行情区
    void DrawInfoArea(CDC* pDC);           // 绘制个股资讯区
    
    // 绘制主图表相关 - 核心绘制方法
    void DrawMainChartBackground(CDC* pDC); // 绘制主图表背景和网格
    void DrawMainChartData(CDC* pDC);      // 绘制主图表数据
    void DrawBorders(CDC* pDC);            // 绘制所有边界线
    
    // 绘制集合竞价区域相关
    void DrawAuctionAreaBackground(CDC* pDC); // 绘制集合竞价区背景
    void DrawAuctionAreaBorders(CDC* pDC);    // 绘制集合竞价区边界线和垂直分割线
    
    // 绘制坐标系
    void DrawTopInfo(CDC* pDC);            // 绘制顶部信息
    void DrawBottomTime(CDC* pDC);         // 绘制底部时间坐标
    void DrawLeftPrice(CDC* pDC);          // 绘制左侧价格坐标
    void DrawRightPercent(CDC* pDC);       // 绘制右侧百分比坐标
    
    // 绘制十字光标
    void DrawCrossCursor(CDC* pDC);        // 绘制十字光标
    void DrawCursorInfo(CDC* pDC);         // 绘制光标信息提示
    
    // 获取鼠标位置对应的分时数据
    BOOL GetDataAtPoint(const CPoint& point, TLINE_DATA& data);
    
    // 键盘导航辅助函数
    void NavigateToPreviousStock();        // 切换到上一支股票
    void NavigateToNextStock();            // 切换到下一支股票
    void NavigateToPreviousDate();         // 切换到前一天数据
    void NavigateToNextDate();             // 切换到后一天数据
    
    // 计算当前股票的价格范围，确保所有绘制函数使用一致的价格范围
    bool CalculatePriceRange(std::string code, std::string name, float& preClose, float& minPrice, float& maxPrice);
    
    // 判断是否为交易时段
    bool IsTradingTime() const;
    
    // 更新布局
    void UpdateLayout();                   // 更新各区域位置和大小
    
    // 数据准备相关方法 - 新增
    void PrepareChartData();              // 准备图表数据，在绘制前调用
    void PrepareMainChartData();          // 准备主图表数据
    void PrepareVolumeData();             // 准备成交量数据
    void PrepareAuctionData();            // 准备集合竞价区数据

    CString m_strCurrentDate;               // 当前显示的日期
    double m_maxVolume;                      // 保存最大成交量值，用于绘制成交量文本
    
    // 分时数据相关函数
    void InitTimeData();                     // 初始化分时数据管理器
    BOOL LoadTimeData(const std::string& stockCode, const std::string& date); // 加载指定日期的分时数据
    BOOL FindAndLoadRecentData(const std::string& stockCode); // 查找最近的可用数据并加载
    
  
    // 顶部信息区显示数据
    struct TopInfoData {
        int time;           // 时间 (格式: HHMM)
        CString date;       // 日期
        float price;        // 当前价格
        float preClose;     // 昨收价
        float change;       // 涨跌额
        float changePercent;// 涨跌幅
        double volume;      // 成交量
        double amount;      // 成交额
        BOOL isValid;       // 数据是否有效
        
        TopInfoData() : time(0), price(0), preClose(0), change(0), 
                        changePercent(0), volume(0), amount(0), isValid(FALSE) {}
    };
    
    TopInfoData m_topInfoData;  // 保存当前顶部信息区显示的数据
    
    // 图表绘制数据缓存 - 新增
    struct ChartDrawingData {
		std::string	    code;                          // 股票代码
		std::string		name;                          // 股票名称
        float preClose;        // 昨收价
        float minPrice;        // 最低价
        float maxPrice;        // 最高价
        float priceRange;      // 价格范围
        double maxVolume;      // 最大成交量
        float minPercent;      // 最小涨跌幅
        float maxPercent;      // 最大涨跌幅
        float priceToPixel;    // 价格转像素比例
        double volumeToPixel;  // 成交量转像素比例
        std::vector<CPoint> pricePoints;    // 价格线点集
        std::vector<CPoint> avgPricePoints; // 均价线点集
        std::vector<std::pair<CPoint, CPoint>> volumeLines; // 成交量直线(起点,终点)
        std::vector<COLORREF> volumeColors; // 成交量颜色
        
        // 集合竞价区数据
        std::vector<CPoint> auctionPricePoints;   // 集合竞价价格线点集
        std::vector<std::pair<CPoint, CPoint>> auctionVolumeLines; // 集合竞价成交量直线(起点,终点)
        
        bool dataValid;        // 数据是否有效
        
        // 构造函数，初始化成员变量
        ChartDrawingData() : preClose(0), minPrice(0), maxPrice(0), 
                             priceRange(0), maxVolume(0), minPercent(0), 
                             maxPercent(0), priceToPixel(0), volumeToPixel(0),
                             dataValid(false) {}
                             
        // 清理方法
        void Clear() {
            preClose = 0;
            minPrice = 0;
            maxPrice = 0;
            priceRange = 0;
            maxVolume = 0;
            minPercent = 0;
            maxPercent = 0;
            priceToPixel = 0;
            volumeToPixel = 0;
            pricePoints.clear();
            avgPricePoints.clear();
            volumeLines.clear();
            volumeColors.clear();
            auctionPricePoints.clear();
            auctionVolumeLines.clear();
            dataValid = false;
        }
    };
    
    ChartDrawingData m_chartData;  // 图表数据缓存
    bool m_bNeedRecalcData;        // 是否需要重新计算数据
    
    
    // 鼠标相关消息处理
    afx_msg BOOL OnMouseWheel(UINT nFlags, short zDelta, CPoint pt);

    // 集合竞价数据成员
    AUCEX m_AucEx;                          // 集合竞价扩展数据
    std::vector<AUC_TIME> m_vecAucTime;     // 集合竞价时间数据
    DWORD m_MinDate;                        // 最小日期
    CString m_LastCode;                     // 最后一个股票代码
    
    // 定时器状态
    bool m_bLastTradingTimeStatus;          // 上次检查的交易时段状态
    
    // 集合竞价相关函数
    BOOL GetStockAucInfo(DWORD dwDate, CString strCode, float fOpen, AUCEX& _AucEx, std::vector<AUC_TIME>& vecAT, float& fPreClose);
    void DrawAucZone(CDC* pDC);             // 绘制集合竞价区域数据
    BOOL FileIsExists(const CString& strFilePath);  // 检查文件是否存在
    std::vector<std::string> split(const std::string& str, const std::string& delim);  // 字符串分割函数
    
    // 涨跌停价格计算函数
    float PriceLimitUp(const CString& strCode, float fPreClose);
    float PriceLimitDown(const CString& strCode, float fPreClose);
    float RoundUp(float val, int n);
    
    // 网络状态检查
    bool IsNetworkAvailable();
    
    // 缓存数据加载
    BOOL LoadCachedData(const std::string& stockCode, const std::string& date);
};



