﻿// KLineInfo.cpp: CTimeInfo 类的实现
//

#include "pch.h"
#include "..\framework.h"
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "TimeInfo.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CTimeInfo

IMPLEMENT_DYNCREATE(CTimeInfo, CView)

BEGIN_MESSAGE_MAP(CTimeInfo, CView)
	ON_WM_ERASEBKGND()
	ON_WM_SIZE()
	ON_WM_TIMER()
	ON_WM_LBUTTONDOWN()
	ON_WM_MOUSEMOVE()
	ON_WM_MOUSEWHEEL()
END_MESSAGE_MAP()

// CTimeInfo 构造/析构

CTimeInfo::CTimeInfo() noexcept
{
	// 初始化股票基本信息
	m_strCode = _T("600000");
	m_strName = _T("鸿图证券");
	
	// 初始化分时数据为0
	m_timeCurrent = 0;
	m_dblPrice = 0.0;
	m_dblAvgPrice = 0.0;
	m_dblOpen = 0.0;
	m_dblPreClose = 0.0;
	m_dblVolume = 0.0;
	m_dblAmount = 0.0;
	m_dblChange = 0.0;
	m_dblChangePercent = 0.0;
	
	// 初始化扩展数据为0
	m_dblHigh = 0.0;
	m_dblLow = 0.0;
	m_dblVolumeRatio = 0.0;
	m_dblTurnover = 0.0;
	m_nBuyVolume = 0;
	m_nSellVolume = 0;
	m_dblCirculatingValue = 0.0;
	m_dblMainInflow = 0.0;
	m_dblMainOutflow = 0.0;
	
	// 设置当前日期
	m_dateCurrent = CTime::GetCurrentTime();
	
	// 初始化行业和概念
	m_vecIndustry.push_back(_T("银行"));
	m_vecIndustry.push_back(_T("金融"));
	m_vecIndustry.push_back(_T("科技"));
	
	m_vecConcept.push_back(_T("大数据"));
	m_vecConcept.push_back(_T("人工智能"));
	m_vecConcept.push_back(_T("云计算"));
	
	// 初始化行业概念数据
	IndustryData data;
	
	data.strType = _T("行业");
	data.strName = _T("银行");
	data.strChange = _T("+2.35%");
	data.clrChange = RGB(255, 0, 0);
	m_vecIndustryData.push_back(data);
	
	data.strType = _T("行业");
	data.strName = _T("金融");
	data.strChange = _T("+1.67%");
	data.clrChange = RGB(255, 0, 0);
	m_vecIndustryData.push_back(data);
	
	data.strType = _T("行业");
	data.strName = _T("科技");
	data.strChange = _T("-0.89%");
	data.clrChange = RGB(0, 255, 0);
	m_vecIndustryData.push_back(data);
	
	data.strType = _T("概念");
	data.strName = _T("大数据");
	data.strChange = _T("+3.24%");
	data.clrChange = RGB(255, 0, 0);
	m_vecIndustryData.push_back(data);
	
	data.strType = _T("概念");
	data.strName = _T("人工智能");
	data.strChange = _T("+4.16%");
	data.clrChange = RGB(255, 0, 0);
	m_vecIndustryData.push_back(data);
	
	data.strType = _T("概念");
	data.strName = _T("云计算");
	data.strChange = _T("+0.78%");
	data.clrChange = RGB(255, 0, 0);
	m_vecIndustryData.push_back(data);
	
	// 初始化预警信息
	for (int i = 0; i < 5; i++) {
		AlertInfo alert;
		alert.nID = i + 1;
		alert.strName = _T("测试股票") + CString(char('A' + i));
		alert.strCode = _T("60000") + CString(char('0' + i));
		alert.strReason = _T("量比异常");
		
		m_vecAlerts.push_back(alert);
	}
	
	// 初始化滚动位置
	m_nIndustryScrollPos = 0;
	m_nIndustryTargetScrollPos = 0;
	m_dblIndustryScrollOffset = 0.0;
	
	m_nAlertScrollPos = 0;
	m_nAlertTargetScrollPos = 0;
	m_dblAlertScrollOffset = 0.0;
	
	// 初始化鼠标位置
	m_ptLastMouse = CPoint(-1, -1);
	
	// 初始化更新时间戳
	m_dwLastUpdateTime = 0;
}

CTimeInfo::~CTimeInfo()
{
}

// 获取文档指针
CStockDoc* CTimeInfo::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 设置股票代码和名称
void CTimeInfo::SetStockInfo(const CString& strCode, const CString& strName)
{
	m_strCode = strCode;
	m_strName = strName;
	
	// 重绘视图
	Invalidate();
}



// 添加行业概念数据
void CTimeInfo::AddIndustryData(const CString& strType, const CString& strName, 
                                 const CString& strChange, BOOL bPositive)
{
	IndustryData data;
	data.strType = strType;
	data.strName = strName;
	data.strChange = strChange;
	data.clrChange = bPositive ? RGB(255, 0, 0) : RGB(0, 255, 0);
	
	m_vecIndustryData.push_back(data);
	
	// 重绘视图
	Invalidate();
}

// 清空行业概念数据
void CTimeInfo::ClearIndustryData()
{
	m_vecIndustryData.clear();
	
	// 重绘视图
	Invalidate();
}

BOOL CTimeInfo::PreCreateWindow(CREATESTRUCT& cs)
{
	// 移除窗口边框
	cs.style &= ~WS_BORDER;
	return CView::PreCreateWindow(cs);
}

// 初始化视图
void CTimeInfo::OnInitialUpdate()
{
	CView::OnInitialUpdate();
	
	// 获取文档中当前股票的信息
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		// 获取当前股票代码
		std::string currentStock = pDoc->GetCurrentStock();
		if (!currentStock.empty())
		{
			// 获取股票名称
			CString stockCode(currentStock.c_str());
			CString stockName = pDoc->GetStockName(currentStock).c_str();
			
			// 更新股票信息
			m_strCode = stockCode;
			m_strName = stockName;
			
			// 打印调试信息
			TRACE(_T("TimeInfo初始化: 设置股票代码=%s, 名称=%s\n"), m_strCode, m_strName);
		}
	}
	
	// 设置预警区滚动定时器 (每3秒滚动一次)
	SetTimer(TIMER_ALERT_SCROLL, 3000, NULL);
	
	// 设置平滑滚动定时器 (每16ms更新一次，约60帧/秒)
	SetTimer(TIMER_SMOOTH_SCROLL, 16, NULL);
}

// CTimeInfo 绘图
void CTimeInfo::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;
	
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 创建内存DC和位图用于双缓冲绘图
	CDC memDC;
	memDC.CreateCompatibleDC(pDC);
	CBitmap memBitmap;
	memBitmap.CreateCompatibleBitmap(pDC, rectClient.Width(), rectClient.Height());
	CBitmap* pOldBitmap = memDC.SelectObject(&memBitmap);
	
	// 设置背景为黑色
	memDC.FillSolidRect(rectClient, RGB(0, 0, 0));
	
	// 绘制基本信息区域到内存DC
	DrawBasicInfo(&memDC);
	
	// 绘制价格信息区域到内存DC
	DrawPriceInfo(&memDC);
	
	// 绘制五档报价区域到内存DC
	DrawLevel2Info(&memDC);
	
	// 绘制行业概念区域到内存DC
	DrawIndustryInfo(&memDC);
	
	// 绘制预警区域到内存DC
	DrawAlertInfo(&memDC);
	
	// 将内存DC的内容复制到屏幕DC，一次性更新显示
	pDC->BitBlt(0, 0, rectClient.Width(), rectClient.Height(), &memDC, 0, 0, SRCCOPY);
	
	// 清理资源
	memDC.SelectObject(pOldBitmap);
	memBitmap.DeleteObject();
}

// 绘制基本信息
void CTimeInfo::DrawBasicInfo(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建标题字体，减小字体大小
	CFont titleFont;
	titleFont.CreateFont(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 选择字体
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 顶部区域，减小高度
	CRect rcTopArea(rectClient.left + 10, rectClient.top + 8, 
				 rectClient.right - 10, rectClient.top + 40);
	
	// 绘制股票名称（左对齐，黄色）
	CRect rcName = rcTopArea;
	rcName.right = rcName.left + rectClient.Width() / 3;
	pDC->SetTextColor(RGB(255, 255, 0)); // 黄色
	pDC->DrawText(m_strName, rcName, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制股票代码（居中对齐，蓝色）
	CRect rcCode = rcTopArea;
	rcCode.left = rcName.right;
	rcCode.right = rcCode.left + rectClient.Width() / 3;
	pDC->SetTextColor(RGB(0, 191, 255)); // 蓝色
	pDC->DrawText(m_strCode, rcCode, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制涨幅（右对齐）
	CRect rcChange = rcTopArea;
	rcChange.left = rcCode.right;
	CString strChangePercent;
	strChangePercent.Format(_T("%+.2f%%"), m_dblChangePercent);
	
	// 根据涨跌设置颜色
	COLORREF clrChange = (m_dblChange > 0) ? RGB(255, 0, 0) : 
						  (m_dblChange < 0) ? RGB(0, 255, 0) : RGB(255, 255, 255);
	pDC->SetTextColor(clrChange);
	pDC->DrawText(strChangePercent, rcChange, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	
	// 恢复文本颜色
	pDC->SetTextColor(RGB(255, 255, 255));
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
	
	// 绘制标题下方的深红色分隔线
	CPen linePen(PS_SOLID, 2, RGB(192, 0, 0));
	CPen* pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rectClient.left + 10, rcTopArea.bottom + 5);
	pDC->LineTo(rectClient.right - 10, rcTopArea.bottom + 5);
	
	// 恢复原笔
	pDC->SelectObject(pOldPen);
}

// 绘制价格信息
void CTimeInfo::DrawPriceInfo(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 获取文档对象以访问股票数据
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;
		
	// 从文档获取当前股票数据
	int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
	const StockData* pStock = nullptr;
	if (stockIndex >= 0) {
		pStock = pDoc->GetStock(stockIndex);
	}
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(220, 220, 220));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建标签字体
	CFont labelFont;
	labelFont.CreateFont(20, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, 
					    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					    DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 创建数据字体
	CFont dataFont;
	dataFont.CreateFont(22, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					  DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					  DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 起始Y坐标（从分割线下方开始）
	int startY = 70;
	int rowHeight = 28; // 略微调整行高以容纳更多信息
	int colGap = 10;
	
	// 两列布局
	int leftColX = rectClient.left + 10;
	int rightColX = rectClient.left + rectClient.Width() / 2 + 10;
	
	int colWidth = (rectClient.Width() - 30) / 2;
	int firstColWidth = 70; // 标签列宽度
	
	// 准备显示的数据
	// 更新从StockData直接获取的数据
	if (pStock) {
		m_dblPrice = pStock->_Close;
		m_dblPreClose = pStock->_preClose;
		m_dblOpen = pStock->_Open;
		m_dblHigh = pStock->_High;
		m_dblLow = pStock->_Low;
		m_dblVolume = pStock->_Volume;
		m_dblAmount = pStock->_Amount;
		m_dblVolumeRatio = pStock->_VolumeRatio;
		m_dblTurnover = pStock->_Turnover;
		m_nBuyVolume = pStock->_buyVolume;
		m_nSellVolume = pStock->_sellVolume;
		m_dblMainInflow = pStock->_MainInflow;
		m_dblMainOutflow = pStock->_MainOutflow;
		m_dblCirculatingValue = pStock->_CirculatingValue;
		
		// 计算涨跌额和涨跌幅
		if (m_dblPreClose > 0) {
			m_dblChange = m_dblPrice - m_dblPreClose;
			m_dblChangePercent = (m_dblPrice / m_dblPreClose - 1.0) * 100.0;
		} else {
			m_dblChange = 0.0;
			m_dblChangePercent = 0.0;
		}
		
		// 计算均价（如果有成交量）
		if (m_dblVolume > 0) {
			m_dblAvgPrice = m_dblAmount / m_dblVolume;
		}
		
		// 更新时间
		m_timeCurrent = CTime::GetCurrentTime();
	}
	
	// 根据实际数据生成格式化字符串
	CString strPrice, strChange, strChangePercent, strOpen, strPreClose;
	CString strAvgPrice, strVolume, strAmount, strTime;
	CString strHigh, strLow, strVolumeRatio, strTurnover;
	CString strBuyVolume, strSellVolume, strMainInflow, strMainOutflow;
	CString strCirculatingValue;
	
	// 格式化当前价格（保留两位小数）
	strPrice.Format(_T("%.2f"), m_dblPrice);
	
	// 格式化涨跌额（包含正负号，保留两位小数）
	strChange.Format(_T("%+.2f"), m_dblChange);
	
	// 格式化涨跌幅（包含正负号和百分号，保留两位小数）
	strChangePercent.Format(_T("%+.2f%%"), m_dblChangePercent);
	
	// 格式化开盘价和昨收价（保留两位小数）
	strOpen.Format(_T("%.2f"), m_dblOpen);
	strPreClose.Format(_T("%.2f"), m_dblPreClose);
	
	// 格式化最高价和最低价（保留两位小数）
	strHigh.Format(_T("%.2f"), m_dblHigh);
	strLow.Format(_T("%.2f"), m_dblLow);
	
	// 格式化均价（保留两位小数）
	strAvgPrice.Format(_T("%.2f"), m_dblAvgPrice);
	
	// 格式化量比（保留两位小数）
	strVolumeRatio.Format(_T("%.2f"), m_dblVolumeRatio);
	
	// 格式化换手率（保留两位小数，加百分号）
	strTurnover.Format(_T("%.2f%%"), m_dblTurnover);
	
	// 格式化成交量（单位转换：手）
	double dblVolumeHand = m_dblVolume / 100.0;
	if (dblVolumeHand >= 10000) {
		strVolume.Format(_T("%.2f万"), dblVolumeHand / 10000.0);
	} else {
		strVolume.Format(_T("%.0f"), dblVolumeHand);
	}
	
	// 格式化成交额（单位转换：万、亿）
	if (m_dblAmount >= 100000000) {
		strAmount.Format(_T("%.2f亿"), m_dblAmount / 100000000.0);
	} else if (m_dblAmount >= 10000) {
		strAmount.Format(_T("%.2f万"), m_dblAmount / 10000.0);
	} else {
		strAmount.Format(_T("%.0f"), m_dblAmount);
	}
	
	// 格式化内盘和外盘（单位：手）
	if (m_nBuyVolume >= 10000) {
		strBuyVolume.Format(_T("%.2f万"), m_nBuyVolume / 10000.0);
	} else {
		strBuyVolume.Format(_T("%d"), m_nBuyVolume);
	}
	
	if (m_nSellVolume >= 10000) {
		strSellVolume.Format(_T("%.2f万"), m_nSellVolume / 10000.0);
	} else {
		strSellVolume.Format(_T("%d"), m_nSellVolume);
	}
	
	// 格式化主力流入流出（单位：万、亿）
	if (fabs(m_dblMainInflow) >= 100000000) {
		strMainInflow.Format(_T("%.2f亿"), m_dblMainInflow / 100000000.0);
	} else {
		strMainInflow.Format(_T("%.2f万"), m_dblMainInflow / 10000.0);
	}
	
	if (fabs(m_dblMainOutflow) >= 100000000) {
		strMainOutflow.Format(_T("%.2f亿"), m_dblMainOutflow / 100000000.0);
	} else {
		strMainOutflow.Format(_T("%.2f万"), m_dblMainOutflow / 10000.0);
	}
	
	// 格式化总市值（单位：亿）
	strCirculatingValue.Format(_T("%.2f亿"), m_dblCirculatingValue);
	
	// 格式化时间
	if (m_timeCurrent.GetTime() > 0) {
		strTime = m_timeCurrent.Format(_T("%H:%M:%S"));
	} else {
		strTime = _T("--:--:--");
	}
	
	// 确定价格颜色（根据涨跌）
	COLORREF clrPrice = (m_dblChange > 0) ? RGB(255, 0, 0) : 
	                    (m_dblChange < 0) ? RGB(0, 255, 0) : 
						RGB(255, 255, 255);
	
	// 定义所有需要绘制的信息项
	struct InfoItem {
		int colX;           // 所在列的X坐标
		int row;            // 行号
		CString label;      // 标签文本
		CString value;      // 值文本
		COLORREF color;     // 值的颜色
	};
	
	// 计算涨停和跌停价格
	float limitUpPrice = m_dblPreClose * 1.1f;
	float limitDownPrice = m_dblPreClose * 0.9f;
	CString strLimitUp, strLimitDown;
	strLimitUp.Format(_T("%.2f"), limitUpPrice);
	strLimitDown.Format(_T("%.2f"), limitDownPrice);
	
	// 计算振幅
	float amplitude = 0.0f;
	if (m_dblPreClose > 0) {
		amplitude = (m_dblHigh - m_dblLow) * 100.0f / m_dblPreClose;
	}
	CString strAmplitude;
	strAmplitude.Format(_T("%.2f%%"), amplitude);
	
	// 假设值，通常从股票数据中获取
	CString strPE = _T("28.36");
	CString strTotalShares = _T("2.56亿");
	CString strCirculatingShares = _T("1.78亿");
	
	std::vector<InfoItem> infoItems = {
		// 左列数据 - 基本价格信息
		{leftColX, 0, _T("最新"), strPrice, clrPrice},
		{leftColX, 1, _T("涨跌"), strChange, clrPrice},
		{leftColX, 2, _T("振幅"), strAmplitude, RGB(255, 165, 0)},
		{leftColX, 3, _T("开盘"), strOpen, RGB(0, 255, 255)},
		{leftColX, 4, _T("昨收"), strPreClose, RGB(0, 255, 255)},
		{leftColX, 5, _T("最高"), strHigh, RGB(255, 0, 255)},
		{leftColX, 6, _T("最低"), strLow, RGB(0, 255, 255)},
		{leftColX, 7, _T("涨停"), strLimitUp, RGB(255, 0, 0)},
		{leftColX, 8, _T("跌停"), strLimitDown, RGB(0, 255, 0)},
		{leftColX, 9, _T("市盈率"), strPE, RGB(255, 255, 0)},
		
		// 右列数据 - 成交量和市值信息
		{rightColX, 0, _T("成交量"), strVolume, RGB(0, 255, 255)},
		{rightColX, 1, _T("成交额"), strAmount, RGB(0, 255, 255)},
		{rightColX, 2, _T("量比"), strVolumeRatio, RGB(255, 165, 0)},
		{rightColX, 3, _T("换手率"), strTurnover, RGB(255, 165, 0)},
		{rightColX, 4, _T("内盘"), strBuyVolume, RGB(255, 0, 0)},
		{rightColX, 5, _T("外盘"), strSellVolume, RGB(0, 255, 0)},
		{rightColX, 6, _T("总市值"), strCirculatingValue, RGB(255, 255, 0)},
		{rightColX, 7, _T("流通值"), strCirculatingValue, RGB(255, 255, 0)},
		{rightColX, 8, _T("总股本"), strTotalShares, RGB(0, 255, 255)},
		{rightColX, 9, _T("流通股"), strCirculatingShares, RGB(0, 255, 255)},
	};
	
	// 绘制所有信息项
	for (const auto& item : infoItems) {
		int itemY = startY + item.row * rowHeight;
		
		// 绘制标签
		pDC->SetTextColor(RGB(180, 180, 180)); // 标签使用浅灰色
		pDC->SelectObject(&labelFont);
		CRect rcLabel(item.colX, itemY, item.colX + firstColWidth, itemY + rowHeight);
		pDC->DrawText(item.label, rcLabel, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
		
		// 绘制数值（使用对应的颜色）
		pDC->SetTextColor(item.color);
		pDC->SelectObject(&dataFont);
		CRect rcValue(item.colX + firstColWidth, itemY, item.colX + colWidth - colGap, itemY + rowHeight);
		
		// 根据列位置决定对齐方式：左列左对齐，右列右对齐
		UINT alignment = (item.colX == leftColX) ? DT_LEFT : DT_RIGHT;
		pDC->DrawText(item.value, rcValue, alignment | DT_VCENTER | DT_SINGLELINE);
	}
	
	// 添加底部的分隔线
	int bottomY = startY + 11 * rowHeight + 5;
	
	CPen linePen(PS_SOLID, 2, RGB(100, 100, 100));
	CPen* pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rectClient.left + 10, bottomY);
	pDC->LineTo(rectClient.right - 10, bottomY);
	
	// 恢复原笔
	pDC->SelectObject(pOldPen);
}

// 绘制五档报价区域
void CTimeInfo::DrawLevel2Info(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 获取价格信息区域底部位置
	int priceInfoBottom = 70 + 11 * 28 + 10; // startY + 11*rowHeight + 间隔
	
	// 计算五档报价区域高度(固定的11行高度：1行标题+5行卖盘+5行买盘)
	int level2RowHeight = 26; // 每行高度
	int level2Height = level2RowHeight * 11 + 10; // 11行加底部间隔
	
	// 五档报价区域
	CRect rcLevel2(rectClient.left + 10, priceInfoBottom, 
				  rectClient.right - 10, priceInfoBottom + level2Height);
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建字体
	CFont dataFont;
	dataFont.CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					  DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					  DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 选择字体
	CFont* pOldFont = pDC->SelectObject(&dataFont);
	
	// 计算委差
	int orderDiff = 0;
	for (int i = 0; i < 5; i++) {
		orderDiff += (m_level2Data._bidVolume[i] - m_level2Data._askVolume[i]) / 100;
	}
	
	// 绘制顶部标题行（卖比和委差）
	CString strSellRatio, strOrderDiff;
	strSellRatio.Format(_T("卖比 %.2f%%"), m_dblChangePercent);
	strOrderDiff.Format(_T("委差 %d"), orderDiff);
	
	CRect rcTitle(rcLevel2.left, rcLevel2.top, rcLevel2.right, rcLevel2.top + level2RowHeight);
	
	// 设置卖比颜色（根据涨跌设置红绿色）
	COLORREF clrChange = (m_dblChangePercent > 0) ? RGB(255, 0, 0) : RGB(0, 255, 0);
	pDC->SetTextColor(clrChange);
	
	// 绘制卖比（左对齐）
	CRect rcSellRatio(rcLevel2.left, rcTitle.top, rcLevel2.left + rcLevel2.Width()/2, rcTitle.bottom);
	pDC->DrawText(strSellRatio, rcSellRatio, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制委差（右对齐）
	CRect rcOrderDiff(rcLevel2.left + rcLevel2.Width()/2, rcTitle.top, rcLevel2.right, rcTitle.bottom);
	pDC->DrawText(strOrderDiff, rcOrderDiff, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	
	// 在标题和卖五之间绘制分隔线
	CPen titleDividerPen(PS_SOLID, 1, RGB(100, 100, 100));
	CPen* pTitleDividerOldPen = pDC->SelectObject(&titleDividerPen);
	pDC->MoveTo(rcLevel2.left, rcTitle.bottom);
	pDC->LineTo(rcLevel2.right, rcTitle.bottom);
	pDC->SelectObject(pTitleDividerOldPen);
	
	// 添加标题区域与卖五之间的分隔带
	CRect rcDivider(rcLevel2.left, rcTitle.bottom, rcLevel2.right, rcTitle.bottom + 4);
	pDC->FillSolidRect(rcDivider, RGB(30, 30, 30));
	
	// 计算列宽
	int totalWidth = rcLevel2.Width();
	int colWidth = totalWidth / 3; // 分成三列
	
	// 设置底色为黑色
	CBrush blackBrush(RGB(0, 0, 0));
	pDC->FillRect(CRect(rcLevel2.left, rcTitle.bottom, rcLevel2.right, rcLevel2.bottom), &blackBrush);
	
	// 绘制卖盘（从上到下：卖五到卖一）
	for (int i = 0; i < 5; i++) {
		int rowTop = rcTitle.bottom + i * level2RowHeight;
		CRect rcRow(rcLevel2.left, rowTop, rcLevel2.right, rowTop + level2RowHeight);
		
		// 绘制序号
		CString strSellIndex;
		strSellIndex.Format(_T("卖%s"), i == 0 ? _T("五") : 
							(i == 1 ? _T("四") : 
							(i == 2 ? _T("三") : 
							(i == 3 ? _T("二") : _T("一")))));
		
		CRect rcSellIndex(rcLevel2.left, rowTop, rcLevel2.left + colWidth/3, rowTop + level2RowHeight);
		pDC->SetTextColor(RGB(255, 255, 255));
		pDC->DrawText(strSellIndex, rcSellIndex, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
		
		// 绘制卖价 - 根据与昨收比较设置颜色
		float sellPrice = m_level2Data._askPrice[4-i]; // 反向索引，卖五->0，卖一->4
		CString strSellPrice;
		strSellPrice.Format(_T("%.2f"), sellPrice);
		
		// 根据与昨收价格比较设置颜色
		COLORREF priceColor;
		if (sellPrice > m_dblPreClose) {
			priceColor = RGB(255, 0, 0); // 价格高于昨收，显示红色
		} else if (sellPrice < m_dblPreClose) {
			priceColor = RGB(0, 255, 0); // 价格低于昨收，显示绿色
		} else {
			priceColor = RGB(255, 255, 255); // 价格等于昨收，显示白色
		}
		
		CRect rcSellPrice(rcLevel2.left + colWidth, rowTop, rcLevel2.left + 2*colWidth, rowTop + level2RowHeight);
		pDC->SetTextColor(priceColor);
		pDC->DrawText(strSellPrice, rcSellPrice, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
		
		// 绘制卖量（青色）
		DWORD sellVolume = m_level2Data._askVolume[4-i];
		CString strSellVolume;
		
		// 成交量转为万手显示
		double sellVolumeHand = sellVolume / 100.0;
		if (sellVolumeHand >= 10000) {
			strSellVolume.Format(_T("%.2f万"), sellVolumeHand / 10000.0);
		} else {
			strSellVolume.Format(_T("%.2f"), sellVolumeHand / 1000.0);
			strSellVolume += _T("万");
		}
		
		CRect rcSellVolume(rcLevel2.left + 2*colWidth, rowTop, rcLevel2.right, rowTop + level2RowHeight);
		pDC->SetTextColor(RGB(0, 255, 255)); // 卖量用青色
		pDC->DrawText(strSellVolume, rcSellVolume, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	}
	
	// 在卖一和买一之间绘制分隔线
	int dividerY = rcTitle.bottom + 5 * level2RowHeight;
	CPen dividerPen(PS_SOLID, 1, RGB(80, 80, 80));
	CPen* pOldPen = pDC->SelectObject(&dividerPen);
	pDC->MoveTo(rcLevel2.left, dividerY);
	pDC->LineTo(rcLevel2.right, dividerY);
	pDC->SelectObject(pOldPen);
	
	// 绘制买盘（从上到下：买一到买五）
	for (int i = 0; i < 5; i++) {
		int rowTop = rcTitle.bottom + (i + 5) * level2RowHeight;
		CRect rcRow(rcLevel2.left, rowTop, rcLevel2.right, rowTop + level2RowHeight);
		
		// 绘制序号
		CString strBuyIndex;
		strBuyIndex.Format(_T("买%s"), i == 0 ? _T("一") : 
						  (i == 1 ? _T("二") : 
						  (i == 2 ? _T("三") : 
						  (i == 3 ? _T("四") : _T("五")))));
		
		CRect rcBuyIndex(rcLevel2.left, rowTop, rcLevel2.left + colWidth/3, rowTop + level2RowHeight);
		pDC->SetTextColor(RGB(255, 255, 255));
		pDC->DrawText(strBuyIndex, rcBuyIndex, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
		
		// 绘制买价 - 根据与昨收比较设置颜色
		float buyPrice = m_level2Data._bidPrice[i];
		CString strBuyPrice;
		strBuyPrice.Format(_T("%.2f"), buyPrice);
		
		// 根据与昨收价格比较设置颜色
		COLORREF priceColor;
		if (buyPrice > m_dblPreClose) {
			priceColor = RGB(255, 0, 0); // 价格高于昨收，显示红色
		} else if (buyPrice < m_dblPreClose) {
			priceColor = RGB(0, 255, 0); // 价格低于昨收，显示绿色
		} else {
			priceColor = RGB(255, 255, 255); // 价格等于昨收，显示白色
		}
		
		CRect rcBuyPrice(rcLevel2.left + colWidth, rowTop, rcLevel2.left + 2*colWidth, rowTop + level2RowHeight);
		pDC->SetTextColor(priceColor);
		pDC->DrawText(strBuyPrice, rcBuyPrice, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
		
		// 绘制买量（青色）
		DWORD buyVolume = m_level2Data._bidVolume[i];
		CString strBuyVolume;
		
		// 成交量转为万手显示
		double buyVolumeHand = buyVolume / 100.0;
		if (buyVolumeHand >= 10000) {
			strBuyVolume.Format(_T("%.2f万"), buyVolumeHand / 10000.0);
		} else {
			strBuyVolume.Format(_T("%.2f"), buyVolumeHand / 1000.0);
			strBuyVolume += _T("万");
		}
		
		CRect rcBuyVolume(rcLevel2.left + 2*colWidth, rowTop, rcLevel2.right, rowTop + level2RowHeight);
		pDC->SetTextColor(RGB(0, 255, 255)); // 买量用青色
		pDC->DrawText(strBuyVolume, rcBuyVolume, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	}
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
	
	// 在底部添加分割线
	CPen bottomLinePen(PS_SOLID, 2, RGB(100, 100, 100));
	pOldPen = pDC->SelectObject(&bottomLinePen);
	pDC->MoveTo(rcLevel2.left, rcLevel2.bottom - 5);
	pDC->LineTo(rcLevel2.right, rcLevel2.bottom - 5);
	pDC->SelectObject(pOldPen);
}

// 绘制行业概念信息
void CTimeInfo::DrawIndustryInfo(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 获取价格信息区域底部位置
	int priceInfoBottom = 70 + 11 * 28 + 10; // startY + 11*rowHeight + 间隔
	
	// 计算五档报价区域高度
	int level2RowHeight = 26; // 每行高度
	int level2Height = level2RowHeight * 11 + 10; // 11行加底部间隔
	
	// 五档报价区域底部位置
	int level2Bottom = priceInfoBottom + level2Height;
	
	// 行业概念区高度 (总高度减去价格区、五档报价区和顶部，再除以2)
	int remainingHeight = rectClient.Height() - level2Bottom;
	int industryHeight = remainingHeight / 2;
	
	// 行业概念区域
	CRect rcIndustry(rectClient.left + 10, level2Bottom, 
				   rectClient.right - 10, level2Bottom + industryHeight);
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建标题字体
	CFont titleFont;
	titleFont.CreateFont(22, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 创建内容字体
	CFont contentFont;
	contentFont.CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					     DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					     DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 选择字体
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 计算可用宽度并分配列宽
	int availableWidth = rcIndustry.Width();
	
	// 三个字段的列宽比例： 类别:名称:涨幅 = 2:5:3
	int totalUnits = 10;
	int categoryWidth = availableWidth * 2 / totalUnits; // 类别
	int nameWidth = availableWidth * 5 / totalUnits;     // 名称
	int changeWidth = availableWidth * 3 / totalUnits;   // 涨幅
	
	// 定义表头高度
	int headerHeight = 40;
	
	// 绘制表头
	CRect rcHeader(rcIndustry.left, rcIndustry.top + 5, rcIndustry.right, rcIndustry.top + 5 + headerHeight);
	
	// 表头背景 - 使用稍深的黑色
	pDC->FillSolidRect(rcHeader, RGB(0, 0, 0));
	
	// 计算各列起始位置
	int col1Start = rcIndustry.left;               // 类别
	int col2Start = col1Start + categoryWidth;     // 名称
	int col3Start = col2Start + nameWidth;         // 涨幅
	
	// 类别表头（左对齐）
	CRect rcCategoryHeader(col1Start, rcHeader.top, col2Start, rcHeader.bottom);
	pDC->SetTextColor(RGB(180, 180, 180));
	pDC->DrawText(_T("类别"), rcCategoryHeader, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 名称表头（左对齐，增加左右间距）
	CRect rcNameHeader(col2Start + 10, rcHeader.top, col3Start - 10, rcHeader.bottom);
	pDC->DrawText(_T("名称"), rcNameHeader, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 涨幅表头（右对齐）
	CRect rcChangeHeader(col3Start, rcHeader.top, rcIndustry.right, rcHeader.bottom);
	pDC->DrawText(_T("涨幅"), rcChangeHeader, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制表头下方的分隔线
	CPen linePen(PS_SOLID, 1, RGB(100, 100, 100));
	CPen* pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rcIndustry.left, rcHeader.bottom);
	pDC->LineTo(rcIndustry.right, rcHeader.bottom);
	
	// 添加分隔线下方的保护区域 (固定高度5像素)
	int protectionHeight = 5;
	CRect rcProtection(rcIndustry.left, rcHeader.bottom, rcIndustry.right, rcHeader.bottom + protectionHeight);
	pDC->FillSolidRect(rcProtection, RGB(0, 0, 0));
	
	// 设置行高
	int rowHeight = 30;
	int headerBottom = rcHeader.bottom + protectionHeight; // 包括保护区域
	
	// 定义内容区域 - 从表头(含保护区域)下方开始
	CRect rcContent(rcIndustry.left, headerBottom, rcIndustry.right, rcIndustry.bottom);
	
	// 在内容区域绘制剪切区域，确保内容不会溢出到表头
	pDC->IntersectClipRect(rcContent);
	
	// 获取当前鼠标位置以确定悬停行
	int hoverItem = HitTestIndustryItem(m_ptLastMouse);
	
	// 绘制数据行
	int rowCount = (int)m_vecIndustryData.size();
	int maxRows = (rcContent.Height()) / rowHeight;
	maxRows = min(maxRows, rowCount);
	
	// 确保滚动位置合法
	if (m_nIndustryScrollPos < 0) {
		m_nIndustryScrollPos = 0;
		m_nIndustryTargetScrollPos = 0;
		m_dblIndustryScrollOffset = 0.0;
	}
	else if (m_nIndustryScrollPos > rowCount - maxRows && rowCount > maxRows) {
		m_nIndustryScrollPos = rowCount - maxRows;
		m_nIndustryTargetScrollPos = m_nIndustryScrollPos;
		m_dblIndustryScrollOffset = 0.0;
	}
	
	// 计算平滑滚动的像素偏移量
	int pixelOffset = -(int)(m_dblIndustryScrollOffset * rowHeight);
	
	// 可能需要多绘制一行以实现平滑滚动效果
	int extraRowsNeeded = m_dblIndustryScrollOffset > 0 ? 1 : 0;
	
	// 使用滚动位置，绘制可见的行
	for (int i = 0; i < maxRows + extraRowsNeeded; i++) {
		int dataIndex = i + m_nIndustryScrollPos;
		if (dataIndex >= rowCount) {
			break; // 超出数据范围
		}
		
		int rowTop = headerBottom + i * rowHeight + pixelOffset;
		const IndustryData& data = m_vecIndustryData[dataIndex];
		
		// 判断当前行是否被鼠标悬停
		bool isHovered = (i == hoverItem);
		
		// 只绘制在可见区域内的行
		if (rowTop + rowHeight > headerBottom && rowTop < rcIndustry.bottom) {
			// 绘制背景（悬停行使用高亮背景，交替行使用深色背景）
			if (isHovered) {
				pDC->FillSolidRect(CRect(rcIndustry.left, rowTop, rcIndustry.right, rowTop + rowHeight), RGB(40, 40, 80));
			} 
			else if (dataIndex % 2 == 1) { // 注意这里是基于数据索引而不是视图索引
				pDC->FillSolidRect(CRect(rcIndustry.left, rowTop, rcIndustry.right, rowTop + rowHeight), RGB(15, 15, 15));
			}
			
			// 类别（左对齐）
			CRect rcCategory(col1Start, rowTop, col2Start - 10, rowTop + rowHeight);
			// 根据类型设置不同颜色
			pDC->SetTextColor(data.strType == _T("行业") ? 
				RGB(255, 160, 0) : // 行业用橙色
				RGB(0, 191, 255));  // 概念用蓝色
			pDC->DrawText(data.strType, rcCategory, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
			
			// 名称（左对齐，增加左右间距）
			CRect rcName(col2Start + 10, rowTop, col3Start - 10, rowTop + rowHeight);
			pDC->SetTextColor(isHovered ? RGB(220, 220, 220) : RGB(255, 255, 255));
			pDC->DrawText(data.strName, rcName, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
			
			// 涨幅（右对齐）
			CRect rcChange(col3Start, rowTop, rcIndustry.right, rowTop + rowHeight);
			pDC->SetTextColor(isHovered ? 
				(data.clrChange == RGB(255, 0, 0) ? RGB(255, 128, 128) : RGB(128, 255, 128)) : 
				data.clrChange);
			pDC->DrawText(data.strChange, rcChange, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
		}
	}
	
	// 重置剪切区域
	pDC->SelectClipRgn(NULL);
	
	// 绘制底部分隔线
	pDC->MoveTo(rcIndustry.left, rcIndustry.bottom);
	pDC->LineTo(rcIndustry.right, rcIndustry.bottom);
	
	// 恢复原笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 绘制预警信息
void CTimeInfo::DrawAlertInfo(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 获取价格信息区域底部位置
	int priceInfoBottom = 70 + 11 * 28 + 10; // startY + 11*rowHeight + 间隔
	
	// 计算五档报价区域高度
	int level2RowHeight = 26; // 每行高度
	int level2Height = level2RowHeight * 11 + 10; // 11行加底部间隔
	
	// 五档报价区域底部位置
	int level2Bottom = priceInfoBottom + level2Height;
	
	// 行业概念区高度 (总高度减去价格区、五档报价区和顶部，再除以2)
	int remainingHeight = rectClient.Height() - level2Bottom;
	int industryHeight = remainingHeight / 2;
	int alertTop = level2Bottom + industryHeight;
	
	// 在预警区域顶部添加分隔线
	CPen topDividerPen(PS_SOLID, 2, RGB(100, 100, 100));
	CPen* pTopDividerOldPen = pDC->SelectObject(&topDividerPen);
	pDC->MoveTo(rectClient.left + 10, alertTop);
	pDC->LineTo(rectClient.right - 10, alertTop);
	pDC->SelectObject(pTopDividerOldPen);
	
	// 预警区域
	CRect rcAlert(rectClient.left + 10, alertTop, 
				rectClient.right - 10, rectClient.bottom - 10);
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建内容字体
	CFont contentFont;
	contentFont.CreateFont(22, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, 
					     DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					     DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 选择字体
	CFont* pOldFont = pDC->SelectObject(&contentFont);
	
	// 计算可用宽度并分配列宽
	int availableWidth = rcAlert.Width();
	
	// 调整列宽比例：股票代码:股票名称:预警原因 = 3:3:4
	int totalUnits = 10;
	int codeWidth = availableWidth * 3 / totalUnits;     // 股票代码
	int nameWidth = availableWidth * 3 / totalUnits;     // 股票名称
	int reasonWidth = availableWidth * 4 / totalUnits;   // 预警原因
	
	// 定义表头高度
	int headerHeight = 40;
	
	// 绘制表头 - 直接从顶部开始
	CRect rcHeader(rcAlert.left, rcAlert.top, rcAlert.right, rcAlert.top + headerHeight);
	
	// 表头背景 - 使用稍深的黑色
	pDC->FillSolidRect(rcHeader, RGB(0, 0, 0));
	
	// 计算各列起始位置
	int col1Start = rcAlert.left;               // 股票代码
	int col2Start = col1Start + codeWidth;      // 股票名称
	int col3Start = col2Start + nameWidth;      // 预警原因
	
	// 设置表头文本颜色
	pDC->SetTextColor(RGB(180, 180, 180));
	
	// 股票代码表头（左对齐）
	CRect rcCodeHeader(col1Start, rcHeader.top, col2Start - 10, rcHeader.bottom);
	pDC->DrawText(_T("代码"), rcCodeHeader, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 股票名称表头（左对齐，增加左间距）
	CRect rcNameHeader(col2Start + 10, rcHeader.top, col3Start - 10, rcHeader.bottom);
	pDC->DrawText(_T("名称"), rcNameHeader, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 预警原因表头（右对齐）
	CRect rcReasonHeader(col3Start, rcHeader.top, rcAlert.right, rcHeader.bottom);
	pDC->DrawText(_T("预警原因"), rcReasonHeader, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制表头下方的分隔线
	CPen linePen(PS_SOLID, 1, RGB(100, 100, 100));
	CPen* pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rcAlert.left, rcHeader.bottom);
	pDC->LineTo(rcAlert.right, rcHeader.bottom);
	
	// 添加分隔线下方的保护区域 (固定高度5像素)
	int protectionHeight = 5;
	CRect rcProtection(rcAlert.left, rcHeader.bottom, rcAlert.right, rcHeader.bottom + protectionHeight);
	pDC->FillSolidRect(rcProtection, RGB(0, 0, 0));
	
	// 设置行高
	int rowHeight = 30;
	int headerBottom = rcHeader.bottom + protectionHeight; // 包括保护区域
	
	// 在内容区域绘制剪切区域，确保内容不会溢出
	CRect rcContent(rcAlert.left, headerBottom, rcAlert.right, rcAlert.bottom);
	pDC->IntersectClipRect(rcContent);
	
	// 获取点击测试结果
	int hoverItem = HitTestAlertItem(m_ptLastMouse);
	
	// 绘制预警数据
	int rowCount = (int)m_vecAlerts.size();
	int maxRows = (rcContent.Height()) / rowHeight;
	maxRows = min(maxRows, rowCount);
	
	// 确保滚动位置合法
	if (m_nAlertScrollPos < 0) {
		m_nAlertScrollPos = 0;
		m_nAlertTargetScrollPos = 0;
		m_dblAlertScrollOffset = 0.0;
	}
	else if (m_nAlertScrollPos > rowCount - maxRows && rowCount > maxRows) {
		m_nAlertScrollPos = rowCount - maxRows;
		m_nAlertTargetScrollPos = m_nAlertScrollPos;
		m_dblAlertScrollOffset = 0.0;
	}
	
	// 计算平滑滚动的像素偏移量
	int pixelOffset = -(int)(m_dblAlertScrollOffset * rowHeight);
	
	// 可能需要多绘制一行以实现平滑滚动效果
	int extraRowsNeeded = m_dblAlertScrollOffset > 0 ? 1 : 0;
	
	// 使用滚动位置
	for (int i = 0; i < maxRows + extraRowsNeeded; i++) {
		int dataIndex = i + m_nAlertScrollPos;
		if (dataIndex >= rowCount) {
			break; // 超出数据范围
		}
		
		int rowTop = headerBottom + i * rowHeight + pixelOffset;
		const AlertInfo& alert = m_vecAlerts[dataIndex];
		
		// 判断当前行是否被鼠标悬停
		bool isHovered = (i == hoverItem);
		
		// 只绘制在可见区域内的行
		if (rowTop + rowHeight > headerBottom && rowTop < rcAlert.bottom) {
			// 绘制行背景（交替行使用深色背景，悬停行使用高亮背景）
			if (isHovered) {
				pDC->FillSolidRect(CRect(rcAlert.left, rowTop, rcAlert.right, rowTop + rowHeight), RGB(80, 20, 20));
			} 
			else if (dataIndex % 2 == 1) {
				pDC->FillSolidRect(CRect(rcAlert.left, rowTop, rcAlert.right, rowTop + rowHeight), RGB(15, 15, 15));
			}
			
			// 设置文本颜色
			pDC->SetTextColor(isHovered ? RGB(255, 200, 200) : RGB(255, 255, 255));
			
			// 绘制股票代码（左对齐）
			CRect rcCode(col1Start, rowTop, col2Start - 10, rowTop + rowHeight);
			pDC->DrawText(alert.strCode, rcCode, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
			
			// 绘制股票名称（左对齐，增加左间距）
			CRect rcName(col2Start + 10, rowTop, col3Start - 10, rowTop + rowHeight);
			pDC->DrawText(alert.strName, rcName, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
			
			// 绘制预警原因（右对齐）
			CRect rcReason(col3Start, rowTop, rcAlert.right, rowTop + rowHeight);
			pDC->DrawText(alert.strReason, rcReason, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
		}
	}
	
	// 重置剪切区域
	pDC->SelectClipRgn(NULL);
	
	// 恢复原笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 防止闪烁
BOOL CTimeInfo::OnEraseBkgnd(CDC* pDC)
{
	// 在双缓冲绘图模式下，直接返回TRUE，不擦除背景
	// 这避免了双重擦除引起的闪烁
	return TRUE;
}

// 处理窗口大小变化
void CTimeInfo::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);
	
	// 强制重绘
	Invalidate();
}

// 处理定时器消息，用于预警区域滚动和平滑滚动
void CTimeInfo::OnTimer(UINT_PTR nIDEvent)
{
	if (nIDEvent == TIMER_ALERT_SCROLL) { // 预警自动滚动定时器
		// 设置预警区目标滚动位置
		m_nAlertTargetScrollPos = (m_nAlertTargetScrollPos + 1) % m_vecAlerts.size();
		// 开始平滑滚动动画
		StartSmoothScroll();
	}
	else if (nIDEvent == TIMER_SMOOTH_SCROLL) { // 平滑滚动动画定时器
		// 更新平滑滚动
		UpdateSmoothScroll();
	}
	
	CView::OnTimer(nIDEvent);
}

// 处理鼠标左键点击
void CTimeInfo::OnLButtonDown(UINT nFlags, CPoint point)
{
	// 检查点击是否在预警区域内的某个条目上
	int itemIndex = HitTestAlertItem(point);
	if (itemIndex != -1) {
		// 点击有效，获取实际数据索引
		int dataIndex = itemIndex + m_nAlertScrollPos;
		if (dataIndex < (int)m_vecAlerts.size()) {
			const AlertInfo& alert = m_vecAlerts[dataIndex];
			
			// 弹出消息框显示点击的股票信息
			CString message;
			message.Format(_T("您点击了股票：%s (%s)\n预警原因：%s"), 
						  alert.strName, alert.strCode, alert.strReason);
			MessageBox(message, _T("股票链接"), MB_ICONINFORMATION);
			
			// 这里可以添加跳转到详细股票页面的代码
			// 例如：GetDocument()->ViewStockDetail(alert.strCode);
		}
	}
	
	// 检查点击是否在行业概念区域内的某个条目上
	int industryIndex = HitTestIndustryItem(point);
	if (industryIndex != -1) {
		// 点击有效，获取实际数据索引
		int dataIndex = industryIndex + m_nIndustryScrollPos;
		if (dataIndex < (int)m_vecIndustryData.size()) {
			const IndustryData& data = m_vecIndustryData[dataIndex];
			
			// 弹出消息框显示点击的行业概念信息
			CString message;
			message.Format(_T("您点击了%s：%s\n当前涨幅：%s"), 
						  data.strType, data.strName, data.strChange);
			MessageBox(message, _T("行业概念链接"), MB_ICONINFORMATION);
			
			// 这里可以添加跳转到行业概念页面的代码
			// 例如：GetDocument()->ViewIndustryConcept(data.strType, data.strName);
		}
	}
	
	CView::OnLButtonDown(nFlags, point);
}

// 辅助函数 - 检测点是否在预警区域的特定行内
int CTimeInfo::HitTestAlertItem(CPoint point)
{
	// 计算预警区域位置
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 计算五档报价区域高度
	int priceInfoBottom = 70 + 11 * 28 + 10; // startY + 11*rowHeight + 间隔
	int level2RowHeight = 26; // 每行高度
	int level2Height = level2RowHeight * 11 + 10; // 11行加底部间隔
	
	// 五档报价区域底部位置
	int level2Bottom = priceInfoBottom + level2Height;
	
	// 行业概念区高度
	int remainingHeight = rectClient.Height() - level2Bottom;
	int industryHeight = remainingHeight / 2;
	int alertTop = level2Bottom + industryHeight;
	
	CRect rcAlert(rectClient.left + 10, alertTop, 
				rectClient.right - 10, rectClient.bottom - 10);
	
	// 判断点是否在预警区域内
	if (!rcAlert.PtInRect(point)) {
		return -1;
	}
	
	// 计算表头区域
	int headerHeight = 40; // 更新为与绘制函数一致的高度
	int protectionHeight = 5; // 保护区域高度
	int headerBottom = rcAlert.top + headerHeight + protectionHeight; // 表头 + 保护区域
	
	// 判断点是否在表头及保护区域以下
	if (point.y <= headerBottom) {
		return -1;
	}
	
	// 计算行高和点击的行
	int rowHeight = 30; // 更新为与绘制函数一致的行高
	
	// 考虑滚动偏移量
	int pixelOffset = -(int)(m_dblAlertScrollOffset * rowHeight);
	int rowOffset = (point.y - headerBottom - pixelOffset);
	int rowIndex = rowOffset / rowHeight;
	
	// 判断是否在有效范围内
	CRect rcContent(rcAlert.left, headerBottom, rcAlert.right, rcAlert.bottom);
	int maxRows = rcContent.Height() / rowHeight;
	if (rowIndex >= 0 && rowIndex < min(maxRows, (int)m_vecAlerts.size() - m_nAlertScrollPos)) {
		return rowIndex; // 返回视图中的行索引（不是数据索引）
	}
	
	return -1;
}

// 判断点是否在行业概念区域的特定行内，返回行索引，-1表示不在任何行内
int CTimeInfo::HitTestIndustryItem(CPoint point)
{
	// 计算行业概念区域位置
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 计算五档报价区域高度
	int priceInfoBottom = 70 + 11 * 28 + 10; // startY + 11*rowHeight + 间隔
	int level2RowHeight = 26; // 每行高度
	int level2Height = level2RowHeight * 11 + 10; // 11行加底部间隔
	
	// 五档报价区域底部位置
	int level2Bottom = priceInfoBottom + level2Height;
	
	// 行业概念区高度
	int remainingHeight = rectClient.Height() - level2Bottom;
	int industryHeight = remainingHeight / 2;
	
	CRect rcIndustry(rectClient.left + 10, level2Bottom, 
				   rectClient.right - 10, level2Bottom + industryHeight);
	
	// 判断点是否在行业概念区域内
	if (!rcIndustry.PtInRect(point)) {
		return -1;
	}
	
	// 计算表头区域
	int headerHeight = 35;
	int protectionHeight = 5; // 保护区域高度
	int headerBottom = rcIndustry.top + 5 + headerHeight + protectionHeight; // 包括分隔线和保护区域
	
	// 判断点是否在表头及保护区域以下
	if (point.y <= headerBottom) {
		return -1;
	}
	
	// 计算行高和点击的行
	int rowHeight = 26;
	
	// 考虑滚动偏移量
	int pixelOffset = -(int)(m_dblIndustryScrollOffset * rowHeight);
	int rowOffset = (point.y - headerBottom - pixelOffset);
	int rowIndex = rowOffset / rowHeight;
	
	// 判断是否在有效范围内
	CRect rcContent(rcIndustry.left, headerBottom, rcIndustry.right, rcIndustry.bottom);
	int maxRows = rcContent.Height() / rowHeight;
	if (rowIndex >= 0 && rowIndex < min(maxRows, (int)m_vecIndustryData.size() - m_nIndustryScrollPos)) {
		return rowIndex; // 返回视图中的行索引（不是数据索引）
	}
	
	return -1;
}

// 处理鼠标移动
void CTimeInfo::OnMouseMove(UINT nFlags, CPoint point)
{
	// 检查鼠标是否在预警区域内的某个条目上
	int alertItemIndex = HitTestAlertItem(point);
	int lastAlertItemIndex = HitTestAlertItem(m_ptLastMouse);
	
	// 检查鼠标是否在行业概念区域内的某个条目上
	int industryItemIndex = HitTestIndustryItem(point);
	int lastIndustryItemIndex = HitTestIndustryItem(m_ptLastMouse);
	
	// 只有当鼠标移动到不同的行或从非高亮区移入高亮区/从高亮区移出非高亮区时才重绘
	if (alertItemIndex != lastAlertItemIndex || industryItemIndex != lastIndustryItemIndex)
	{
		// 保存鼠标位置
		m_ptLastMouse = point;
		
		// 改变鼠标样式为手型光标（如果在链接上）
		if (alertItemIndex != -1 || industryItemIndex != -1)
		{
			::SetCursor(::LoadCursor(NULL, IDC_HAND));
		}
		else
		{
			::SetCursor(::LoadCursor(NULL, IDC_ARROW));
		}
		
		// 计算预警区域位置
		CRect rectClient;
		GetClientRect(&rectClient);
		
		int priceInfoBottom = 70 + 11 * 28 + 10;
		int level2RowHeight = 26;
		int level2Height = level2RowHeight * 11 + 10;
		int level2Bottom = priceInfoBottom + level2Height;
		int remainingHeight = rectClient.Height() - level2Bottom;
		int industryHeight = remainingHeight / 2;
		int alertTop = level2Bottom + industryHeight;
		
		// 如果预警区域的行状态发生变化，需要重绘
		if (lastAlertItemIndex != alertItemIndex)
		{
			// 仅重绘受影响的行，而不是整个预警区域
			if (lastAlertItemIndex != -1 || alertItemIndex != -1)
			{
				CRect rcAlert(rectClient.left + 10, alertTop, 
							rectClient.right - 10, rectClient.bottom - 10);
				
				// 计算表头高度
				int headerHeight = 35;
				int headerBottom = rcAlert.top + 5 + headerHeight;
				int rowHeight = 26;
				
				// 如果之前有高亮行，需要重绘该行
				if (lastAlertItemIndex != -1)
				{
					int lastRowTop = headerBottom + 5 + lastAlertItemIndex * rowHeight;
					CRect rcLastRow(rcAlert.left, lastRowTop, rcAlert.right, lastRowTop + rowHeight);
					InvalidateRect(rcLastRow, FALSE); // 不擦除背景，减少闪烁
				}
				
				// 如果当前有新的高亮行，需要重绘该行
				if (alertItemIndex != -1)
				{
					int rowTop = headerBottom + 5 + alertItemIndex * rowHeight;
					CRect rcCurrentRow(rcAlert.left, rowTop, rcAlert.right, rowTop + rowHeight);
					InvalidateRect(rcCurrentRow, FALSE); // 不擦除背景，减少闪烁
				}
			}
		}
		
		// 如果行业概念区域的行状态发生变化，需要重绘
		if (lastIndustryItemIndex != industryItemIndex)
		{
			// 仅重绘受影响的行，而不是整个行业概念区域
			if (lastIndustryItemIndex != -1 || industryItemIndex != -1)
			{
				CRect rcIndustry(rectClient.left + 10, level2Bottom, 
							   rectClient.right - 10, level2Bottom + industryHeight);
				
				// 计算表头高度
				int headerHeight = 35;
				int headerBottom = rcIndustry.top + 5 + headerHeight + 2; // 加上分隔线高度
				int rowHeight = 26;
				
				// 如果之前有高亮行，需要重绘该行
				if (lastIndustryItemIndex != -1)
				{
					int lastRowTop = headerBottom + 5 + lastIndustryItemIndex * rowHeight;
					CRect rcLastRow(rcIndustry.left, lastRowTop, rcIndustry.right, lastRowTop + rowHeight);
					InvalidateRect(rcLastRow, FALSE); // 不擦除背景，减少闪烁
				}
				
				// 如果当前有新的高亮行，需要重绘该行
				if (industryItemIndex != -1)
				{
					int rowTop = headerBottom + 5 + industryItemIndex * rowHeight;
					CRect rcCurrentRow(rcIndustry.left, rowTop, rcIndustry.right, rowTop + rowHeight);
					InvalidateRect(rcCurrentRow, FALSE); // 不擦除背景，减少闪烁
				}
			}
		}
	}

	CView::OnMouseMove(nFlags, point);
}

// 处理鼠标滚轮消息
BOOL CTimeInfo::OnMouseWheel(UINT nFlags, short zDelta, CPoint pt)
{
	// 将屏幕坐标转换为客户区坐标
	CPoint point = pt;
	ScreenToClient(&point);
	
	// 计算预警区域位置
	CRect rectClient;
	GetClientRect(&rectClient);
	
	int priceInfoBottom = 70 + 11 * 28 + 10;
	int level2RowHeight = 26;
	int level2Height = level2RowHeight * 11 + 10;
	int level2Bottom = priceInfoBottom + level2Height;
	int remainingHeight = rectClient.Height() - level2Bottom;
	int industryHeight = remainingHeight / 2;
	int alertTop = level2Bottom + industryHeight;
	
	CRect rcAlert(rectClient.left + 10, alertTop, 
				rectClient.right - 10, rectClient.bottom - 10);
	
	CRect rcIndustry(rectClient.left + 10, level2Bottom, 
				   rectClient.right - 10, level2Bottom + industryHeight);
	
	// 计算滚动方向和数量
	int scrollLines = zDelta > 0 ? -1 : 1; // 向上滚轮为负，向下滚轮为正
	
	// 判断鼠标在哪个区域
	if (rcIndustry.PtInRect(point)) {
		// 行业概念区域
		int rowCount = (int)m_vecIndustryData.size();
		int maxRows = (rcIndustry.Height() - 50) / 26; // 粗略计算可见行数
		
		// 计算新的目标滚动位置
		int newPos = m_nIndustryTargetScrollPos + scrollLines;
		
		// 确保滚动位置在有效范围内
		if (newPos < 0) {
			newPos = 0;
		}
		else if (newPos > rowCount - maxRows && rowCount > maxRows) {
			newPos = rowCount - maxRows;
		}
		
		// 如果有变化才更新
		if (newPos != m_nIndustryTargetScrollPos) {
			m_nIndustryTargetScrollPos = newPos;
			StartSmoothScroll();
		}
		
		return TRUE; // 已处理消息
	}
	else if (rcAlert.PtInRect(point)) {
		// 预警区域
		int rowCount = (int)m_vecAlerts.size();
		int maxRows = (rcAlert.Height() - 50) / 30; // 调整行高为30，匹配绘制函数中的行高
		
		// 计算新的目标滚动位置
		int newPos = m_nAlertTargetScrollPos + scrollLines;
		
		// 确保滚动位置在有效范围内
		if (newPos < 0) {
			newPos = 0;
		}
		else if (newPos > rowCount - maxRows && rowCount > maxRows) {
			newPos = rowCount - maxRows;
		}
		
		// 如果有变化才更新
		if (newPos != m_nAlertTargetScrollPos) {
			m_nAlertTargetScrollPos = newPos;
			StartSmoothScroll();
		}
		
		return TRUE; // 已处理消息
	}
	
	return CView::OnMouseWheel(nFlags, zDelta, pt);
}

// 开始平滑滚动动画
void CTimeInfo::StartSmoothScroll()
{
	// 这里不需要特别的初始化，滚动更新会在TIMER_SMOOTH_SCROLL定时器中进行
	// 仅需确保定时器已启动
	Invalidate();
}

// 更新平滑滚动
void CTimeInfo::UpdateSmoothScroll()
{
	bool needRedraw = false;
	
	// 更新行业概念区域滚动
	if (m_nIndustryScrollPos != m_nIndustryTargetScrollPos) {
		// 平滑过渡到目标位置
		double diff = m_nIndustryTargetScrollPos - m_nIndustryScrollPos - m_dblIndustryScrollOffset;
		m_dblIndustryScrollOffset += diff * 0.25; // 使用0.25的系数实现平滑效果
		
		// 如果偏移量足够接近整数，则完成滚动
		if (fabs(m_dblIndustryScrollOffset) >= 0.95) {
			int intOffset = (int)(m_dblIndustryScrollOffset + (m_dblIndustryScrollOffset > 0 ? 0.5 : -0.5));
			m_nIndustryScrollPos += intOffset;
			m_dblIndustryScrollOffset -= intOffset;
			
			// 确保滚动位置在有效范围内
			int rowCount = (int)m_vecIndustryData.size();
			if (m_nIndustryScrollPos < 0) {
				m_nIndustryScrollPos = 0;
				m_nIndustryTargetScrollPos = 0;
				m_dblIndustryScrollOffset = 0;
			}
			else if (m_nIndustryScrollPos > rowCount) {
				m_nIndustryScrollPos = rowCount;
				m_nIndustryTargetScrollPos = rowCount;
				m_dblIndustryScrollOffset = 0;
			}
		}
		
		needRedraw = true;
	}
	
	// 更新预警区域滚动
	if (m_nAlertScrollPos != m_nAlertTargetScrollPos) {
		// 平滑过渡到目标位置
		double diff = m_nAlertTargetScrollPos - m_nAlertScrollPos - m_dblAlertScrollOffset;
		m_dblAlertScrollOffset += diff * 0.25; // 使用0.25的系数实现平滑效果
		
		// 如果偏移量足够接近整数，则完成滚动
		if (fabs(m_dblAlertScrollOffset) >= 0.95) {
			int intOffset = (int)(m_dblAlertScrollOffset + (m_dblAlertScrollOffset > 0 ? 0.5 : -0.5));
			m_nAlertScrollPos += intOffset;
			m_dblAlertScrollOffset -= intOffset;
			
			// 确保滚动位置在有效范围内
			int rowCount = (int)m_vecAlerts.size();
			if (m_nAlertScrollPos < 0) {
				m_nAlertScrollPos = 0;
				m_nAlertTargetScrollPos = 0;
				m_dblAlertScrollOffset = 0;
			}
			else if (m_nAlertScrollPos > rowCount) {
				m_nAlertScrollPos = rowCount;
				m_nAlertTargetScrollPos = rowCount;
				m_dblAlertScrollOffset = 0;
			}
		}
		
		needRedraw = true;
	}
	
	// 如果需要重绘，则刷新视图
	if (needRedraw) {
		Invalidate(FALSE); // 不擦除背景，减少闪烁
	}
}

// 处理文档更新
void CTimeInfo::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 调用基类的OnUpdate方法
	CView::OnUpdate(pSender, lHint, pHint);
	
	// 获取当前文档
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;
	
	// 获取当前股票代码
	std::string currentStock = pDoc->GetCurrentStock();
	CString strCurrentCode(currentStock.c_str());
	
	// 如果股票代码非空且与当前显示的不同，则更新股票信息
	if (!strCurrentCode.IsEmpty() && strCurrentCode != m_strCode)
	{
		// 获取股票名称
		CString strName = pDoc->GetStockName(currentStock).c_str();
		
		// 更新股票代码和名称
		m_strCode = strCurrentCode;
		m_strName = strName;
		
		// 添加调试信息
		TRACE(_T("CTimeInfo::OnUpdate - 股票变更为: %s (%s)\n"), m_strCode, m_strName);
		
		// 重绘视图
		Invalidate();
	}
	
	// 更新当前股票的Level2数据
	if (!m_strCode.IsEmpty())
	{
		int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
		if (stockIndex >= 0)
		{
			const StockData* pStockData = pDoc->GetStock(stockIndex);
			if (pStockData)
			{
				// 获取五档报价数据
				m_level2Data = pStockData->_Level2;
				
				// 获取其他基本价格数据
				m_dblPrice = pStockData->_Close;
				m_dblPreClose = pStockData->_preClose;
				m_dblOpen = pStockData->_Open;
				m_dblVolume = pStockData->_Volume;
				m_dblAmount = pStockData->_Amount;
				
				// 计算涨跌额和涨跌幅
				if (m_dblPreClose > 0)
				{
					m_dblChange = m_dblPrice - m_dblPreClose;
					m_dblChangePercent = (m_dblPrice / m_dblPreClose - 1.0) * 100.0;
				}
				else
				{
					m_dblChange = 0.0;
					m_dblChangePercent = 0.0;
				}
				
				// 获取当前时间
				m_timeCurrent = CTime::GetCurrentTime();
			}
		}
	}
} 