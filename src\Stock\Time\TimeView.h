﻿// TimeView.h: CTimeView 类的接口
//

#pragma once
#include "TimeLine.h"
#include "TimeInfo.h"
#include "..\StockSplitter.h"  // 添加分割窗口头文件

class CStockDoc;

class CTimeView : public CView
{
protected: // 仅从序列化创建
	CTimeView() noexcept;
	DECLARE_DYNCREATE(CTimeView)

// 特性
public:
	CStockDoc* GetDocument() const;

// 操作
public:
	// 设置股票代码
	void SetStockCode(const std::string& strCode);
	
	// 获取分时线视图指针
	CTimeLine* GetTimeLineView();

// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnInitialUpdate();
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint);

// 实现
public:
	virtual ~CTimeView();

	// 分割窗口
	CStockSplitter m_wndSplitter;  // 分割窗口

	// 子视图ID
	int m_nTimeLineViewID;  // 分时线视图ID
	int m_nTimeInfoViewID;  // 信息视图ID

protected:
	// 初始化分割窗口
	BOOL CreateSplitterWindow();

	//// 时间点数据结构
	//struct TimePointData
	//{
	//	CTime time;       // 时间
	//	double price;     // 价格
	//	double avgPrice;  // 均价
	//	double volume;    // 成交量
	//};
	//
	//// 分时数据
	//CArray<TimePointData, TimePointData&> m_timeData;
	//
	//// 股票基本信息
	//CString m_strCode;  // 股票代码
	//CString m_strName;  // 股票名称
	//double m_dblPreClose; // 昨收价
	//double m_dblOpen;   // 开盘价
	//double m_dblHigh;   // 最高价
	//double m_dblLow;    // 最低价
	//double m_dblVolume; // 总成交量
	//
	//// 图表参数
	//CRect m_rcPrice;    // 价格图区域
	//CRect m_rcVolume;   // 成交量区域
	//int m_nDataCount;   // 数据点数量
	//double m_dblMaxPrice; // 最高价格
	//double m_dblMinPrice; // 最低价格
	//double m_dblMaxVolume; // 最大成交量
	//
	//// 绘制函数
	//void DrawPriceChart(CDC* pDC);
	//void DrawVolumeChart(CDC* pDC);
	//void DrawGrid(CDC* pDC);
	//void DrawTimeLine(CDC* pDC);
	//void DrawStockInfo(CDC* pDC);
	//void DrawCrossCursor(CDC* pDC, CPoint point);
	//
	//// 生成模拟数据
	//void GenerateSampleData();
	//
	//// 计算最大最小值
	//void CalculateMaxMin();

// 生成的消息映射函数
protected:
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
	DECLARE_MESSAGE_MAP()
	
private:
	// 鼠标十字光标相关
	bool m_bShowCrossCursor;  // 是否显示十字光标
	CPoint m_ptCrossCursor;   // 十字光标位置
};
