/*************************************************************************
				类声明 : CUGCtrl
**************************************************************************
	源文件 : UGCtrl.cpp
	头文件 : UGCtrl.h
// 本软件及其相关组件、文档和文件（"库"）的版权归属于
// ?1994-2007 The Code Project (1612916 Ontario Limited)，使用本库
// 受软件许可协议（"协议"）的约束。协议副本可从
// The Code Project (www.codeproject.com) 获取，作为您下载
// 此文件的包的一部分，或直接从我们的办公室获取。
// 关于管理本软件的许可证副本，您可以通过以下方式联系我们：
// <EMAIL>，或致电 416-849-8900。

	目的
		这是网格控件的主类。它包含并控制所有构成
		Ultimate Grid for MFC的窗口和类。

		这个类非常重要，是使用Ultimate Grid进行
		开发时首先会遇到的类。
		
	细节
		- 通常在应用程序中使用从此类派生的类。
		  派生类是必要的，以便处理网格发送的任何通知。
		- 从CWnd派生允许网格可以在任何可以使用CWnd的地方使用。
		- 当使用CreateGrid或AttachGrid创建此类时，它还会：
			- 创建所有网格的子窗口
			  (CUGGrid, CUGTopHdg, CUGSideHdg等)
			- 创建默认的内存管理器数据源
			  并注册网格的标准单元格类型
*************************************************************************/
#ifndef _UGCtrl_H_
#define _UGCtrl_H_

// 网格功能启用定义 - 注释掉这些定义中的一个或多个
// 以移除一个或多个功能
#define UG_ENABLE_MOUSEWHEEL     // 启用鼠标滚轮支持
#define UG_ENABLE_PRINTING       // 启用打印功能
#define UG_ENABLE_FINDDIALOG     // 启用查找对话框
#define UG_ENABLE_SCROLLHINTS    // 启用滚动提示

#ifdef __AFXOLE_H__  // 必须包含OLE
	#define UG_ENABLE_DRAGDROP   // 启用拖放功能
#endif

#ifndef WS_EX_LAYOUTRTL
	#define WS_EX_LAYOUTRTL		0x00400000L
#endif // WS_EX_LAYOUTRTL

// 标准网格类前向声明
class CUGGridInfo;         // 网格信息类
class CUGGrid;             // 网格主体类
class CUGTopHdg;           // 顶部表头类
class CUGSideHdg;          // 侧边表头类
class CUGCnrBtn;           // 角落按钮类
class CUGVScroll;          // 垂直滚动条类
class CUGHScroll;          // 水平滚动条类
class CUGCtrl;             // 网格控制类(主类)
class CUGCell;             // 单元格类
class CUGCellType;         // 单元格类型类
class CUGEdit;             // 编辑控件类
class CUGCellFormat;       // 单元格格式类

// 标准网格头文件包含
#include "UGDefine.h"      // 定义和常量
#include "UGStruct.h"      // 结构定义
#include "UGPtrLst.h"      // 指针列表类
#include "UGDrwHnt.h"      // 绘制提示类
#include "UGCell.h"        // 单元格类
#include "UGCnrBtn.h"      // 角落按钮类
#include "UGGrid.h"        // 网格主体类
#include "UGHScrol.h"      // 水平滚动条类
#include "UGSideHd.h"      // 侧边表头类
#include "UGTopHdg.h"      // 顶部表头类
#include "UGVScrol.h"      // 垂直滚动条类
#include "UGDtaSrc.h"      // 数据源类
#include "UGMemMan.h"      // 内存管理类
#include "UGCelTyp.h"      // 单元格类型类
#include "UGMultiS.h"      // 多选类
#include "UGEditBase.h"    // 编辑基类
#include "UGEdit.h"        // 编辑类
#include "UGMEdit.h"       // 掩码编辑类
#include "UGTab.h"         // 标签类
#include "UGGdInfo.h"      // 网格信息类
#include "UGFormat.h"      // 格式类
#include "UGXPThemes.h"    // XP主题支持
#ifdef UG_ENABLE_SCROLLHINTS
	#include "ughint.h"    // 滚动提示类
#endif 
#ifdef UG_ENABLE_PRINTING
	#include "UGPrint.h"   // 打印类
#endif


// 标准单元格类型
#include "ugLstBox.h"      // 列表框单元格类型
#include "ugdltype.h"      // 下拉列表单元格类型
#include "ugcbtype.h"      // 复选框单元格类型
#include "ugctarrw.h"      // 箭头单元格类型

#ifdef UG_ENABLE_DRAGDROP
	#include "UGDrgDrp.h"  // 拖放支持
#endif

/////////////////////////////////////////////////
// CUGCtrl类 - 网格控件的主类
class UG_CLASS_DECL CUGCtrl : public CWnd
{
// 构造
public:
	CUGCtrl();


// 属性
public:

// 操作
public:

// 重写
	// ClassWizard生成的虚函数重写
	//{{AFX_VIRTUAL(CUGCtrl)
	protected:
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);  // 创建窗口前的预处理
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);  // 处理命令消息
	public:
	virtual CScrollBar* GetScrollBarCtrl(int nBar) const;  // 获取滚动条控件
	//}}AFX_VIRTUAL

// 实现
public:
	virtual ~CUGCtrl();  // 虚析构函数

	// 生成的消息映射函数
protected:
	//{{AFX_MSG(CUGCtrl)
	afx_msg void OnSize(UINT nType, int cx, int cy);  // 大小变化消息处理
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);  // 创建消息处理
	afx_msg void OnDestroy();  // 销毁消息处理
	afx_msg void OnPaint();  // 绘制消息处理
	afx_msg void OnSetFocus(CWnd* pOldWnd);  // 获取焦点消息处理
	afx_msg void OnSysColorChange();  // 系统颜色变化消息处理
	afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);  // 水平滚动消息处理
	afx_msg void OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);  // 垂直滚动消息处理
	afx_msg UINT OnGetDlgCode();  // 获取对话框代码消息处理
	afx_msg LRESULT OnCellTypeMessage(WPARAM, LPARAM);  // 单元格类型消息处理
	afx_msg BOOL OnEraseBkgnd( CDC* pDC );  // 擦除背景消息处理
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

public:
	// 设置初始单元格状态
	void SetInitialCellStates();

	// 构造函数结果状态
	int m_contructorResults;

	// 用于重置网格的方法
	void ResetCells(int startRow, int endRow, int startCol, int endCol);  // 重置指定范围的单元格
	void ResetAll(bool allSheets = true);  // 重置所有单元格
	void ResetSizes(int startRow, int endRow, int startCol, int endCol);  // 重置指定范围的大小

protected:
	// 设置是否使用默认状态存储
	void UseDefaultStateStorage(bool use) { m_storeInitialStates = use; }

private:
	// 存储初始行和列大小的方法
	void SetInitialSizes();
	// 是否存储初始状态的标志
	bool m_storeInitialStates;

protected:

	//***** 内部类 *****
	
	// 数据源列表
	CUGDataSource		**m_dataSrcList;         // 数据源指针数组
	int					m_dataSrcListLength;     // 数据源数组长度
	int					m_dataSrcListMaxLength;  // 数据源数组最大长度

	CUGPtrList			*m_fontList;             // 字体列表
	CUGPtrList			*m_bitmapList;           // 位图列表
	CUGPtrList			*m_cellTypeList;         // 单元格类型列表
	CUGPtrList			*m_cellStylesList;       // 单元格样式列表
	CUGPtrList			*m_validateList;         // 验证器列表
	CUGPtrList			*m_displayFormatList;    // 显示格式列表

	// 标准单元格类型
	CUGCellType			m_normalCellType;        // 普通单元格类型
	CUGDropListType		m_dropListType;          // 下拉列表单元格类型
	CUGCheckBoxType		m_checkBoxType;          // 复选框单元格类型
	CUGArrowType		m_arrowType;             // 箭头单元格类型


	#ifdef UG_ENABLE_PRINTING
	CUGPrint*			m_CUGPrint;              // 打印支持对象
	#endif

	// 弹出菜单
	CMenu				*m_menu;
	
	// 窗口组件
	CUGGrid				*m_CUGGrid;              // 网格主窗口
	CUGTopHdg			*m_CUGTopHdg;            // 顶部表头窗口
	CUGSideHdg			*m_CUGSideHdg;           // 侧边表头窗口
	CUGVScroll			*m_CUGVScroll;           // 垂直滚动条窗口
	CUGHScroll			*m_CUGHScroll;           // 水平滚动条窗口
	CUGCnrBtn			*m_CUGCnrBtn;            // 角落按钮窗口
	
	#ifdef UG_ENABLE_SCROLLHINTS
	CUGHint				*m_CUGHint;              // 滚动提示窗口
	#endif
	
	CUGTab				*m_CUGTab;               // 标签控件
	
	CWnd				*m_trackingWnd;          // 跟踪窗口

	// 网格信息对象
	CUGGridInfo			**m_GIList;              // 网格信息列表
	CUGGridInfo			*m_GI;                   // 当前网格信息指针
	int					m_numberSheets;          // 工作表数量
	int					m_currentSheet;          // 当前工作表索引

	// 网格设置
	int					m_GI_timeout;            // 网格信息超时设置
	RECT				m_gridRect;              // 网格矩形区域
	int					m_enableUpdate;          // 更新启用标志
	int					m_tabSizing;             // 标签调整大小标志

	// 多选相关
	CUGMultiSelect		*m_multiSelect;          // 多选对象
	int					m_findInAllCols;         // 在所有列中查找标志

	// 拖放相关
	#ifdef UG_ENABLE_DRAGDROP
	CUGDropTarget		m_dropTarget;            // 拖放目标对象
	#endif

	// 编辑相关
	BOOL				m_editInProgress;        // 编辑进行中标志
	BOOL				m_bCancelMode;           // 取消模式标志
	CWnd				*m_editCtrl;             // 当前编辑控件
	CUGEdit				m_defEditCtrl;           // 默认编辑控件
	CUGMaskedEdit		m_defMaskedEditCtrl;     // 默认掩码编辑控件

	// 查找对话框
	#ifdef UG_ENABLE_FINDDIALOG
	CFindReplaceDialog	*m_findReplaceDialog;    // 查找替换对话框
	BOOL				m_findDialogRunning;     // 对话框运行中标志
	BOOL				m_findDialogStarted;     // 对话框已启动标志
	#endif
	
	// 绘制样式设置
	HCURSOR				m_arrowCursor;          // 箭头光标
	HCURSOR				m_WEReSizeCursor;       // 左右调整大小光标
	HCURSOR				m_NSReSizeCursor;       // 上下调整大小光标
	CPen				m_threeDLightPen;        // 3D浅色画笔
	CPen				m_threeDDarkPen;         // 3D深色画笔

public:
	// 内部计算和实用函数
	void CalcTopRow();          // 计算最大顶行然后根据需要调整顶行
	void CalcLeftCol();         // 计算最大左列然后根据需要调整左列
	void AdjustTopRow();        // 移动顶行使当前行可见
	void AdjustLeftCol();       // 移动左列使当前列可见
	void Update();              // 更新所有窗口并执行重新计算
	void Moved();               // 当网格移动时调用此函数

	// 创建和布局
	BOOL CreateChildWindows();  // 创建子窗口
	void ToggleLayout(CWnd *pWnd);  // 切换布局方向
	
	void SetLockRowHeight();    // 设置锁定行高度
	void SetLockColWidth();     // 设置锁定列宽度
	
	void MoveTrackingWindow();  // 移动跟踪窗口
	
	// 验证函数
	int VerifyTopRow(long* newRow);      // 验证顶行
	int VerifyCurrentRow(long* newRow);  // 验证当前行
	int VerifyLeftCol(int* newCol);      // 验证左列
	int VerifyCurrentCol(int* newCol);   // 验证当前列

	// 网格创建和附加
	BOOL CreateGrid(DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID);  // 创建网格
	
	BOOL AttachGrid(CWnd * wnd, UINT ID);  // 将网格附加到现有窗口
	
	BOOL DetachGrid();  // 分离网格
	
	// 调整组件大小和位置
	void AdjustComponentSizes();  // 调整组件大小和位置
	
	// 编辑相关函数
	int StartEdit();                     // 开始编辑当前单元格
	int StartEdit(int key);              // 以指定键开始编辑当前单元格
	int StartEdit(int col, long row, int key);  // 开始编辑指定单元格
	int ContinueEdit(int adjustCol, long adjustRow);  // 继续编辑到另一单元格
	
	// 行列相关函数
	int SetNumberRows(long rows, BOOL redraw = TRUE);  // 设置行数
	long GetNumberRows();                            // 获取行数
	
	int SetNumberCols(int cols, BOOL redraw = TRUE);  // 设置列数
	int GetNumberCols();                             // 获取列数
	
	int SetDefColWidth(int width);                        // 设置默认列宽
	int SetColWidth(int col, int width);                  // 设置指定列宽
	int GetColWidth(int col, int *width);                 // 获取指定列宽
	int GetColWidth(int col);                             // 获取指定列宽(直接返回值)
	
	int SetDefRowHeight(int height);                      // 设置默认行高
	int SetUniformRowHeight(int flag);                    // 设置统一行高
	int SetRowHeight(long row, int height);               // 设置指定行高
	int GetRowHeight(long row, int *height);              // 获取指定行高
	int GetRowHeight(long row);                           // 获取指定行高(直接返回值)
	
	int GetCurrentCol();                                  // 获取当前列
	long GetCurrentRow();                                 // 获取当前行
	int GetLeftCol();                                     // 获取左列
	long GetTopRow();                                     // 获取顶行
	int GetRightCol();                                    // 获取右列
	long GetBottomRow();                                  // 获取底行
	
	int InsertCol(int col);                               // 插入列
	int AppendCol();                                      // 追加列
	int DeleteCol(int col);                               // 删除列
	int InsertRow(long row);                              // 插入行
	int AppendRow();                                      // 追加行
	int DeleteRow(long row);                              // 删除行

	// 表头设置函数
	int SetTH_NumberRows(int rows);                     // 设置顶部表头行数
	int SetTH_RowHeight(int row, int height);           // 设置顶部表头行高
	int SetSH_NumberCols(int cols);                     // 设置侧边表头列数
	int SetSH_ColWidth(int col, int width);             // 设置侧边表头列宽

	// 查找和替换
	int FindFirst(CString *string, int *col, long *row, long flags);  // 首次查找
	int FindNext(CString *string, int *col, long *row, int flags);    // 下一个查找
	
	int FindDialog();                                                 // 显示查找对话框
	int ReplaceDialog();                                              // 显示替换对话框
	
	LRESULT ProcessFindDialog(WPARAM, LPARAM);                        // 处理查找对话框消息
	int FindInAllCols(BOOL state);                                    // 设置是否在所有列中查找

	// 排序相关
	int SortBy(int col, int flag = UG_SORT_ASCENDING);                // 按列排序
	int SortBy(int *cols, int num, int flag = UG_SORT_ASCENDING);     // 按多列排序

	// 表头尺寸设置
	int SetTH_Height(int height);                                     // 设置顶部表头高度
	int GetTH_Height();                                               // 获取顶部表头高度
	int SetSH_Width(int width);                                       // 设置侧边表头宽度
	int GetSH_Width();                                                // 获取侧边表头宽度
	int SetVS_Width(int width);                                       // 设置垂直滚动条宽度
	int GetVS_Width();                                                // 获取垂直滚动条宽度
	int SetHS_Height(int height);                                     // 设置水平滚动条高度
	int GetHS_Height();                                               // 获取水平滚动条高度
}

} 