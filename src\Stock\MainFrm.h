﻿// MainFrm.h: CMainFrame 类的接口
//

#pragma once
#include "StockSplitter.h"
#include "TabPage.h"
#include "StockStatusBar.h"  // 添加自定义状态栏头文件
#include "Symbol/SymbolGrid.h" // 添加网格控件头文件

// 前向声明
class CLoadingDlg;
class CSymbolView;

class CMainFrame : public CFrameWnd
{
	
protected: // 仅从序列化创建
	CMainFrame() noexcept;
	DECLARE_DYNCREATE(CMainFrame)

// 特性
public:
	int					m_nStockListViewID;
	int					m_nTimeLineViewID;
	int					m_nKLineViewID;

// 操作
public:
	void			SwitchView(int ViewID);
	
	// 更新大盘指数数据
	void UpdateMarketIndex(const MarketIndexData& indexData);
	
	// 更新市场统计数据
	void UpdateMarketStats(const MarketStatData& statsData);
	
	// 获取网格控件
	CSymbolGrid* GetSymbolGrid();
	
	// 获取视图ID
	int GetStockListViewID() const { return m_nStockListViewID; }
	int GetTimeLineViewID() const { return m_nTimeLineViewID; }
	int GetKLineViewID() const { return m_nKLineViewID; }
	
	// 检查视图是否已初始化完成
	bool IsViewInitialized() const { return m_bViewInitialized; }

// 重写
public:
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual BOOL OnCreateClient(LPCREATESTRUCT lpcs, CCreateContext* pContext);

// 实现
public:
	virtual ~CMainFrame();
#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

protected:  // 控件条嵌入成员
	CStockStatusBar		m_wndStatusBar;  // 使用自定义状态栏
	CTabPage			m_wndTabPage;        // 自定义选项卡控件
	CStockSplitter		m_wndSplitter;       // 主分割窗口
	
	bool                m_bViewInitialized;  // 视图初始化完成标志

// 生成的消息映射函数
protected:
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnSize(UINT nType, int cx, int cy);  // 添加大小变化处理
	afx_msg void OnUpdateViewShowInfoArea(CCmdUI* pCmdUI);
	afx_msg void OnViewShowInfoArea();
	afx_msg void OnUpdateViewShowAuctionArea(CCmdUI* pCmdUI);
	afx_msg void OnViewShowAuctionArea();
	afx_msg void OnUpdateViewShowVolumeArea(CCmdUI* pCmdUI);
	afx_msg void OnViewShowVolumeArea();
	afx_msg void OnAppRefresh();  // 添加刷新处理函数


	DECLARE_MESSAGE_MAP()
public:
	afx_msg void OnDownStockList();
	afx_msg void OnUpdateStockList();
};


