﻿/////////////////////////////////////////////////////////////////////////////
//	Skeleton Class for a Derived CSymbolGrid

#include "pch.h"
#include "..\Stock.h"
#include "..\StockDoc.h"
#include "..\StockDef.h"
#include "..\MainFrm.h"
#include "UGStrOp.h"
#include "SymbolGrid.h"
#include "..\ColumnConfigDlg.h"
#include "..\UIConstants.h"

// 定义表格边框和网格线常量
#define UG_BDR_NONE 0
#define UG_GRID_NONE 0



BEGIN_MESSAGE_MAP(CSymbolGrid, CUGCtrl)
	ON_WM_SIZE()
	ON_WM_TIMER()
	ON_COMMAND(ID_COLUMN_CONFIG, OnColumnConfig)
	ON_COMMAND(ID_RECENT_STRONG, OnRecentStrong)
	//ON_MESSAGE(WM_USER_DATA_UPDATED, OnNotifyStockUpdate)
END_MESSAGE_MAP()


/////////////////////////////////////////////////////////////////////////////
// Standard CSymbolGrid construction/destruction
CSymbolGrid::CSymbolGrid()
{
	UGXPThemes::UseThemes(FALSE);
	
	// 设置全局指针指向当前实例
	theApp.g_pSymbolGrid = this;
	
	// 初始化缓存变量
	m_cachedMarketType = -1;
	m_bCacheValid = false;
	m_nCurrentRow = -1;
	m_nCurrentColCount = NUM_COLS_DEFAULT;
	
	// 初始化列配置
	memset(m_colInfos, 0, sizeof(m_colInfos));
	memset(m_defaultColInfos, 0, sizeof(m_defaultColInfos));
	
	// 初始化用户自定义列配置标志
	m_bUsingCustomConfig = false;
	
	
	// 初始化防闪烁相关变量
	m_bAntiFlickerEnabled = true;
	m_lastUpdateTime = 0;

}

CSymbolGrid::~CSymbolGrid()
{
	// 保存当前列配置到ColConfig.dat
	if (m_bUsingCustomConfig)
	{
		SaveColumnConfig("");
	}
	
	// 清除全局指针
	if (theApp.g_pSymbolGrid == this)
	{
		theApp.g_pSymbolGrid = NULL;
	}
	
	UGXPThemes::CleanUp();
}


// 通知更新个股数据
LRESULT CSymbolGrid::OnNotifyStockUpdate(WPARAM wparam, LPARAM lparam)
{
	// 获取当前可视区域的行范围
	long topRow = GetTopRow();
	long bottomRow = GetBottomRow();
	
	// 如果底部行无效（例如为0），则计算可视区域的底部行
	if (bottomRow <= 0)
	{
		// 获取表格的高度并减去锁定行的高度
		int gridHeight = m_GI->m_gridHeight - m_GI->m_lockRowHeight;
		long row = topRow;
		int height = 0;
		
		// 累加行高直到超出可视区域
		while (height < gridHeight && row < GetNumberRows())
		{
			height += GetRowHeight(row);
			row++;
		}
		
		// 设置底部可视行（取最后一个完全可见的行）
		bottomRow = row - 1;
	}
	
	// 确保行范围有效
	if (topRow < 0) topRow = 0;
	if (bottomRow >= GetNumberRows()) bottomRow = GetNumberRows() - 1;
	if (bottomRow < topRow) bottomRow = topRow;
	
	TRACE("更新可视行范围：%ld 到 %ld\n", topRow, bottomRow);
	
	// 使缓存失效以确保数据会被重新加载
	m_bCacheValid = false;
	
	// 暂时禁用窗口更新以减少闪烁
	EnableUpdate(FALSE);
	
	// 只更新可视区域的行
	for (long row = topRow; row <= bottomRow; row++)
	{
		// 重绘该行的所有列
		for (int col = 0; col < GetNumberCols(); col++)
		{
			RedrawCell(col, row);
		}
	}
	
	// 重新启用窗口更新并刷新显示
	EnableUpdate(TRUE);
	
	return 0;
}

// 设置选项卡高度
void CSymbolGrid::SetTabHeight(int height)
{
	if (m_CUGTab != NULL && ::IsWindow(m_CUGTab->GetSafeHwnd()))
	{
		// 获取客户区大小
		CRect rect;
		m_CUGTab->GetClientRect(&rect);
		
		// 设置Tab控件位置和大小
		m_CUGTab->MoveWindow(rect.left, rect.top, rect.Width(), height, TRUE);
		m_CUGTab->ShowWindow(SW_SHOW);
		
		// 需要重新调整布局
		AdjustComponentSizes();
	}
}

// 设置选项卡前景色
bool CSymbolGrid::SetTabTextColor(int tabID, COLORREF color)
{
	if (m_CUGTab != NULL && ::IsWindow(m_CUGTab->GetSafeHwnd()))
	{
		// 调用Tab控件的设置文本颜色方法
		int result = m_CUGTab->SetTabTextColor(tabID, color);
		m_CUGTab->Invalidate(); // 刷新显示
		return (result == UG_SUCCESS);
	}
	return false;
}

// 设置选项卡背景色
bool CSymbolGrid::SetTabBackColor(int tabID, COLORREF color)
{
	if (m_CUGTab != NULL && ::IsWindow(m_CUGTab->GetSafeHwnd()))
	{
		// 调用Tab控件的设置背景颜色方法
		int result = m_CUGTab->SetTabBackColor(tabID, color);
		m_CUGTab->Invalidate(); // 刷新显示
		return (result == UG_SUCCESS);
	}
	return false;
}

// 设置选项卡选中时的前景色
bool CSymbolGrid::SetTabHTextColor(int tabID, COLORREF color)
{
	if (m_CUGTab != NULL && ::IsWindow(m_CUGTab->GetSafeHwnd()))
	{
		// 调用Tab控件的设置选中文本颜色方法
		int result = m_CUGTab->SetTabHTextColor(tabID, color);
		m_CUGTab->Invalidate(); // 刷新显示
		return (result == UG_SUCCESS);
	}
	return false;
}

// 设置选项卡选中时的背景色
bool CSymbolGrid::SetTabHBackColor(int tabID, COLORREF color)
{
	if (m_CUGTab != NULL && ::IsWindow(m_CUGTab->GetSafeHwnd()))
	{
		// 调用Tab控件的设置选中背景颜色方法
		int result = m_CUGTab->SetTabHBackColor(tabID, color);
		m_CUGTab->Invalidate(); // 刷新显示
		return (result == UG_SUCCESS);
	}
	return false;
}

// 获取对齐方式标志
int CSymbolGrid::GetAlignmentFlag(int alignment)
{
	switch (alignment)
	{
	case COL_ALIGN_LEFT:
		return UG_ALIGNLEFT;
	case COL_ALIGN_CENTER:
		return UG_ALIGNCENTER;
	case COL_ALIGN_RIGHT:
		return UG_ALIGNRIGHT;
	default:
		return UG_ALIGNLEFT;
	}
}

void CSymbolGrid::OnSetup()
{
	UGXPThemes::UseThemes(FALSE);
	
	// 字体设置：使用常量定义
	AddFont(UIConstants::DEFAULT_FONT_NAME, UIConstants::DEFAULT_FONT_SIZE, FW_MEDIUM);
	AddFont(UIConstants::DEFAULT_FONT_NAME, UIConstants::BOLD_FONT_SIZE, FW_BOLD);
	SetDefFont(0);

	// 添加市场类型选项卡
	AddTab(_T("全部股票"), TAB_INDEX_ALL);
	AddTab(_T("上证A股"), TAB_INDEX_SH_A);
	AddTab(_T("深证A股"), TAB_INDEX_SZ_A);
	AddTab(_T("创业板"), TAB_INDEX_GEM);
	AddTab(_T("科创板"), TAB_INDEX_STAR);
	AddTab(_T("北证A股"), TAB_INDEX_BJ_A);

	SetNumberSheets(6);	
	
						
	SetSH_Width(0);
	SetTabWidth(UIConstants::DEFAULT_TAB_WIDTH);
	SetTabHeight(UIConstants::DEFAULT_TAB_HEIGHT / 2);  // 使用一半高度
	m_iArrowIndex = AddCellType(&m_sortArrow);
	SetCurrentCellMode(2);

	
	m_CurTabIndex = TAB_INDEX_ALL; // 默认为全部股票
	
	// 启用双缓冲绘制模式，减少闪烁
	SetDoubleBufferMode(TRUE);
	
	// 尝试加载保存的列配置
	LoadColumnConfig("");

	AdjustComponentSizes();
}


void CSymbolGrid::OnSheetSetup(int sheetNumber)
{
	UGXPThemes::UseThemes(FALSE);
	

	// 获取父文档以访问数据
	CStockDoc* pDoc = NULL;
	CWnd* pWnd = GetParent();
	while (pWnd && !pWnd->IsKindOf(RUNTIME_CLASS(CView)))
		pWnd = pWnd->GetParent();

	if (!pWnd)
		return;

	CView* pView = (CView*)pWnd;
	pDoc = (CStockDoc*)pView->GetDocument();
	if (!pDoc)
		return;

	//// 设置默认行高
	SetDefRowHeight(28);  // 增大行高，提高行间距

	// 设置表格列配置 - 根据当前的sheetNumber设置列
	SetupColumnsForTab(sheetNumber);

	// 设置默认字体 - 确保每个选项卡页面都使用相同字体
	SetDefFont(0); // 设置默认字体为第一个字体

	// 设置网格默认属性
	CUGCell cell;
	SetSH_Width(0);		// 设置最左侧一例宽度
	GetGridDefault(&cell);
	cell.SetBackColor(RGB(0,0,0)); // 背景色设为黑色
	cell.SetTextColor(RGB(255,255,255)); // 文本颜色设为白色
	SetGridDefault(&cell);

	// 设置表头样式
	CUGCell headerCell;
	GetHeadingDefault(&headerCell);
	headerCell.SetBackColor(MY_COLOR_GRID_HEADER); // 黑色表头
	headerCell.SetTextColor(MY_COLOR_GRID_HEADER_TEXT); // 黄色文字
	headerCell.SetAlignment(UG_ALIGNCENTER | UG_ALIGNVCENTER);
	// 去掉表头边框
	headerCell.SetBorder(UG_BDR_NONE);
	SetHeadingDefault(&headerCell);

	// 设置单元格无边框
	EnableExcelBorders(FALSE);

	// 去掉所有网格线
	SetVScrollMode(UG_SCROLLNORMAL);
	SetHScrollMode(UG_SCROLLNORMAL);

	// 启用双缓冲绘制，减少闪烁
	SetDoubleBufferMode(TRUE);

	// 启用性能优化选项
	SetUserSizingMode(TRUE);  // 允许用户调整列宽
	SetHighlightRow(TRUE);    // 高亮显示当前行
	SetCurrentCellMode(2);    // 设置单元格选择模式

	// 更新当前选项卡索引
	m_CurTabIndex = sheetNumber;

	// 根据选项卡类型加载对应股票列表
	EnableUpdate(FALSE); // 禁用更新，减少闪烁

	if (sheetNumber == TAB_INDEX_ALL)
	{
		SetNumberRows(pDoc->m_vecStocks.size());

		// 使用批量操作优化性能
		std::vector<std::pair<int, int>> cellParams;
		cellParams.reserve(pDoc->m_vecStocks.size());

		for (int n = 0; n < pDoc->m_vecStocks.size(); n++)
		{
			StockData& SD = pDoc->m_vecStocks.at(n);
			cellParams.emplace_back(n, SD._ID - 1);
		}

		BatchSetCellParams(cellParams);
	}
	if (sheetNumber == TAB_INDEX_SH_A)
	{
		int row = 0;
		SetNumberRows(pDoc->m_CountMarket_SH_A);
		for (int n = 0; n < pDoc->m_vecStocks.size(); n++)
		{
			StockData& SD = pDoc->m_vecStocks.at(n);
			if (SD._MarketType == MARKET_SH_A)
			{
				GetCell(0, row, &cell);
				cell.SetParam(SD._ID - 1);
				SetCell(0, row, &cell);
				row++;
			}
		}
	}
	if (sheetNumber == TAB_INDEX_SZ_A)
	{
		int row = 0;
		SetNumberRows(pDoc->m_CountMarket_SZ_A);
		for (int n = 0; n < pDoc->m_vecStocks.size(); n++)
		{
			StockData& SD = pDoc->m_vecStocks.at(n);
			if (SD._MarketType == MARKET_SZ_A)
			{
				GetCell(0, row, &cell);
				cell.SetParam(SD._ID - 1);
				SetCell(0, row, &cell);
				row++;
			}
		}
	}
	if (sheetNumber == TAB_INDEX_GEM)
	{
		int row = 0;
		SetNumberRows(pDoc->m_CountMarket_GEM);
		for (int n = 0; n < pDoc->m_vecStocks.size(); n++)
		{
			StockData& SD = pDoc->m_vecStocks.at(n);
			if (SD._MarketType == MARKET_GEM)
			{
				GetCell(0, row, &cell);
				cell.SetParam(SD._ID - 1);
				SetCell(0, row, &cell);
				row++;
			}
		}
	}
	if (sheetNumber == TAB_INDEX_STAR)
	{
		int row = 0;
		SetNumberRows(pDoc->m_CountMarket_STAR);
		for (int n = 0; n < pDoc->m_vecStocks.size(); n++)
		{
			StockData& SD = pDoc->m_vecStocks.at(n);
			if (SD._MarketType == MARKET_STAR)
			{
				GetCell(0, row, &cell);
				cell.SetParam(SD._ID-1);
				SetCell(0, row, &cell);
				row++;
			}
		}
	}
	if (sheetNumber == TAB_INDEX_BJ_A)
	{
		int row = 0;
		SetNumberRows(pDoc->m_CountMarket_BJ_A);
		for (int n = 0; n < pDoc->m_vecStocks.size(); n++)
		{
			StockData& SD = pDoc->m_vecStocks.at(n);
			if (SD._MarketType == MARKET_BJ_A)
			{
				GetCell(0, row, &cell);
				cell.SetParam(SD._ID - 1);
				SetCell(0, row, &cell);
				row++;
			}
		}
	}
}


void CSymbolGrid::OnSize(UINT nType, int cx, int cy)
{
	EnableUpdate(FALSE);

	RECT rect;
	GetClientRect(&rect);
	SetSH_Width(0);
	SetColWidth(0, 50);
	SetColWidth(1, 80);
	SetColWidth(2, 80);
	
	// 设置表头行高度
	//SetRowHeight(-1, 25);  // 减小表头行高，适应更小的字体
	
	// 列宽设置现在由AdjustComponentSizes方法处理
	// 这里不再设置单独的列宽
	
	EnableUpdate(TRUE);
	// 调用基类的OnSize方法
	CUGCtrl::OnSize(nType, cx, cy);
}


void CSymbolGrid::OnTabSelected(int ID)
{
	// 更新当前选中的选项卡
	m_CurTabIndex = ID;
	
	//// 设置当前表单，这将自动调用OnSheetSetup加载对应股票列表
	SetSheetNumber(ID);
}


// 
int	 CSymbolGrid::OnSortEvaluate(CUGCell* pCell1, CUGCell* pCell2, int iFlags)
{
	if (pCell1 == NULL && pCell2 == NULL)
		return 0;
	else if (pCell1 == NULL)
		return 1;
	else if (pCell2 == NULL)
		return -1;

	int retVal = 0;
	
	// 获取当前排序的列索引
	int sortCol = m_iSortCol;
	if (sortCol < 0 || sortCol >= m_nCurrentColCount)
		return 0;
		
	// 获取列的数据类型
	int dataType = m_colInfos[sortCol]._DataType;
	
	// 根据数据类型进行不同的比较
	switch (dataType)
	{
	case COL_TYPE_STRING:  // 字符串类型
		{
			CString str1 = pCell1->GetText();
			CString str2 = pCell2->GetText();
			retVal = str1.CompareNoCase(str2);
		}
		break;
		
	case COL_TYPE_NUMBER:  // 数字类型
		{
			double val1, val2;
			val1 = atof(pCell1->GetText());
			val2 = atof(pCell2->GetText());
			
			if (val1 < val2)
				retVal = -1;
			else if (val1 > val2)
				retVal = 1;
			else
				retVal = 0;
		}
		break;
		
	case COL_TYPE_PRICE:  // 价格类型，与数字类型处理方式相同
		{
			double val1, val2;
			val1 = atof(pCell1->GetText());
			val2 = atof(pCell2->GetText());
			
			if (val1 < val2)
				retVal = -1;
			else if (val1 > val2)
				retVal = 1;
			else
				retVal = 0;
		}
		break;
		
	case COL_TYPE_PERCENT:  // 百分比类型，需要去掉百分号再比较
		{
			CString str1 = pCell1->GetText();
			CString str2 = pCell2->GetText();
			
			// 处理"--"情况
			if (str1 == "--" && str2 == "--")
				return 0;
			else if (str1 == "--")
				return 1;  // "--"排在后面
			else if (str2 == "--")
				return -1;
				
			// 去掉百分号后转换为数值
			str1.Replace(_T("%"), _T(""));
			str2.Replace(_T("%"), _T(""));
			
			double val1 = atof(str1);
			double val2 = atof(str2);
			
			if (val1 < val2)
				retVal = -1;
			else if (val1 > val2)
				retVal = 1;
			else
				retVal = 0;
		}
		break;
		
	case COL_TYPE_DATE:  // 日期类型，使用字符串比较（假设格式为YYYY-MM-DD）
		{
			CString str1 = pCell1->GetText();
			CString str2 = pCell2->GetText();
			retVal = str1.Compare(str2);  // 按字典序比较日期字符串
		}
		break;
		
	case COL_TYPE_STATUS:  // 状态类型，使用字符串比较
		{
			CString str1 = pCell1->GetText();
			CString str2 = pCell2->GetText();
			retVal = str1.CompareNoCase(str2);
		}
		break;
		
	default:  // 默认使用字符串比较
		{
			CString str1 = pCell1->GetText();
			CString str2 = pCell2->GetText();
			retVal = str1.CompareNoCase(str2);
		}
		break;
	}
	
	// 如果是降序排序，反转返回值
	if ((iFlags & UG_SORT_DESCENDING) == UG_SORT_DESCENDING)
		retVal = -retVal;

	return retVal;
}

void CSymbolGrid::OnGetCell(int col,long row,CUGCell *cell)
{
	// 如果是表头(-1行)
	if (row == -1)
	{
		// 设置表头文字
		if (col >= 0 && col < m_nCurrentColCount)
		{
			// 根据列配置设置表头文字
			if (m_colInfos[col]._Visible)
			{
				cell->SetText(m_colInfos[col]._FieldName);
				cell->SetBackColor(RGB(0, 0, 0));
				cell->SetTextColor(RGB(230, 230, 230));
				cell->SetAlignment(UG_ALIGNCENTER | UG_ALIGNVCENTER);
				// 使用正确的方法设置字体
				cell->SetFont(GetFont(1)); // 使用粗体字体(索引1)
			}
		}
		return;
	}
	
	
	// 获取父文档以访问数据
	CStockDoc* pDoc = NULL;
	CWnd* pWnd = GetParent();
	while (pWnd && !pWnd->IsKindOf(RUNTIME_CLASS(CView)))
		pWnd = pWnd->GetParent();
		
	if (!pWnd)
		return;
		
	CView* pView = (CView*)pWnd;
	pDoc = (CStockDoc*)pView->GetDocument();
	if (!pDoc)
		return;

	// 获取当前选中股票索引
	CUGCell Cell0;
	GetCell(0, row, &Cell0);
	int stockIndex = Cell0.GetParam();


	// 使用索引获取股票数据
	const StockData* pStockData = pDoc->GetStock(stockIndex);
	if (!pStockData)
		return;
	
	// 设置所有行背景色为黑色
	cell->SetBackColor(UIConstants::COLOR_BLACK);

	// 设置单元格文本对齐方式
	if (col >= 0 && col < m_nCurrentColCount)
		cell->SetAlignment(GetAlignmentFlag(m_colInfos[col]._Alignment));

	// 设置单元格文本颜色 - 默认为白色
	cell->SetTextColor(UIConstants::COLOR_WHITE);
	
	// 根据列类型填充单元格数据
	if (col < 0 || col >= m_nCurrentColCount || !m_colInfos[col]._Visible)
		return;
	
	CString text;
	
	// 根据数据字段设置单元格数据
	if (strcmp(m_colInfos[col]._DataField, "index") == 0)
	{
		// 序号列
		text.Format(_T("%d"), row + 1);
		cell->SetText(text);
	}
	else if (strcmp(m_colInfos[col]._DataField, "code") == 0)
	{
		// 代码列
		cell->SetText(pStockData->_Code.c_str());
	}
	else if (strcmp(m_colInfos[col]._DataField, "name") == 0)
	{
		// 名称列
		cell->SetText(pStockData->_Name.c_str());
		// 设置名称列为黄色
		cell->SetTextColor(RGB(255, 255, 0));
	}
	else if (strcmp(m_colInfos[col]._DataField, "industry") == 0)
	{
		// 行业列
		cell->SetText(pStockData->_Industry.c_str());
	}
	else if (strcmp(m_colInfos[col]._DataField, "theme") == 0)
	{
		// 题材列
		cell->SetText(pStockData->_Theme.c_str());
	}
	else if (strcmp(m_colInfos[col]._DataField, "style") == 0)
	{
		// 风格列
		cell->SetText(pStockData->_Style.c_str());
	}
	else if (strcmp(m_colInfos[col]._DataField, "plate") == 0)
	{
		// 板块列
		cell->SetText(pStockData->_Plate.c_str());
	}
	else if (strcmp(m_colInfos[col]._DataField, "price") == 0)
	{
		// 价格列
		text.Format(_T("%.2f"), pStockData->_Close);
		cell->SetText(text);
		
		// 根据涨跌设置颜色
		if (pStockData->_Close > pStockData->_preClose)
			cell->SetTextColor(RGB(255, 40, 40)); // 上涨红色
		else if (pStockData->_Close < pStockData->_preClose)
			cell->SetTextColor(RGB(40, 255, 40)); // 下跌绿色
	}
	else if (strcmp(m_colInfos[col]._DataField, "change") == 0)
	{
		// 涨跌幅列
		if (pStockData->_preClose > 0)
		{
			float change = (pStockData->_Close - pStockData->_preClose) / pStockData->_preClose * 100;
			text.Format(_T("%.2f%%"), change);
			cell->SetText(text);
			
				
			// 根据涨跌设置颜色
			if (change > 0)
				cell->SetTextColor(UIConstants::COLOR_RED_UP);
			else if (change < 0)
				cell->SetTextColor(UIConstants::COLOR_GREEN_DOWN);
		}
		else
		{
	
			cell->SetText(_T("--"));
		}
	}
	else if (strcmp(m_colInfos[col]._DataField, "open") == 0)
	{
		// 开盘价列
		text.Format(_T("%.2f"), pStockData->_Open);
		cell->SetText(text);
		
		// 根据与昨收相比是涨是跌设置颜色
		if (pStockData->_Open > pStockData->_preClose)
			cell->SetTextColor(RGB(255, 40, 40)); // 上涨红色
		else if (pStockData->_Open < pStockData->_preClose)
			cell->SetTextColor(RGB(40, 255, 40)); // 下跌绿色
	}
	else if (strcmp(m_colInfos[col]._DataField, "high") == 0)
	{
		// 最高价列
		text.Format(_T("%.2f"), pStockData->_High);
		cell->SetText(text);
		
		// 根据与昨收相比是涨是跌设置颜色
		if (pStockData->_High > pStockData->_preClose)
			cell->SetTextColor(RGB(255, 40, 40)); // 上涨红色
		else if (pStockData->_High < pStockData->_preClose)
			cell->SetTextColor(RGB(40, 255, 40)); // 下跌绿色
	}
	else if (strcmp(m_colInfos[col]._DataField, "low") == 0)
	{
		// 最低价列
		text.Format(_T("%.2f"), pStockData->_Low);
		cell->SetText(text);
		
		// 根据与昨收相比是涨是跌设置颜色
		if (pStockData->_Low > pStockData->_preClose)
			cell->SetTextColor(RGB(255, 40, 40)); // 上涨红色
		else if (pStockData->_Low < pStockData->_preClose)
			cell->SetTextColor(RGB(40, 255, 40)); // 下跌绿色
	}
	else if (strcmp(m_colInfos[col]._DataField, "pre_close") == 0)
	{
		// 昨收价列
		text.Format(_T("%.2f"), pStockData->_preClose);
		cell->SetText(text);
	}
	else if (strcmp(m_colInfos[col]._DataField, "volume") == 0)
	{
		// 成交量列(手)
		if (pStockData->_Volume >= 10000)
			text.Format(_T("%.2f万"), pStockData->_Volume / 100.0 / 10000.0);
		else
			text.Format(_T("%d"), (int)(pStockData->_Volume / 100.0));
		cell->SetText(text);
		// 设置成交量为亮蓝色
		cell->SetTextColor(RGB(0, 175, 255));
	}
	else if (strcmp(m_colInfos[col]._DataField, "amount") == 0)
	{
		// 成交额列(万元)
		if (pStockData->_Amount >= 10000000)
			text.Format(_T("%.2f亿"), pStockData->_Amount / 10000.0 / 10000.0);
		else
			text.Format(_T("%.2f万"), pStockData->_Amount / 10000.0);
		cell->SetText(text);
		// 设置成交额为亮蓝色
		cell->SetTextColor(RGB(0, 175, 255));
	}
	else if (strcmp(m_colInfos[col]._DataField, "turnover") == 0)
	{
		// 换手率列
		text.Format(_T("%.2f%%"), pStockData->_Turnover);
		cell->SetText(text);
	}
	else if (strcmp(m_colInfos[col]._DataField, "volume_ratio") == 0)
	{
		// 量比列
		text.Format(_T("%.2f"), pStockData->_VolumeRatio);
		cell->SetText(text);
	}
	else if (strcmp(m_colInfos[col]._DataField, "main_inflow") == 0)
	{
		// 主力买列（单位：元，统一显示为亿元）
		double inflowInYi = pStockData->_MainInflow / 100000000.0; // 将元转换为亿元
		text.Format(_T("%.2f亿"), inflowInYi);
		cell->SetText(text);
		cell->SetTextColor(RGB(255, 40, 40)); // 主力买入显示为红色
	}
	else if (strcmp(m_colInfos[col]._DataField, "main_outflow") == 0)
	{
		// 主力卖列（单位：元，统一显示为亿元）
		double outflowInYi = pStockData->_MainOutflow / 100000000.0; // 将元转换为亿元
		text.Format(_T("%.2f亿"), outflowInYi);
		cell->SetText(text);
		cell->SetTextColor(RGB(40, 255, 40)); // 主力卖出显示为绿色
	}
	else if (strcmp(m_colInfos[col]._DataField, "main_net_inflow") == 0)
	{
		// 主力净额列（单位：元，统一显示为亿元）
		double netInflow = pStockData->_MainInflow - pStockData->_MainOutflow; // 净流入（元）
		double netInflowInYi = netInflow / 100000000.0; // 将元转换为亿元
		text.Format(_T("%.2f亿"), netInflowInYi);
		cell->SetText(text);
		
		// 净流入为正显示红色，为负显示绿色
		if (netInflow > 0)
			cell->SetTextColor(RGB(255, 40, 40));
		else if (netInflow < 0)
			cell->SetTextColor(RGB(40, 255, 40));
	}
	else if (strcmp(m_colInfos[col]._DataField, "circulating_value") == 0)
	{
		// 流通值列(亿元)
		text.Format(_T("%.2f亿"), pStockData->_CirculatingValue);
		cell->SetText(text);
	}
	else if (strcmp(m_colInfos[col]._DataField, "actual_circulating_value") == 0)
	{
		// 实际流通值列(亿元)
		text.Format(_T("%.2f亿"), pStockData->_ActualCirculatingValue);
		cell->SetText(text);
	}
	else if (strcmp(m_colInfos[col]._DataField, "amplitude") == 0)
	{
		// 振幅列
		if (pStockData->_preClose > 0)
		{
			float amplitude = (pStockData->_High - pStockData->_Low) / pStockData->_preClose * 100;
			text.Format(_T("%.2f%%"), amplitude);
			cell->SetText(text);
		}
		else
		{
			cell->SetText(_T("--"));
		}
		// 设置振幅为暗紫色
		cell->SetTextColor(RGB(180, 120, 240));
	}
	else if (strcmp(m_colInfos[col]._DataField, "open_change") == 0)
	{
		// 开盘涨幅列
		if (pStockData->_preClose > 0)
		{
			float openChange = (pStockData->_Open - pStockData->_preClose) / pStockData->_preClose * 100;
			text.Format(_T("%.2f%%"), openChange);
			cell->SetText(text);
			
			// 根据涨跌设置颜色
			if (openChange > 0)
				cell->SetTextColor(RGB(255, 40, 40)); // 上涨红色
			else if (openChange < 0)
				cell->SetTextColor(RGB(40, 255, 40)); // 下跌绿色
		}
		else
		{
			cell->SetText(_T("--"));
		}
	}
	else if (strcmp(m_colInfos[col]._DataField, "change_amount") == 0)
	{
		// 涨跌列
		float change = pStockData->_Close - pStockData->_preClose;
		text.Format(_T("%.2f"), change);
		cell->SetText(text);
		
		// 根据涨跌设置颜色
		if (change > 0)
			cell->SetTextColor(RGB(255, 40, 40)); // 上涨红色
		else if (change < 0)
			cell->SetTextColor(RGB(40, 255, 40)); // 下跌绿色
	}
}


void CSymbolGrid::OnLClicked(int col,long row,int updn,RECT *rect,POINT *point,int processed)
{
	int	nCol = GetCurrentCol();
	long nRow = GetCurrentRow();
	if (nRow > -1)
	{

	}
}
		
// 双击列表
void CSymbolGrid::OnDClicked(int col,long row,RECT *rect,POINT *point,BOOL processed)
{
	int	nCol = GetCurrentCol();
	long nRow = GetCurrentRow();
	if (nRow < 0)
		return;

	// 获取父文档
	CWnd* pWnd = GetParent();
	while (pWnd && !pWnd->IsKindOf(RUNTIME_CLASS(CView)))
		pWnd = pWnd->GetParent();

	if (!pWnd)
		return;

	CView* pView = (CView*)pWnd;
	CStockDoc* pDoc = (CStockDoc*)pView->GetDocument();
	if (!pDoc)
		return;


	CUGCell Cell;
	GetCell(0, row, &Cell);
	int stockIndex = Cell.GetParam();


	// 使用索引获取股票数据
	const StockData* pStockData = pDoc->GetStock(stockIndex);
	// 设置当前股票
	pDoc->SetCurrentStock(pStockData->_Code);

	// 获取主框架
	CMainFrame* pMainFrame = (CMainFrame*)AfxGetMainWnd();
	if (pMainFrame)
	{
		// 切换到分时视图
		pMainFrame->SwitchView(pMainFrame->m_nTimeLineViewID);
	}
}	


// 表格区域右键点击事件
void CSymbolGrid::OnRClicked(int col, long row, int updn, RECT *rect, POINT *point, int processed)
{
	// 只处理鼠标按下事件
	if (updn != 1)
		return;
	
	// 创建弹出菜单
	CMenu menu;
	menu.CreatePopupMenu();
	
	// 添加列配置菜单项
	menu.AppendMenu(MF_STRING, ID_COLUMN_CONFIG, _T("列配置..."));
	
	// 在点击位置显示菜单
	CPoint pt = *point;
	ClientToScreen(&pt);
	menu.TrackPopupMenu(TPM_LEFTALIGN | TPM_RIGHTBUTTON, pt.x, pt.y, this);
	
	// 清理
	menu.DestroyMenu();
}


// 拖动网格
void CSymbolGrid::OnMouseMove(int col,long row,POINT *point,UINT nFlags,BOOL processed)
{
}


// 键盘事件
void CSymbolGrid::OnKeyDown(UINT* vcKey, BOOL processed)
{
	CUGCtrl::OnKeyDown(vcKey, processed);
	if      (*vcKey == VK_UP)
	{
		if (GetCurrentRow() == 0)
			return;
	
	
	}
	else if (*vcKey == VK_DOWN)
	{
		int nRow = GetCurrentRow();
		int nRows = GetNumberRows();

	}
	else if (*vcKey == VK_RETURN)
	{
		int	nCol = GetCurrentCol();
		long nRow = GetCurrentRow();
		if (nRow > -1)
		{
			// 获取主框架指针
			CMainFrame* pMainFrame = (CMainFrame*)AfxGetMainWnd();
			if (pMainFrame)
			{
				// 获取父文档
				CWnd* pWnd = GetParent();
				while (pWnd && !pWnd->IsKindOf(RUNTIME_CLASS(CView)))
					pWnd = pWnd->GetParent();

				if (!pWnd)
					return;

				CView* pView = (CView*)pWnd;
				CStockDoc* pDoc = (CStockDoc*)pView->GetDocument();
				if (!pDoc)
					return;

				// 从当前选中行获取股票代码
				CUGCell cell;
				GetCell(0, nRow, &cell);
				int stockIndex = cell.GetParam();

				// 使用索引获取股票数据
				const StockData* pStockData = pDoc->GetStock(stockIndex);
				if (!pStockData)
					return;

				// 设置当前股票
				pDoc->SetCurrentStock(pStockData->_Code);
    
				// 切换到分时视图
				pMainFrame->SwitchView(pMainFrame->m_nTimeLineViewID);
				
				TRACE(_T("SymbolGrid: 按回车键切换到分时视图，股票代码: %s\n"), pStockData->_Code.c_str());
			}
		}
	}
	else if (*vcKey == VK_DELETE)
	{
		int nSheetNumber = GetSheetNumber();
		long row = GetCurrentRow();
		if (row > -1)
		{

		
		}
	}
	else if (((*vcKey >= 0x30) && (*vcKey <= 0x39)) || ((*vcKey >= 0x41) && (*vcKey <= 0x5A)))
	{
		
	}
}

COLORREF CSymbolGrid::OnGetDefBackColor(int section)
{
	return COLOUR_BLACK;
}

void CSymbolGrid::OnTopRowChange(long oldrow, long newrow)
{

}
// 鼠标滚轮
BOOL CSymbolGrid::OnMouseWheel(UINT nFlags, short zDelta, CPoint pt)
{
	return CUGCtrl::OnMouseWheel(nFlags, zDelta, pt);
}

// 优化的单元格参数设置函数
void CSymbolGrid::SetCellParamOptimized(int row, int stockIndex)
{
	// 使用GetCell/SetCell但避免重复重绘
	CUGCell cell;
	GetCell(0, row, &cell);
	cell.SetParam(stockIndex);
	SetCell(0, row, &cell);
}

// 批量设置单元格参数
void CSymbolGrid::BatchSetCellParams(const std::vector<std::pair<int, int>>& cellParams)
{
	// 禁用重绘以提高性能
	EnableUpdate(FALSE);

	CUGCell cell;
	for (const auto& param : cellParams) {
		GetCell(0, param.first, &cell);
		cell.SetParam(param.second);
		SetCell(0, param.first, &cell);
	}

	// 重新启用更新并重绘
	EnableUpdate(TRUE);
	RedrawWindow();
}



int		CSymbolGrid::OnMenuStart(int col, long row, int section)
{
	UNREFERENCED_PARAMETER(col);
	UNREFERENCED_PARAMETER(row);

	return FALSE;
}


void	CSymbolGrid::OnMenuCommand(int col, long row, int section, int item)
{
	// 检查菜单ID
	switch (item)
			{
	case ID_COLUMN_CONFIG:
		// 调用列配置对话框
		OnColumnConfig();
		break;
			
	case ID_RECENT_STRONG:
		// 处理强势股排名
		OnRecentStrong();
		break;
	}
}


void	CSymbolGrid::OnTH_LClicked(int col, long row, int updn, RECT* rect, POINT* point, BOOL processed)
{
	UNREFERENCED_PARAMETER(row);
	UNREFERENCED_PARAMETER(rect);
	UNREFERENCED_PARAMETER(point);
	UNREFERENCED_PARAMETER(processed);

	if (updn == 0)
		return;
	
	//QuickSetCellType(m_iSortCol, -1, 0);

	if (col == m_iSortCol)
	{
		if (m_bSortedAscending)
			m_bSortedAscending = FALSE;
		else
			m_bSortedAscending = TRUE;
	}
	else
	{
		m_iSortCol = col;
		m_bSortedAscending = TRUE;
	}

	if (m_bSortedAscending)
	{
		SortBy(col, UG_SORT_ASCENDING);
		QuickSetCellType(m_iSortCol, -1, m_iArrowIndex);
		QuickSetCellTypeEx(m_iSortCol, -1, UGCT_SORTARROWDOWN);
	}
	else
	{
		SortBy(col, UG_SORT_DESCENDING);
		QuickSetCellType(m_iSortCol, -1, m_iArrowIndex);
		QuickSetCellTypeEx(m_iSortCol, -1, UGCT_SORTARROWUP);
	}

	RedrawAll();
}



void CSymbolGrid::OnRecentStrong()
{
	// 获取父文档
	CStockDoc* pDoc = NULL;
	CWnd* pWnd = GetParent();
	while (pWnd && !pWnd->IsKindOf(RUNTIME_CLASS(CView)))
		pWnd = pWnd->GetParent();
		
	if (pWnd)
	{
		CView* pView = (CView*)pWnd;
		pDoc = (CStockDoc*)pView->GetDocument();
	}
	
	if (!pDoc)
		return;
	
	// 显示强势股排名功能
	MessageBox(_T("强势股排名功能正在开发中..."), _T("提示"), MB_ICONINFORMATION);
	
	// TODO: 在此处实现强势股排名相关功能
}


// 将选项卡索引转换为市场类型
int CSymbolGrid::GetTabToMarketType(int tabIndex)
{
	// 将TAB索引映射到市场类型
	switch (tabIndex)
	{
	case TAB_INDEX_ALL:
		return MARKET_ALL;
	case TAB_INDEX_SH_A:
		return MARKET_SH_A;
	case TAB_INDEX_SZ_A:
		return MARKET_SZ_A;
	case TAB_INDEX_GEM:
		return MARKET_GEM;
	case TAB_INDEX_STAR:
		return MARKET_STAR;
	case TAB_INDEX_BJ_A:
		return MARKET_BJ_A;
	default:
		return MARKET_ALL;
	}
}

// 获取列文本颜色
COLORREF CSymbolGrid::GetColTextColor(int col, long row)
{
	// 默认文本颜色
	COLORREF color = COLOUR_WHITE;
	
	
	
	return color;
}

// 调整组件大小
void CSymbolGrid::AdjustComponentSizes()
{
	// 获取窗口尺寸
	CRect rect;
	GetClientRect(&rect);
	
	// 设置表头宽度
	SetSH_Width(0);
	
	// 根据当前列配置设置各列宽度
	for (int i = 0; i < m_nCurrentColCount; i++)
	{
		if (m_colInfos[i]._Visible)
		{
			SetColWidth(i, m_colInfos[i]._Width);
		}
	}
}

// 设置当前选项卡的列配置
void CSymbolGrid::SetupColumnsForTab(int tabIndex)
{
	// 如果使用自定义配置，则不重置列配置
	if (m_bUsingCustomConfig)
	{
		// 仅调整表格列数和列宽
		SetNumberCols(m_nCurrentColCount);
		
		// 设置列宽
		for (int i = 0; i < m_nCurrentColCount; i++)
		{
			if (m_colInfos[i]._Visible)
			{
				SetColWidth(i, m_colInfos[i]._Width);
			}
		}
		
		return;
	}

	// 重置列配置
	memset(m_colInfos, 0, sizeof(m_colInfos));
	
	// 基本列配置 - 适用于所有选项卡的通用列
	strcpy_s(m_colInfos[0]._FieldName, "序号");
	m_colInfos[0]._Width = 50;  // 减小列宽
	m_colInfos[0]._Visible = true;
	m_colInfos[0]._DataType = COL_TYPE_NUMBER;
	m_colInfos[0]._Alignment = COL_ALIGN_CENTER;
	m_colInfos[0]._DisplayOrder = 0;
	strcpy_s(m_colInfos[0]._DataField, "index");
	
	strcpy_s(m_colInfos[1]._FieldName, "代码");
	m_colInfos[1]._Width = 85;  // 减小列宽
	m_colInfos[1]._Visible = true;
	m_colInfos[1]._DataType = COL_TYPE_STRING;
	m_colInfos[1]._Alignment = COL_ALIGN_CENTER;
	m_colInfos[1]._DisplayOrder = 1;
	strcpy_s(m_colInfos[1]._DataField, "code");
	
	strcpy_s(m_colInfos[2]._FieldName, "名称");
	m_colInfos[2]._Width = 100; // 减小列宽
	m_colInfos[2]._Visible = true;
	m_colInfos[2]._DataType = COL_TYPE_STRING;
	m_colInfos[2]._Alignment = COL_ALIGN_LEFT;
	m_colInfos[2]._DisplayOrder = 2;
	strcpy_s(m_colInfos[2]._DataField, "name");
	
	// 行业信息 (字段索引从3开始，因为删除了市场字段)
	strcpy_s(m_colInfos[3]._FieldName, "行业");
	m_colInfos[3]._Width = 120; // 减小列宽
	m_colInfos[3]._Visible = true;
	m_colInfos[3]._DataType = COL_TYPE_STRING;
	m_colInfos[3]._Alignment = COL_ALIGN_LEFT;
	m_colInfos[3]._DisplayOrder = 3;
	strcpy_s(m_colInfos[3]._DataField, "industry");
	
	// 价格信息
	strcpy_s(m_colInfos[4]._FieldName, "价格");
	m_colInfos[4]._Width = 80;  // 减小列宽
	m_colInfos[4]._Visible = true;
	m_colInfos[4]._DataType = COL_TYPE_PRICE;
	m_colInfos[4]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[4]._DisplayOrder = 4;
	strcpy_s(m_colInfos[4]._DataField, "price");
	
	strcpy_s(m_colInfos[5]._FieldName, "涨幅");
	m_colInfos[5]._Width = 80;  // 减小列宽
	m_colInfos[5]._Visible = true;
	m_colInfos[5]._DataType = COL_TYPE_PERCENT;
	m_colInfos[5]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[5]._DisplayOrder = 5;
	strcpy_s(m_colInfos[5]._DataField, "change");
	
	// 成交相关
	strcpy_s(m_colInfos[6]._FieldName, "成交量");
	m_colInfos[6]._Width = 90;
	m_colInfos[6]._Visible = true;
	m_colInfos[6]._DataType = COL_TYPE_NUMBER;
	m_colInfos[6]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[6]._DisplayOrder = 6;
	strcpy_s(m_colInfos[6]._DataField, "volume");
	
	strcpy_s(m_colInfos[7]._FieldName, "成交额");
	m_colInfos[7]._Width = 90;
	m_colInfos[7]._Visible = true;
	m_colInfos[7]._DataType = COL_TYPE_NUMBER;
	m_colInfos[7]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[7]._DisplayOrder = 7;
	strcpy_s(m_colInfos[7]._DataField, "amount");
	
	strcpy_s(m_colInfos[8]._FieldName, "换手率");
	m_colInfos[8]._Width = 70;
	m_colInfos[8]._Visible = true;
	m_colInfos[8]._DataType = COL_TYPE_PERCENT;
	m_colInfos[8]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[8]._DisplayOrder = 8;
	strcpy_s(m_colInfos[8]._DataField, "turnover");
	
	strcpy_s(m_colInfos[9]._FieldName, "量比");
	m_colInfos[9]._Width = 70;
	m_colInfos[9]._Visible = true;
	m_colInfos[9]._DataType = COL_TYPE_NUMBER;
	m_colInfos[9]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[9]._DisplayOrder = 9;
	strcpy_s(m_colInfos[9]._DataField, "volume_ratio");
	
	// 价格相关信息
	strcpy_s(m_colInfos[10]._FieldName, "昨收");
	m_colInfos[10]._Width = 70;
	m_colInfos[10]._Visible = true;
	m_colInfos[10]._DataType = COL_TYPE_PRICE;
	m_colInfos[10]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[10]._DisplayOrder = 10;
	strcpy_s(m_colInfos[10]._DataField, "pre_close");
	
	strcpy_s(m_colInfos[11]._FieldName, "开盘");
	m_colInfos[11]._Width = 70;
	m_colInfos[11]._Visible = true;
	m_colInfos[11]._DataType = COL_TYPE_PRICE;
	m_colInfos[11]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[11]._DisplayOrder = 11;
	strcpy_s(m_colInfos[11]._DataField, "open");
	
	strcpy_s(m_colInfos[12]._FieldName, "最高");
	m_colInfos[12]._Width = 70;
	m_colInfos[12]._Visible = true;
	m_colInfos[12]._DataType = COL_TYPE_PRICE;
	m_colInfos[12]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[12]._DisplayOrder = 12;
	strcpy_s(m_colInfos[12]._DataField, "high");
	
	strcpy_s(m_colInfos[13]._FieldName, "最低");
	m_colInfos[13]._Width = 70;
	m_colInfos[13]._Visible = true;
	m_colInfos[13]._DataType = COL_TYPE_PRICE;
	m_colInfos[13]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[13]._DisplayOrder = 13;
	strcpy_s(m_colInfos[13]._DataField, "low");
	
	// 跳过涨停价和跌停价字段
	
	// 涨跌幅相关
	strcpy_s(m_colInfos[14]._FieldName, "涨跌");
	m_colInfos[14]._Width = 70;
	m_colInfos[14]._Visible = true;
	m_colInfos[14]._DataType = COL_TYPE_PRICE;
	m_colInfos[14]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[14]._DisplayOrder = 14;
	strcpy_s(m_colInfos[14]._DataField, "change_amount");
	
	strcpy_s(m_colInfos[15]._FieldName, "振幅");
	m_colInfos[15]._Width = 70;
	m_colInfos[15]._Visible = true;
	m_colInfos[15]._DataType = COL_TYPE_PERCENT;
	m_colInfos[15]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[15]._DisplayOrder = 15;
	strcpy_s(m_colInfos[15]._DataField, "amplitude");
	
	strcpy_s(m_colInfos[16]._FieldName, "开盘涨幅");
	m_colInfos[16]._Width = 80;
	m_colInfos[16]._Visible = true;
	m_colInfos[16]._DataType = COL_TYPE_PERCENT;
	m_colInfos[16]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[16]._DisplayOrder = 16;
	strcpy_s(m_colInfos[16]._DataField, "open_change");
	
	strcpy_s(m_colInfos[17]._FieldName, "竞价涨幅");
	m_colInfos[17]._Width = 80;
	m_colInfos[17]._Visible = true;
	m_colInfos[17]._DataType = COL_TYPE_PERCENT;
	m_colInfos[17]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[17]._DisplayOrder = 17;
	strcpy_s(m_colInfos[17]._DataField, "auction_change");
	
	// 主力资金
	strcpy_s(m_colInfos[18]._FieldName, "主力买");
	m_colInfos[18]._Width = 90;
	m_colInfos[18]._Visible = true;
	m_colInfos[18]._DataType = COL_TYPE_NUMBER;
	m_colInfos[18]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[18]._DisplayOrder = 18;
	strcpy_s(m_colInfos[18]._DataField, "main_inflow");
	
	strcpy_s(m_colInfos[19]._FieldName, "主力卖");
	m_colInfos[19]._Width = 90;
	m_colInfos[19]._Visible = true;
	m_colInfos[19]._DataType = COL_TYPE_NUMBER;
	m_colInfos[19]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[19]._DisplayOrder = 19;
	strcpy_s(m_colInfos[19]._DataField, "main_outflow");
	
	strcpy_s(m_colInfos[20]._FieldName, "主力净额");
	m_colInfos[20]._Width = 90;
	m_colInfos[20]._Visible = true;
	m_colInfos[20]._DataType = COL_TYPE_NUMBER;
	m_colInfos[20]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[20]._DisplayOrder = 20;
	strcpy_s(m_colInfos[20]._DataField, "main_net_inflow");
	
	// 估值指标
	strcpy_s(m_colInfos[21]._FieldName, "市盈率");
	m_colInfos[21]._Width = 70;
	m_colInfos[21]._Visible = true;
	m_colInfos[21]._DataType = COL_TYPE_NUMBER;
	m_colInfos[21]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[21]._DisplayOrder = 21;
	strcpy_s(m_colInfos[21]._DataField, "pe");
	
	strcpy_s(m_colInfos[22]._FieldName, "市净率");
	m_colInfos[22]._Width = 70;
	m_colInfos[22]._Visible = true;
	m_colInfos[22]._DataType = COL_TYPE_NUMBER;
	m_colInfos[22]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[22]._DisplayOrder = 22;
	strcpy_s(m_colInfos[22]._DataField, "pb");

	// 流通值相关
	strcpy_s(m_colInfos[23]._FieldName, "流通值");
	m_colInfos[23]._Width = 90;
	m_colInfos[23]._Visible = true;
	m_colInfos[23]._DataType = COL_TYPE_NUMBER;
	m_colInfos[23]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[23]._DisplayOrder = 23;
	strcpy_s(m_colInfos[23]._DataField, "circulating_value");
	
	strcpy_s(m_colInfos[24]._FieldName, "实际流通值");
	m_colInfos[24]._Width = 90;
	m_colInfos[24]._Visible = true;
	m_colInfos[24]._DataType = COL_TYPE_NUMBER;
	m_colInfos[24]._Alignment = COL_ALIGN_RIGHT;
	m_colInfos[24]._DisplayOrder = 24;
	strcpy_s(m_colInfos[24]._DataField, "actual_circulating_value");
	
	
	// 普通A股只显示通用字段
	m_nCurrentColCount = 25; // 减少7个字段后的总数
	
	
	// 第一次设置时，保存为默认配置
	if (m_defaultColInfos[0]._FieldName[0] == 0)
	{
		memcpy(m_defaultColInfos, m_colInfos, sizeof(m_colInfos));
	}
	
	// 确保列数不超过最大限制
	if (m_nCurrentColCount > NUM_COLS_MAX)
	{
		TRACE(_T("警告：当前列数(%d)超过最大限制(%d)，已被调整\n"), m_nCurrentColCount, NUM_COLS_MAX);
		m_nCurrentColCount = NUM_COLS_MAX;
	}
	
	// 设置表格列数
	SetNumberCols(m_nCurrentColCount);
	
	// 设置列宽
	for (int i = 0; i < m_nCurrentColCount; i++)
	{
		if (m_colInfos[i]._Visible)
		{
			SetColWidth(i, m_colInfos[i]._Width);
		}
	}
}

// 获取当前选项卡的列配置
const COLUMN_INFO* CSymbolGrid::GetCurrentTabColumns() const
{
	return m_colInfos;
}

// 获取当前选项卡的列数
int CSymbolGrid::GetCurrentColumnCount() const
{
	return m_nCurrentColCount;
}

// 添加自定义列
bool CSymbolGrid::AddCustomColumn(COLUMN_INFO& colInfo)
{
	// 检查是否还有空间添加新列
	if (m_nCurrentColCount >= NUM_COLS_MAX)
	{
		return false;
	}
	
	// 设置为用户自定义
	colInfo._UserDefined = true;
	colInfo._DisplayOrder = m_nCurrentColCount;
	
	// 添加到当前列配置中
	memcpy(&m_colInfos[m_nCurrentColCount], &colInfo, sizeof(COLUMN_INFO));
	
	// 更新列数
	m_nCurrentColCount++;
	
	// 设置为使用自定义配置
	m_bUsingCustomConfig = true;
	
	// 更新表格列数
	SetNumberCols(m_nCurrentColCount);
	
	// 设置列宽
	SetColWidth(m_nCurrentColCount - 1, colInfo._Width);
	
	// 重新绘制表格
	RedrawAll();
	
	// 调整所有列的显示顺序
	int visibleColCount = 0;
	for (int i = 0; i < m_nCurrentColCount; i++)
	{
		if (m_colInfos[i]._Visible)
		{
			m_colInfos[i]._DisplayOrder = visibleColCount++;
		}
	}
	
	// 保存配置
	SaveColumnConfig("");
	
	return true;
}

// 隐藏指定列
bool CSymbolGrid::HideColumn(int colIndex)
{
	// 检查列索引是否有效
	if (colIndex < 0 || colIndex >= m_nCurrentColCount)
	{
		return false;
	}
	
	// 不允许隐藏前三列（序号、股票代码、股票名称）
	if (colIndex < 3)
	{
		return false;
	}
	
	// 设置为不可见
	m_colInfos[colIndex]._Visible = false;
	
	// 设置为使用自定义配置
	m_bUsingCustomConfig = true;
	
	// 重新排列可见列
	int visibleColCount = 0;
	for (int i = 0; i < m_nCurrentColCount; i++)
	{
		if (m_colInfos[i]._Visible)
		{
			m_colInfos[i]._DisplayOrder = visibleColCount++;
		}
	}
	
	// 重新绘制表格
	RedrawAll();
	
	return true;
}

// 显示指定列
bool CSymbolGrid::ShowColumn(int colIndex)
{
	// 检查列索引是否有效
	if (colIndex < 0 || colIndex >= m_nCurrentColCount)
	{
		return false;
	}
	
	// 设置为可见
	m_colInfos[colIndex]._Visible = true;
	
	// 设置为使用自定义配置
	m_bUsingCustomConfig = true;
	
	// 重新排列可见列
	int visibleColCount = 0;
	for (int i = 0; i < m_nCurrentColCount; i++)
	{
		if (m_colInfos[i]._Visible)
		{
			m_colInfos[i]._DisplayOrder = visibleColCount++;
		}
	}
	
	// 重新绘制表格
	RedrawAll();
	
	return true;
}

// 重置列配置为默认值
void CSymbolGrid::ResetColumnsToDefault()
{
	// 复制默认配置
	memcpy(m_colInfos, m_defaultColInfos, sizeof(m_colInfos));
	
	// 重置标志
	m_bUsingCustomConfig = false;
	
	// 重置列数
	for (m_nCurrentColCount = 0; m_nCurrentColCount < NUM_COLS_MAX; m_nCurrentColCount++)
	{
		if (m_defaultColInfos[m_nCurrentColCount]._FieldName[0] == 0)
			break;
	}
	
	// 更新表格列数
	SetNumberCols(m_nCurrentColCount);
	
	// 调整列宽
	AdjustComponentSizes();
	
	// 重新绘制表格
	RedrawAll();
}

// 保存当前列配置
bool CSymbolGrid::SaveColumnConfig(const char* configName)
{
	// 固定配置文件名为ColConfig.dat，保存在程序目录下
	char configFileName[MAX_PATH] = "ColConfig.dat";
	
	// 打开文件
	FILE* file = NULL;
	fopen_s(&file, configFileName, "wb");
	if (file == NULL)
	{
		return false;
	}
	
	// 写入列数
	fwrite(&m_nCurrentColCount, sizeof(m_nCurrentColCount), 1, file);
	
	// 写入列配置
	fwrite(m_colInfos, sizeof(COLUMN_INFO), m_nCurrentColCount, file);
	
	// 关闭文件
	fclose(file);
	
	return true;
}

// 加载列配置
bool CSymbolGrid::LoadColumnConfig(const char* configName)
{
	// 固定配置文件名为ColConfig.dat，从程序目录加载
	char configFileName[MAX_PATH] = "ColConfig.dat";
	
	// 打开文件
	FILE* file = NULL;
	fopen_s(&file, configFileName, "rb");
	if (file == NULL)
	{
		return false;
	}
	
	// 读取列数
	int colCount = 0;
	fread(&colCount, sizeof(colCount), 1, file);
	
	// 检查列数是否有效
	if (colCount <= 0 || colCount > NUM_COLS_MAX)
	{
		fclose(file);
		return false;
	}
	
	// 读取列配置
	COLUMN_INFO colInfos[NUM_COLS_MAX] = { 0 };
	fread(colInfos, sizeof(COLUMN_INFO), colCount, file);
	
	// 关闭文件
	fclose(file);
	
	// 应用配置
	memcpy(m_colInfos, colInfos, sizeof(COLUMN_INFO) * colCount);
	m_nCurrentColCount = colCount;
	m_bUsingCustomConfig = true;
	
	// 更新表格列数
	SetNumberCols(m_nCurrentColCount);
	
	// 调整列宽
	AdjustComponentSizes();
	
	// 重新绘制表格
	RedrawAll();
	
	return true;
}

// 列配置对话框
void CSymbolGrid::OnColumnConfig()
{
	// 创建列配置对话框实例
	CColumnConfigDlg dlg;
	
	// 设置当前列配置信息
	dlg.SetColumnInfos(m_colInfos, m_nCurrentColCount);
	
	// 显示对话框
	if (dlg.DoModal() == IDOK)
	{
		// 获取用户配置的列信息
		int newColumnCount = dlg.GetColumnCount();
		
		// 检查是否要重置为默认配置
		if (newColumnCount < 0)
		{
			// 重置为默认配置
			ResetColumnsToDefault();
		}
		else
		{
			// 更新列配置
			const COLUMN_INFO* newColumns = dlg.GetColumnInfos();
			
			// 复制新的列配置
			memcpy(m_colInfos, newColumns, sizeof(COLUMN_INFO) * newColumnCount);
			m_nCurrentColCount = newColumnCount;
			
			// 标记为使用自定义配置
			m_bUsingCustomConfig = true;
			
			// 保存列配置
			SaveColumnConfig("");
		}
		
		// 更新列配置
		SetNumberCols(m_nCurrentColCount);
		
		// 重绘表格
		RedrawAll();
	}
}



// 表头右键点击事件处理
void CSymbolGrid::OnTH_RClicked(int col, long row, int updn, RECT *rect, POINT *point, BOOL processed)
{
	// 只处理鼠标按下事件
	if (updn != 1)
		return;
	
	// 创建弹出菜单
	CMenu menu;
	menu.CreatePopupMenu();
	
	// 添加列配置菜单项
	menu.AppendMenu(MF_STRING, ID_COLUMN_CONFIG, _T("列配置..."));
	
	// 在点击位置显示菜单
	CPoint pt = *point;
	ClientToScreen(&pt);
	menu.TrackPopupMenu(TPM_LEFTALIGN | TPM_RIGHTBUTTON, pt.x, pt.y, this);
	
	// 清理
	menu.DestroyMenu();
}

// 行变更事件处理
void CSymbolGrid::OnRowChange(long oldrow, long newrow)
{
	// 保存当前选中行
	m_nCurrentRow = newrow;
	
	// 设置选中行的背景色和文字颜色
	if (newrow >= 0)
	{
		// 使用深蓝色作为选中行背景，以便在黑色背景中突出显示
		for (int col = 0; col < GetNumberCols(); col++)
		{
			QuickSetBackColor(col, newrow, RGB(0, 0, 80)); // 深蓝色背景
			QuickSetTextColor(col, newrow, RGB(255, 255, 255)); // 白色文字
		}
	}
	
	// 如果有旧选中行，恢复其背景色
	if (oldrow >= 0 && oldrow != newrow)
	{
		for (int col = 0; col < GetNumberCols(); col++)
		{
			QuickSetBackColor(col, oldrow, RGB(0, 0, 0)); // 恢复为黑色背景
			
			// 恢复文字颜色（需要考虑是否有特殊颜色）
			CUGCell cell;
			GetCell(col, oldrow, &cell);
			QuickSetTextColor(col, oldrow, cell.GetTextColor());
		}
	}
}

// 刷新重绘所有单元格
void CSymbolGrid::RedrawAll()
{
    // 获取当前时间
    DWORD currentTime = GetTickCount();
    
    // 如果启用防闪烁模式，且距离上次更新时间不足 100 毫秒，则忽略本次更新请求
    if (m_bAntiFlickerEnabled && (currentTime - m_lastUpdateTime) < 100)
    {
        return;
    }
    
    // 记录本次更新时间
    m_lastUpdateTime = currentTime;
    
    // 暂时禁用窗口更新
    EnableUpdate(FALSE);
    
    // 刷新表格内容
    Invalidate(TRUE);
    
    // 调用父类的RedrawAll方法刷新行和列标题
    CUGCtrl::RedrawAll();
    
    // 重新启用窗口更新
    EnableUpdate(TRUE);
}


