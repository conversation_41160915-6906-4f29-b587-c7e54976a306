﻿// SymbolView.cpp: CSymbolView 类的实现
//

#include "pch.h"
#include "..\framework.h"
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "SymbolView.h"
#include "..\MainFrm.h"
#include <vector>
#include <algorithm>
#include <random>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// CSymbolView

IMPLEMENT_DYNCREATE(CSymbolView, CView)

BEGIN_MESSAGE_MAP(CSymbolView, CView)
	ON_WM_LBUTTONDOWN()
	ON_WM_CREATE()
	ON_WM_SIZE()
	ON_WM_DESTROY()
	ON_WM_ERASEBKGND()
END_MESSAGE_MAP()

// CSymbolView 构造/析构

CSymbolView::CSymbolView() noexcept
{
	// 初始化参数
	m_nSymbolBarHeight = 30;   // 导航栏高度
}

CSymbolView::~CSymbolView()
{
	// 网格控件的销毁在OnDestroy中处理
}

// 获取文档指针
CStockDoc* CSymbolView::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

BOOL CSymbolView::PreCreateWindow(CREATESTRUCT& cs)
{
	// 设置无边框样式
	cs.style &= ~WS_BORDER;       // 移除边框
	cs.dwExStyle &= ~WS_EX_CLIENTEDGE; // 移除扩展边框样式
	
	return CView::PreCreateWindow(cs);
}

// CSymbolView 绘图
void CSymbolView::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;
	
	// 使用分割窗口显示，不需要在此绘制内容
	// 各子窗口会自行处理绘制
}

// 初始化视图
void CSymbolView::OnInitialUpdate()
{
	CView::OnInitialUpdate();

	// 配置网格控件样式
	if (::IsWindow(m_SymbolGrid.GetSafeHwnd()))
	{
		//// 启用防闪烁模式
		m_SymbolGrid.EnableAntiFlicker(true);
		
		// 设置选项卡高度和颜色
		m_SymbolGrid.SetTabHeight(24);
		
		// 设置选项卡样式 - 普通状态
		for (int i = 0; i < 6; i++)
		{
			m_SymbolGrid.SetTabTextColor(i, RGB(200, 200, 200));
			m_SymbolGrid.SetTabBackColor(i, RGB(40, 40, 40));
			
			// 设置选项卡选中状态样式
			m_SymbolGrid.SetTabHBackColor(i, RGB(128, 0, 0));
			m_SymbolGrid.SetTabHTextColor(i, RGB(255, 255, 0));
		}
	}

}

// CSymbolView 消息处理程序
int CSymbolView::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CView::OnCreate(lpCreateStruct) == -1)
		return -1;

	// 获取客户区大小
	CRect rect;
	GetClientRect(rect);
	
	// 创建静态分割窗口，2行1列
	if (!m_wndSplitter.CreateStatic(this, 2, 1))
	{
		TRACE0("Failed to create splitter window\n");
		return FALSE;
	}
	// 创建创建上下文
	CCreateContext context;
	context.m_pNewViewClass = nullptr;
	context.m_pCurrentDoc = GetDocument();
	context.m_pNewDocTemplate = nullptr;
	context.m_pLastView = nullptr;
	context.m_pCurrentFrame = nullptr;
	// 创建SymbolBar窗口
	m_wndSplitter.AddView(0, 0, RUNTIME_CLASS(CSymbolBar), &context);

    // 创建网格控件
    if (!m_SymbolGrid.CreateGrid(WS_CHILD | WS_VISIBLE, CRect(0, 0, 0, 0), &m_wndSplitter, m_wndSplitter.IdFromRowCol(1, 0)))
    {
        TRACE0("Failed to create SymbolGrid window\n");
        return FALSE;
    }
	// 设置行高度
	m_wndSplitter.SetRowInfo(0, m_nSymbolBarHeight, 0);  // 导航栏固定高度30像素
	
	// 重新计算分割窗口布局
	m_wndSplitter.SetLocked(TRUE);  // 锁定分割条，防止用户调整
	m_wndSplitter.RecalcLayout();
	return 0;
}

void CSymbolView::OnDestroy()
{
	// 调用基类的OnDestroy进行正常清理
	CView::OnDestroy();
	
	// 确保分割窗口已经销毁或安全释放
	if (::IsWindow(m_wndSplitter.GetSafeHwnd()))
	{
		// 销毁分割窗口中的所有子窗口
		//m_wndSplitter.DestroyWindow();
	}
}

void CSymbolView::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);

	// 调整分割窗口大小
	if (::IsWindow(m_wndSplitter.GetSafeHwnd()))
	{
		m_wndSplitter.MoveWindow(0, 0, cx, cy);
		
		// 确保导航栏高度固定
		int cyCur, cyMin;
		m_wndSplitter.GetRowInfo(0, cyCur, cyMin);
		if (cyCur != m_nSymbolBarHeight)
		{
			m_wndSplitter.SetRowInfo(0, m_nSymbolBarHeight, 0);
			
			m_wndSplitter.RecalcLayout();
		}
	}
}

// 处理背景擦除，设置黑色背景
BOOL CSymbolView::OnEraseBkgnd(CDC* pDC)
{
	CRect rect;
	GetClientRect(rect);
	
	// 使用黑色填充背景
	pDC->FillSolidRect(rect, RGB(0, 0, 0));
	
	return TRUE; // 背景已处理
}

