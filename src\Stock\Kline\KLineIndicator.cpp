﻿// KLineIndicator.cpp: CKLineIndicator 类的实现
//

#include "pch.h"
#include "..\framework.h"
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "KLineIndicator.h"
#include "..\MainFrm.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CKLineIndicator

IMPLEMENT_DYNCREATE(CKLineIndicator, CView)

BEGIN_MESSAGE_MAP(CKLineIndicator, CView)
	ON_WM_ERASEBKGND()
	ON_WM_SIZE()
	ON_WM_LBUTTONDOWN()
	ON_WM_KEYDOWN()
END_MESSAGE_MAP()

// CKLineIndicator 构造/析构

CKLineIndicator::CKLineIndicator() noexcept
{
	// 初始化股票代码
	m_strCode = _T("600000");
	
	// 默认选择MACD指标
	m_nCurrentIndicator = 0;
}

CKLineIndicator::~CKLineIndicator()
{
}

// 获取文档指针
CStockDoc* CKLineIndicator::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 设置股票代码
void CKLineIndicator::SetStockCode(const CString& strCode)
{
	TRACE(_T("CKLineIndicator::SetStockCode - 设置股票代码: %s\n"), strCode);
	m_strCode = strCode;
	
	// 加载K线数据
	m_vecKLine.clear();
	LoadKLineDataFromLocalFile(m_strCode, m_vecKLine);
	TRACE(_T("CKLineIndicator::SetStockCode - 加载了 %d 条K线数据\n"), (int)m_vecKLine.size());
	
	// 重绘视图
	Invalidate();
}

// 响应文档更新
void CKLineIndicator::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 调用基类的OnUpdate方法
	CView::OnUpdate(pSender, lHint, pHint);
	
	// 从文档获取当前股票代码
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
		
		// 如果当前显示的股票代码与文档中的不同，则更新
		if (m_strCode != strCurrentStock)
		{
			// 设置新的股票代码，这会触发视图更新
			SetStockCode(strCurrentStock);
			
			TRACE(_T("CKLineIndicator::OnUpdate - 股票代码已更新: %s\n"), strCurrentStock);
		}
	}
}

BOOL CKLineIndicator::PreCreateWindow(CREATESTRUCT& cs)
{
	// 移除窗口边框
	cs.style &= ~WS_BORDER;
	return CView::PreCreateWindow(cs);
}

// 初始化视图
void CKLineIndicator::OnInitialUpdate()
{
	CView::OnInitialUpdate();
}

// CKLineIndicator 绘图
void CKLineIndicator::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;
	
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 设置背景为黑色
	pDC->FillSolidRect(rectClient, RGB(0, 0, 0));
	
	// 指标选择区域的高度
	int nSelectorHeight = 30;
	
	// 计算指标显示区域 - 从顶部到底部选择区域的上方
	CRect rcIndicator = rectClient;
	rcIndicator.bottom -= nSelectorHeight;  // 预留底部空间给选择区域
	
	// 根据当前选择的指标绘制相应的图形
	switch (m_nCurrentIndicator)
	{
	case 0:  // MACD
		DrawMACD(pDC, rcIndicator);
		break;
	case 1:  // RSI
		DrawRSI(pDC, rcIndicator);
		break;
	case 2:  // KDJ
		DrawKDJ(pDC, rcIndicator);
		break;
	case 3:  // BOLL
		DrawBOLL(pDC, rcIndicator);
		break;
	case 4:  // BBI
		DrawBBI(pDC, rcIndicator);
		break;
	}
	
	// 绘制指标选择区域 - 现在在底部绘制
	DrawIndicatorSelector(pDC);
}

// 绘制指标选择区域
void CKLineIndicator::DrawIndicatorSelector(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 定义选择区域 - 现在在底部
	int nSelectorHeight = 30;
	CRect rcSelector(rectClient.left, rectClient.bottom - nSelectorHeight, 
	                rectClient.right, rectClient.bottom);
	
	// 绘制背景
	pDC->FillSolidRect(rcSelector, RGB(30, 30, 30));
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(200, 200, 200));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建字体
	CFont font;
	font.CreateFont(16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, 
	               DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	               DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&font);
	
	// 定义每个选项的区域 - 固定宽度为100像素
	int itemWidth = 100; // 固定宽度为100像素
	int startX = rectClient.left;
	
	// 定义各个指标的选择区域
	CRect rcMACD(startX, rcSelector.top, startX + itemWidth, rcSelector.bottom);
	startX += itemWidth;
	
	CRect rcRSI(startX, rcSelector.top, startX + itemWidth, rcSelector.bottom);
	startX += itemWidth;
	
	CRect rcKDJ(startX, rcSelector.top, startX + itemWidth, rcSelector.bottom);
	startX += itemWidth;
	
	CRect rcBOLL(startX, rcSelector.top, startX + itemWidth, rcSelector.bottom);
	startX += itemWidth;
	
	CRect rcBBI(startX, rcSelector.top, startX + itemWidth, rcSelector.bottom);
	
	// 绘制选项
	pDC->DrawText(_T("MACD"), rcMACD, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	pDC->DrawText(_T("RSI"), rcRSI, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	pDC->DrawText(_T("KDJ"), rcKDJ, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	pDC->DrawText(_T("BOLL"), rcBOLL, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	pDC->DrawText(_T("BBI"), rcBBI, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制选中项的高亮
	CPen penHighlight(PS_SOLID, 2, RGB(0, 162, 232));  // 蓝色高亮
	CPen* pOldPen = pDC->SelectObject(&penHighlight);
	
	// 根据当前选择的指标绘制上划线（因为在底部，所以使用上划线）
	CRect rcHighlight;
	switch (m_nCurrentIndicator)
	{
	case 0:  // MACD
		rcHighlight = rcMACD;
		break;
	case 1:  // RSI
		rcHighlight = rcRSI;
		break;
	case 2:  // KDJ
		rcHighlight = rcKDJ;
		break;
	case 3:  // BOLL
		rcHighlight = rcBOLL;
		break;
	case 4:  // BBI
		rcHighlight = rcBBI;
		break;
	}
	
	pDC->MoveTo(rcHighlight.left + 10, rcHighlight.top + 2);
	pDC->LineTo(rcHighlight.right - 10, rcHighlight.top + 2);
	
	// 恢复原笔和字体
	pDC->SelectObject(pOldPen);
	
	// 绘制选项之间的垂直分隔线
	CPen separatorPen(PS_DOT, 1, RGB(70, 70, 70));  // 深灰色分隔线
	pDC->SelectObject(&separatorPen);
	
	// 绘制垂直分隔线，在每个选项之间
	for (int i = 1; i <= 4; i++) {
		int x = rectClient.left + i * itemWidth;
		pDC->MoveTo(x, rcSelector.top + 2);  // 留出2像素的上边距
		pDC->LineTo(x, rcSelector.bottom - 2);  // 留出2像素的下边距
	}
	
	// 绘制分隔线 - 在选择区域顶部
	CPen linePen(PS_DOT, 1, RGB(50, 50, 50));
	pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rcSelector.left, rcSelector.top);
	pDC->LineTo(rcSelector.right, rcSelector.top);
	
	// 恢复原笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 绘制MACD指标
void CKLineIndicator::DrawMACD(CDC* pDC, const CRect& rcArea)
{
	// 检查K线数据是否可用
	if (m_vecKLine.empty()) {
		// 显示无数据提示
		pDC->SetTextColor(RGB(255, 255, 255));
		pDC->SetBkMode(TRANSPARENT);
		CString strNoData;
		strNoData.Format(_T("股票 %s 无K线数据"), m_strCode);
		pDC->TextOut(rcArea.left + 10, rcArea.top + 10, strNoData);
		TRACE(_T("MACD: 股票 %s K线数据为空\n"), m_strCode);
		return;
	}
	
	const std::vector<KLINE_DATA>& klineData = m_vecKLine;
	int nDataCount = klineData.size();
	if (nDataCount < 26) return; // MACD需要至少26天数据
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 计算MACD指标数据
	std::vector<double> ema12(nDataCount), ema26(nDataCount);
	std::vector<double> dif(nDataCount), dea(nDataCount), macd(nDataCount);
	
	// 计算EMA12和EMA26
	double alpha12 = 2.0 / (12 + 1);
	double alpha26 = 2.0 / (26 + 1);
	double alpha9 = 2.0 / (9 + 1);
	
	// 初始化EMA值
	ema12[0] = klineData[0]._Close;
	ema26[0] = klineData[0]._Close;
	
	for (int i = 1; i < nDataCount; i++) {
		ema12[i] = alpha12 * klineData[i]._Close + (1 - alpha12) * ema12[i-1];
		ema26[i] = alpha26 * klineData[i]._Close + (1 - alpha26) * ema26[i-1];
		dif[i] = ema12[i] - ema26[i];
	}
	
	// 计算DEA (DIF的9日EMA)
	dea[0] = dif[0];
	for (int i = 1; i < nDataCount; i++) {
		dea[i] = alpha9 * dif[i] + (1 - alpha9) * dea[i-1];
		macd[i] = 2 * (dif[i] - dea[i]);
	}
	
	// 定义区域
	int nInfoHeight = 30;
	CRect rcInfo(rcArea.left, rcArea.top, rcArea.right, rcArea.top + nInfoHeight);
	CRect rcChart(rcArea.left, rcInfo.bottom, rcArea.right, rcArea.bottom);
	
	// 创建标题字体并绘制标题
	CFont titleFont;
	titleFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
	                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 显示当前MACD值
	CString strTitle;
	if (nDataCount > 0) {
		strTitle.Format(_T("MACD(12,26,9) DIF:%.3f DEA:%.3f MACD:%.3f"), 
			dif[nDataCount-1], dea[nDataCount-1], macd[nDataCount-1]);
	} else {
		strTitle = _T("MACD(12,26,9)");
	}
	
	CRect rcTitle(rcInfo.left + 10, rcInfo.top, rcInfo.right - 10, rcInfo.bottom);
	pDC->DrawText(strTitle, rcTitle, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制分隔线
	CPen separatorPen(PS_DOT, 1, RGB(128, 0, 0));
	CPen* pOldPen = pDC->SelectObject(&separatorPen);
	pDC->MoveTo(rcArea.left, rcInfo.bottom);
	pDC->LineTo(rcArea.right, rcInfo.bottom);
	
	// 绘制网格
	CPen gridPen(PS_SOLID, 1, RGB(30, 30, 30));
	pDC->SelectObject(&gridPen);
	
	// 水平网格线
	int yStep = rcChart.Height() / 4;
	for (int i = 1; i < 4; i++) {
		int y = rcChart.top + i * yStep;
		pDC->MoveTo(rcChart.left, y);
		pDC->LineTo(rcChart.right, y);
	}
	
	// 零轴线 (中间线加粗)
	CPen zeroLinePen(PS_SOLID, 1, RGB(100, 100, 100));
	pDC->SelectObject(&zeroLinePen);
	int yZero = rcChart.top + rcChart.Height() / 2;
	pDC->MoveTo(rcChart.left, yZero);
	pDC->LineTo(rcChart.right, yZero);
	
	// 计算显示范围和比例
	int nDisplayCount = min(60, nDataCount); // 显示最近60根K线
	int nStartIndex = max(0, nDataCount - nDisplayCount);
	
	// 找出MACD值的最大最小值用于缩放
	double maxVal = -999999, minVal = 999999;
	for (int i = nStartIndex; i < nDataCount; i++) {
		maxVal = max(maxVal, max(dif[i], max(dea[i], macd[i])));
		minVal = min(minVal, min(dif[i], min(dea[i], macd[i])));
	}
	
	if (maxVal == minVal) {
		maxVal += 0.1;
		minVal -= 0.1;
	}
	
	double range = maxVal - minVal;
	double yScale = rcChart.Height() / range;
	
	// 绘制MACD柱状图
	CPen macdUpPen(PS_SOLID, 1, RGB(255, 0, 0));    // 红色上涨
	CPen macdDownPen(PS_SOLID, 1, RGB(0, 255, 0));  // 绿色下跌
	CBrush macdUpBrush(RGB(255, 0, 0));
	CBrush macdDownBrush(RGB(0, 255, 0));
	
	int barWidth = max(1, rcChart.Width() / nDisplayCount - 1);
	
	for (int i = nStartIndex; i < nDataCount; i++) {
		int x = rcChart.left + (i - nStartIndex) * rcChart.Width() / nDisplayCount;
		int yMacd = yZero - (int)((macd[i] - 0) * yScale / 2);  // MACD相对于零轴
		
		if (macd[i] >= 0) {
			pDC->SelectObject(&macdUpPen);
			pDC->SelectObject(&macdUpBrush);
		} else {
			pDC->SelectObject(&macdDownPen);
			pDC->SelectObject(&macdDownBrush);
		}
		
		CRect barRect(x - barWidth/2, min(yZero, yMacd), x + barWidth/2, max(yZero, yMacd));
		pDC->Rectangle(barRect);
	}
	
	// 绘制DIF线 (白色)
	CPen difPen(PS_SOLID, 1, RGB(255, 255, 255));
	pDC->SelectObject(&difPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / nDisplayCount;
		int y1 = rcChart.bottom - (int)((dif[i-1] - minVal) * yScale);
		int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / nDisplayCount;
		int y2 = rcChart.bottom - (int)((dif[i] - minVal) * yScale);
		
		pDC->MoveTo(x1, y1);
		pDC->LineTo(x2, y2);
	}
	
	// 绘制DEA线 (黄色)
	CPen deaPen(PS_SOLID, 1, RGB(255, 255, 0));
	pDC->SelectObject(&deaPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / nDisplayCount;
		int y1 = rcChart.bottom - (int)((dea[i-1] - minVal) * yScale);
		int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / nDisplayCount;
		int y2 = rcChart.bottom - (int)((dea[i] - minVal) * yScale);
		
		pDC->MoveTo(x1, y1);
		pDC->LineTo(x2, y2);
	}
	
	// 恢复画笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 绘制RSI指标
void CKLineIndicator::DrawRSI(CDC* pDC, const CRect& rcArea)
{
	// 检查K线数据是否可用
	if (m_vecKLine.empty()) {
		// 显示无数据提示
		pDC->SetTextColor(RGB(255, 255, 255));
		pDC->SetBkMode(TRANSPARENT);
		CString strNoData;
		strNoData.Format(_T("股票 %s 无K线数据"), m_strCode);
		pDC->TextOut(rcArea.left + 10, rcArea.top + 10, strNoData);
		return;
	}
	
	const std::vector<KLINE_DATA>& klineData = m_vecKLine;
	int nDataCount = klineData.size();
	if (nDataCount < 24) return; // RSI需要足够数据
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 计算RSI指标 (6日、12日、24日)
	std::vector<double> rsi6(nDataCount), rsi12(nDataCount), rsi24(nDataCount);
	
	// 计算RSI的函数
	auto calculateRSI = [&](int period, std::vector<double>& rsi) {
		for (int i = period; i < nDataCount; i++) {
			double avgGain = 0.0, avgLoss = 0.0;
			
			// 计算初始平均涨跌
			for (int j = i - period + 1; j <= i; j++) {
				double change = klineData[j]._Close - klineData[j-1]._Close;
				if (change > 0) avgGain += change;
				else avgLoss += (-change);
			}
			
			avgGain /= period;
			avgLoss /= period;
			
			// 计算RS和RSI
			if (avgLoss != 0) {
				double rs = avgGain / avgLoss;
				rsi[i] = 100.0 - (100.0 / (1.0 + rs));
			} else {
				rsi[i] = 100.0;
			}
		}
	};
	
	calculateRSI(6, rsi6);
	calculateRSI(12, rsi12); 
	calculateRSI(24, rsi24);
	
	// 定义区域
	int nInfoHeight = 30;
	CRect rcInfo(rcArea.left, rcArea.top, rcArea.right, rcArea.top + nInfoHeight);
	CRect rcChart(rcArea.left, rcInfo.bottom, rcArea.right, rcArea.bottom);
	
	// 创建标题字体并绘制标题
	CFont titleFont;
	titleFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
	                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 显示当前RSI值
	CString strTitle;
	if (nDataCount > 24) {
		strTitle.Format(_T("RSI(6,12,24) RSI6:%.1f RSI12:%.1f RSI24:%.1f"), 
			rsi6[nDataCount-1], rsi12[nDataCount-1], rsi24[nDataCount-1]);
	} else {
		strTitle = _T("RSI(6,12,24)");
	}
	
	CRect rcTitle(rcInfo.left + 10, rcInfo.top, rcInfo.right - 10, rcInfo.bottom);
	pDC->DrawText(strTitle, rcTitle, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制分隔线
	CPen separatorPen(PS_DOT, 1, RGB(128, 0, 0));
	CPen* pOldPen = pDC->SelectObject(&separatorPen);
	pDC->MoveTo(rcArea.left, rcInfo.bottom);
	pDC->LineTo(rcArea.right, rcInfo.bottom);
	
	// 绘制网格
	CPen gridPen(PS_SOLID, 1, RGB(30, 30, 30));
	pDC->SelectObject(&gridPen);
	
	// 绘制水平网格线，RSI范围是0-100
	for (int i = 1; i < 4; i++) {
		int y = rcChart.top + i * rcChart.Height() / 4;
		pDC->MoveTo(rcChart.left, y);
		pDC->LineTo(rcChart.right, y);
	}
	
	// 绘制重要水平线 (超买超卖线)
	CPen importantLinePen(PS_DOT, 1, RGB(100, 100, 100));
	pDC->SelectObject(&importantLinePen);
	
	// 超买线 (70%)
	int y70 = rcChart.bottom - (int)(0.7 * rcChart.Height());
	pDC->MoveTo(rcChart.left, y70);
	pDC->LineTo(rcChart.right, y70);
	
	// 超卖线 (30%)
	int y30 = rcChart.bottom - (int)(0.3 * rcChart.Height());
	pDC->MoveTo(rcChart.left, y30);
	pDC->LineTo(rcChart.right, y30);
	
	// 中轴线 (50%)
	int y50 = rcChart.bottom - (int)(0.5 * rcChart.Height());
	pDC->MoveTo(rcChart.left, y50);
	pDC->LineTo(rcChart.right, y50);
	
	// 计算显示范围
	int nDisplayCount = min(60, nDataCount);
	int nStartIndex = max(24, nDataCount - nDisplayCount); // 确保有足够数据计算RSI
	
	// 绘制RSI6线 (白色)
	CPen rsi6Pen(PS_SOLID, 1, RGB(255, 255, 255));
	pDC->SelectObject(&rsi6Pen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (rsi6[i-1] > 0 && rsi6[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)(rsi6[i-1] / 100.0 * rcChart.Height());
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)(rsi6[i] / 100.0 * rcChart.Height());
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制RSI12线 (黄色)
	CPen rsi12Pen(PS_SOLID, 1, RGB(255, 255, 0));
	pDC->SelectObject(&rsi12Pen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (rsi12[i-1] > 0 && rsi12[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)(rsi12[i-1] / 100.0 * rcChart.Height());
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)(rsi12[i] / 100.0 * rcChart.Height());
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制RSI24线 (紫色)
	CPen rsi24Pen(PS_SOLID, 1, RGB(255, 0, 255));
	pDC->SelectObject(&rsi24Pen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (rsi24[i-1] > 0 && rsi24[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)(rsi24[i-1] / 100.0 * rcChart.Height());
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)(rsi24[i] / 100.0 * rcChart.Height());
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 恢复画笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 绘制KDJ指标
void CKLineIndicator::DrawKDJ(CDC* pDC, const CRect& rcArea)
{
	// 检查K线数据是否可用
	if (m_vecKLine.empty()) {
		// 显示无数据提示
		pDC->SetTextColor(RGB(255, 255, 255));
		pDC->SetBkMode(TRANSPARENT);
		CString strNoData;
		strNoData.Format(_T("股票 %s 无K线数据"), m_strCode);
		pDC->TextOut(rcArea.left + 10, rcArea.top + 10, strNoData);
		return;
	}
	
	const std::vector<KLINE_DATA>& klineData = m_vecKLine;
	int nDataCount = klineData.size();
	if (nDataCount < 9) return; // KDJ需要至少9天数据
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 计算KDJ指标 (参数：9日周期，3日平滑)
	std::vector<double> kValue(nDataCount), dValue(nDataCount), jValue(nDataCount);
	std::vector<double> rsv(nDataCount);  // 未成熟随机值
	
	// 计算RSV (Raw Stochastic Value)
	for (int i = 8; i < nDataCount; i++) {  // 从第9天开始计算
		double highest = klineData[i]._High;
		double lowest = klineData[i]._Low;
		
		// 找出过去9天（包括今天）的最高价和最低价
		for (int j = i - 8; j <= i; j++) {
			if (klineData[j]._High > highest) highest = klineData[j]._High;
			if (klineData[j]._Low < lowest) lowest = klineData[j]._Low;
		}
		
		// 计算RSV
		if (highest != lowest) {
			rsv[i] = (klineData[i]._Close - lowest) / (highest - lowest) * 100.0;
		} else {
			rsv[i] = 50.0;  // 如果最高价等于最低价，RSV设为50
		}
	}
	
	// 计算K、D、J值 (使用平滑算法)
	kValue[8] = 50.0;  // K值初始化为50
	dValue[8] = 50.0;  // D值初始化为50
	
	for (int i = 9; i < nDataCount; i++) {
		// K = 2/3 * 前一日K值 + 1/3 * 当日RSV
		kValue[i] = (2.0/3.0) * kValue[i-1] + (1.0/3.0) * rsv[i];
		
		// D = 2/3 * 前一日D值 + 1/3 * 当日K值
		dValue[i] = (2.0/3.0) * dValue[i-1] + (1.0/3.0) * kValue[i];
		
		// J = 3K - 2D
		jValue[i] = 3.0 * kValue[i] - 2.0 * dValue[i];
	}
	
	// 定义区域
	int nInfoHeight = 30;
	CRect rcInfo(rcArea.left, rcArea.top, rcArea.right, rcArea.top + nInfoHeight);
	CRect rcChart(rcArea.left, rcInfo.bottom, rcArea.right, rcArea.bottom);
	
	// 创建标题字体并绘制标题
	CFont titleFont;
	titleFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
	                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 显示当前KDJ值
	CString strTitle;
	if (nDataCount > 9) {
		strTitle.Format(_T("KDJ(9,3,3) K:%.1f D:%.1f J:%.1f"), 
			kValue[nDataCount-1], dValue[nDataCount-1], jValue[nDataCount-1]);
	} else {
		strTitle = _T("KDJ(9,3,3)");
	}
	
	CRect rcTitle(rcInfo.left + 10, rcInfo.top, rcInfo.right - 10, rcInfo.bottom);
	pDC->DrawText(strTitle, rcTitle, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制分隔线
	CPen separatorPen(PS_DOT, 1, RGB(128, 0, 0));
	CPen* pOldPen = pDC->SelectObject(&separatorPen);
	pDC->MoveTo(rcArea.left, rcInfo.bottom);
	pDC->LineTo(rcArea.right, rcInfo.bottom);
	
	// 绘制网格
	CPen gridPen(PS_SOLID, 1, RGB(30, 30, 30));
	pDC->SelectObject(&gridPen);
	
	// 绘制水平网格线，KDJ范围通常是0-100
	for (int i = 1; i < 4; i++) {
		int y = rcChart.top + i * rcChart.Height() / 4;
		pDC->MoveTo(rcChart.left, y);
		pDC->LineTo(rcChart.right, y);
	}
	
	// 绘制重要水平线 (超买超卖线)
	CPen importantLinePen(PS_DOT, 1, RGB(100, 100, 100));
	pDC->SelectObject(&importantLinePen);
	
	// 超买线 (80%)
	int y80 = rcChart.bottom - (int)(0.8 * rcChart.Height());
	pDC->MoveTo(rcChart.left, y80);
	pDC->LineTo(rcChart.right, y80);
	
	// 超卖线 (20%)
	int y20 = rcChart.bottom - (int)(0.2 * rcChart.Height());
	pDC->MoveTo(rcChart.left, y20);
	pDC->LineTo(rcChart.right, y20);
	
	// 中轴线 (50%)
	int y50 = rcChart.bottom - (int)(0.5 * rcChart.Height());
	pDC->MoveTo(rcChart.left, y50);
	pDC->LineTo(rcChart.right, y50);
	
	// 计算显示范围
	int nDisplayCount = min(60, nDataCount);
	int nStartIndex = max(9, nDataCount - nDisplayCount); // 确保有足够数据计算KDJ
	
	// 绘制K线 (白色)
	CPen kPen(PS_SOLID, 1, RGB(255, 255, 255));
	pDC->SelectObject(&kPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y1 = rcChart.bottom - (int)(kValue[i-1] / 100.0 * rcChart.Height());
		int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y2 = rcChart.bottom - (int)(kValue[i] / 100.0 * rcChart.Height());
		
		// 确保Y坐标在有效范围内
		y1 = max(rcChart.top, min(rcChart.bottom, y1));
		y2 = max(rcChart.top, min(rcChart.bottom, y2));
		
		pDC->MoveTo(x1, y1);
		pDC->LineTo(x2, y2);
	}
	
	// 绘制D线 (黄色)
	CPen dPen(PS_SOLID, 1, RGB(255, 255, 0));
	pDC->SelectObject(&dPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y1 = rcChart.bottom - (int)(dValue[i-1] / 100.0 * rcChart.Height());
		int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y2 = rcChart.bottom - (int)(dValue[i] / 100.0 * rcChart.Height());
		
		// 确保Y坐标在有效范围内
		y1 = max(rcChart.top, min(rcChart.bottom, y1));
		y2 = max(rcChart.top, min(rcChart.bottom, y2));
		
		pDC->MoveTo(x1, y1);
		pDC->LineTo(x2, y2);
	}
	
	// 绘制J线 (紫色)
	CPen jPen(PS_SOLID, 1, RGB(255, 0, 255));
	pDC->SelectObject(&jPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y1 = rcChart.bottom - (int)(jValue[i-1] / 100.0 * rcChart.Height());
		int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y2 = rcChart.bottom - (int)(jValue[i] / 100.0 * rcChart.Height());
		
		// J线可能超出0-100范围，需要特殊处理
		y1 = max(rcChart.top, min(rcChart.bottom, y1));
		y2 = max(rcChart.top, min(rcChart.bottom, y2));
		
		pDC->MoveTo(x1, y1);
		pDC->LineTo(x2, y2);
	}
	
	// 恢复画笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 绘制BOLL指标 (布林带)
void CKLineIndicator::DrawBOLL(CDC* pDC, const CRect& rcArea)
{
	// 检查K线数据是否可用
	if (m_vecKLine.empty()) {
		// 显示无数据提示
		pDC->SetTextColor(RGB(255, 255, 255));
		pDC->SetBkMode(TRANSPARENT);
		CString strNoData;
		strNoData.Format(_T("股票 %s 无K线数据"), m_strCode);
		pDC->TextOut(rcArea.left + 10, rcArea.top + 10, strNoData);
		return;
	}
	
	const std::vector<KLINE_DATA>& klineData = m_vecKLine;
	int nDataCount = klineData.size();
	if (nDataCount < 20) return; // BOLL需要至少20天数据
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 计算BOLL指标 (20日移动平均线，2倍标准差)
	std::vector<double> mid(nDataCount), upper(nDataCount), lower(nDataCount);
	int period = 20;
	double multiple = 2.0;
	
	// 计算布林带
	for (int i = period - 1; i < nDataCount; i++) {
		// 计算20日移动平均线 (中轨)
		double sum = 0.0;
		for (int j = i - period + 1; j <= i; j++) {
			sum += klineData[j]._Close;
		}
		mid[i] = sum / period;
		
		// 计算标准差
		double variance = 0.0;
		for (int j = i - period + 1; j <= i; j++) {
			double diff = klineData[j]._Close - mid[i];
			variance += diff * diff;
		}
		double stdDev = sqrt(variance / period);
		
		// 计算上下轨
		upper[i] = mid[i] + multiple * stdDev;
		lower[i] = mid[i] - multiple * stdDev;
	}
	
	// 定义区域
	int nInfoHeight = 30;
	CRect rcInfo(rcArea.left, rcArea.top, rcArea.right, rcArea.top + nInfoHeight);
	CRect rcChart(rcArea.left, rcInfo.bottom, rcArea.right, rcArea.bottom);
	
	// 创建标题字体并绘制标题
	CFont titleFont;
	titleFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
	                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 显示当前布林带值
	CString strTitle;
	if (nDataCount > 20) {
		strTitle.Format(_T("BOLL(20,2) MID:%.2f UPPER:%.2f LOWER:%.2f"), 
			mid[nDataCount-1], upper[nDataCount-1], lower[nDataCount-1]);
	} else {
		strTitle = _T("BOLL(20,2)");
	}
	
	CRect rcTitle(rcInfo.left + 10, rcInfo.top, rcInfo.right - 10, rcInfo.bottom);
	pDC->DrawText(strTitle, rcTitle, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制分隔线
	CPen separatorPen(PS_DOT, 1, RGB(128, 0, 0));
	CPen* pOldPen = pDC->SelectObject(&separatorPen);
	pDC->MoveTo(rcArea.left, rcInfo.bottom);
	pDC->LineTo(rcArea.right, rcInfo.bottom);
	
	// 绘制网格
	CPen gridPen(PS_SOLID, 1, RGB(30, 30, 30));
	pDC->SelectObject(&gridPen);
	
	// 绘制水平网格线
	for (int i = 1; i < 4; i++) {
		int y = rcChart.top + i * rcChart.Height() / 4;
		pDC->MoveTo(rcChart.left, y);
		pDC->LineTo(rcChart.right, y);
	}
	
	// 计算显示范围和价格缩放
	int nDisplayCount = min(60, nDataCount);
	int nStartIndex = max(20, nDataCount - nDisplayCount);
	
	// 找出显示范围内的最高最低价
	double maxPrice = -999999, minPrice = 999999;
	for (int i = nStartIndex; i < nDataCount; i++) {
		maxPrice = max(maxPrice, max(upper[i], max(klineData[i]._High, mid[i])));
		minPrice = min(minPrice, min(lower[i], min(klineData[i]._Low, mid[i])));
	}
	
	if (maxPrice == minPrice) {
		maxPrice += 0.1;
		minPrice -= 0.1;
	}
	
	double priceRange = maxPrice - minPrice;
	double yScale = rcChart.Height() / priceRange;
	
	// 绘制上轨线 (黄色)
	CPen upperPen(PS_SOLID, 1, RGB(255, 255, 0));
	pDC->SelectObject(&upperPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (upper[i-1] > 0 && upper[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)((upper[i-1] - minPrice) * yScale);
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)((upper[i] - minPrice) * yScale);
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制中轨线 (白色)
	CPen midPen(PS_SOLID, 1, RGB(255, 255, 255));
	pDC->SelectObject(&midPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (mid[i-1] > 0 && mid[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)((mid[i-1] - minPrice) * yScale);
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)((mid[i] - minPrice) * yScale);
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制下轨线 (青色)
	CPen lowerPen(PS_SOLID, 1, RGB(0, 255, 255));
	pDC->SelectObject(&lowerPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (lower[i-1] > 0 && lower[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)((lower[i-1] - minPrice) * yScale);
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)((lower[i] - minPrice) * yScale);
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制K线价格线作为参考 (灰色细线)
	CPen pricePen(PS_SOLID, 1, RGB(128, 128, 128));
	pDC->SelectObject(&pricePen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y1 = rcChart.bottom - (int)((klineData[i-1]._Close - minPrice) * yScale);
		int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y2 = rcChart.bottom - (int)((klineData[i]._Close - minPrice) * yScale);
		
		pDC->MoveTo(x1, y1);
		pDC->LineTo(x2, y2);
	}
	
	// 恢复画笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 绘制BBI指标 (多空指标)
void CKLineIndicator::DrawBBI(CDC* pDC, const CRect& rcArea)
{
	// 检查K线数据是否可用
	if (m_vecKLine.empty()) {
		// 显示无数据提示
		pDC->SetTextColor(RGB(255, 255, 255));
		pDC->SetBkMode(TRANSPARENT);
		CString strNoData;
		strNoData.Format(_T("股票 %s 无K线数据"), m_strCode);
		pDC->TextOut(rcArea.left + 10, rcArea.top + 10, strNoData);
		return;
	}
	
	const std::vector<KLINE_DATA>& klineData = m_vecKLine;
	int nDataCount = klineData.size();
	if (nDataCount < 24) return; // BBI需要至少24天数据
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 计算BBI指标 (3日、6日、12日、24日移动平均线的算术平均)
	std::vector<double> ma3(nDataCount), ma6(nDataCount), ma12(nDataCount), ma24(nDataCount);
	std::vector<double> bbi(nDataCount);
	
	// 计算各周期移动平均线
	auto calculateMA = [&](int period, std::vector<double>& ma) {
		for (int i = period - 1; i < nDataCount; i++) {
			double sum = 0.0;
			for (int j = i - period + 1; j <= i; j++) {
				sum += klineData[j]._Close;
			}
			ma[i] = sum / period;
		}
	};
	
	calculateMA(3, ma3);
	calculateMA(6, ma6);
	calculateMA(12, ma12);
	calculateMA(24, ma24);
	
	// 计算BBI值
	for (int i = 23; i < nDataCount; i++) { // 从第24天开始
		bbi[i] = (ma3[i] + ma6[i] + ma12[i] + ma24[i]) / 4.0;
	}
	
	// 定义区域
	int nInfoHeight = 30;
	CRect rcInfo(rcArea.left, rcArea.top, rcArea.right, rcArea.top + nInfoHeight);
	CRect rcChart(rcArea.left, rcInfo.bottom, rcArea.right, rcArea.bottom);
	
	// 创建标题字体并绘制标题
	CFont titleFont;
	titleFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
	                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 显示当前BBI值
	CString strTitle;
	if (nDataCount > 24) {
		strTitle.Format(_T("BBI(3,6,12,24) BBI:%.2f MA3:%.2f MA6:%.2f MA12:%.2f MA24:%.2f"), 
			bbi[nDataCount-1], ma3[nDataCount-1], ma6[nDataCount-1], ma12[nDataCount-1], ma24[nDataCount-1]);
	} else {
		strTitle = _T("BBI(3,6,12,24)");
	}
	
	CRect rcTitle(rcInfo.left + 10, rcInfo.top, rcInfo.right - 10, rcInfo.bottom);
	pDC->DrawText(strTitle, rcTitle, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制分隔线
	CPen separatorPen(PS_DOT, 1, RGB(128, 0, 0));
	CPen* pOldPen = pDC->SelectObject(&separatorPen);
	pDC->MoveTo(rcArea.left, rcInfo.bottom);
	pDC->LineTo(rcArea.right, rcInfo.bottom);
	
	// 绘制网格
	CPen gridPen(PS_SOLID, 1, RGB(30, 30, 30));
	pDC->SelectObject(&gridPen);
	
	// 绘制水平网格线
	for (int i = 1; i < 4; i++) {
		int y = rcChart.top + i * rcChart.Height() / 4;
		pDC->MoveTo(rcChart.left, y);
		pDC->LineTo(rcChart.right, y);
	}
	
	// 计算显示范围和价格缩放
	int nDisplayCount = min(60, nDataCount);
	int nStartIndex = max(24, nDataCount - nDisplayCount);
	
	// 找出显示范围内的最高最低价
	double maxPrice = -999999, minPrice = 999999;
	for (int i = nStartIndex; i < nDataCount; i++) {
		maxPrice = max(maxPrice, max(bbi[i], max(ma3[i], max(ma6[i], max(ma12[i], ma24[i])))));
		minPrice = min(minPrice, min(bbi[i], min(ma3[i], min(ma6[i], min(ma12[i], ma24[i])))));
		
		// 包含K线价格作为参考
		maxPrice = max(maxPrice, klineData[i]._Close);
		minPrice = min(minPrice, klineData[i]._Close);
	}
	
	if (maxPrice == minPrice) {
		maxPrice += 0.1;
		minPrice -= 0.1;
	}
	
	double priceRange = maxPrice - minPrice;
	double yScale = rcChart.Height() / priceRange;
	
	// 绘制BBI线 (白色粗线)
	CPen bbiPen(PS_SOLID, 2, RGB(255, 255, 255));
	pDC->SelectObject(&bbiPen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (bbi[i-1] > 0 && bbi[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)((bbi[i-1] - minPrice) * yScale);
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)((bbi[i] - minPrice) * yScale);
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制MA3线 (红色)
	CPen ma3Pen(PS_SOLID, 1, RGB(255, 0, 0));
	pDC->SelectObject(&ma3Pen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (ma3[i-1] > 0 && ma3[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)((ma3[i-1] - minPrice) * yScale);
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)((ma3[i] - minPrice) * yScale);
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制MA6线 (黄色)
	CPen ma6Pen(PS_SOLID, 1, RGB(255, 255, 0));
	pDC->SelectObject(&ma6Pen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (ma6[i-1] > 0 && ma6[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)((ma6[i-1] - minPrice) * yScale);
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)((ma6[i] - minPrice) * yScale);
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制MA12线 (紫色)
	CPen ma12Pen(PS_SOLID, 1, RGB(255, 0, 255));
	pDC->SelectObject(&ma12Pen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (ma12[i-1] > 0 && ma12[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)((ma12[i-1] - minPrice) * yScale);
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)((ma12[i] - minPrice) * yScale);
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制MA24线 (青色)
	CPen ma24Pen(PS_SOLID, 1, RGB(0, 255, 255));
	pDC->SelectObject(&ma24Pen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		if (ma24[i-1] > 0 && ma24[i] > 0) {
			int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y1 = rcChart.bottom - (int)((ma24[i-1] - minPrice) * yScale);
			int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
			int y2 = rcChart.bottom - (int)((ma24[i] - minPrice) * yScale);
			
			pDC->MoveTo(x1, y1);
			pDC->LineTo(x2, y2);
		}
	}
	
	// 绘制K线价格线作为参考 (灰色细线)
	CPen pricePen(PS_SOLID, 1, RGB(128, 128, 128));
	pDC->SelectObject(&pricePen);
	
	for (int i = nStartIndex + 1; i < nDataCount; i++) {
		int x1 = rcChart.left + (i-1 - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y1 = rcChart.bottom - (int)((klineData[i-1]._Close - minPrice) * yScale);
		int x2 = rcChart.left + (i - nStartIndex) * rcChart.Width() / (nDataCount - nStartIndex);
		int y2 = rcChart.bottom - (int)((klineData[i]._Close - minPrice) * yScale);
		
		pDC->MoveTo(x1, y1);
		pDC->LineTo(x2, y2);
	}
	
	// 恢复画笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 防止闪烁
BOOL CKLineIndicator::OnEraseBkgnd(CDC* pDC)
{
	return TRUE;
}

// 处理窗口大小变化
void CKLineIndicator::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);
	
	// 强制重绘
	Invalidate();
}

// 处理鼠标左键点击
void CKLineIndicator::OnLButtonDown(UINT nFlags, CPoint point)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 检查点击是否在底部的指标选择区域内
	int nSelectorHeight = 30;
	if (point.y >= (rectClient.bottom - nSelectorHeight))
	{
		// 固定每个选项的宽度为100像素
		int itemWidth = 100;
		int index = (point.x - rectClient.left) / itemWidth;
		
		// 检查索引有效性并更新当前指标
		if (index >= 0 && index <= 4 && index != m_nCurrentIndicator)
		{
			m_nCurrentIndicator = index;
			// 重绘视图
			Invalidate();
		}
	}
	
	CView::OnLButtonDown(nFlags, point);
}

// 处理键盘消息
void CKLineIndicator::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags)
{
	// 检查是否按下回车键
	if (nChar == VK_RETURN)
	{
		// 切换到股票列表视图
		CMainFrame* pMainFrame = (CMainFrame*)AfxGetMainWnd();
		if (pMainFrame)
		{
			// 切换到股票列表视图
			pMainFrame->SwitchView(pMainFrame->GetStockListViewID());
		}
	}
	
	CView::OnKeyDown(nChar, nRepCnt, nFlags);
}

/**
 * @brief 从本地二进制文件加载K线数据
 * @param code 股票代码
 * @param klineData 用于存储加载的K线数据的向量
 * @return bool 是否成功加载数据
 */
bool CKLineIndicator::LoadKLineDataFromLocalFile(const CString& code, std::vector<KLINE_DATA>& klineData)
{
    // 清空原有数据
    klineData.clear();
    
    // 构建文件路径
    CString filePath;
    filePath.Format(_T("F:\\RedCow\\KLine\\%s.day"), code);
    
    // 使用CreateFile打开文件
    HANDLE hFile = CreateFile(
        filePath,                    // 文件路径
        GENERIC_READ,                // 读取访问权限
        FILE_SHARE_READ,             // 共享模式
        NULL,                        // 安全属性
        OPEN_EXISTING,               // 打开已存在的文件
        FILE_ATTRIBUTE_NORMAL,       // 文件属性
        NULL                         // 模板文件句柄
    );
    
    // 检查文件是否成功打开
    if (hFile == INVALID_HANDLE_VALUE)
    {
        TRACE(_T("CKLineIndicator: 无法打开K线数据文件: %s, 错误码: %d\n"), filePath, GetLastError());
        return false;
    }
    
    // 获取文件大小
    DWORD fileSize = GetFileSize(hFile, NULL);
    if (fileSize == INVALID_FILE_SIZE)
    {
        TRACE(_T("CKLineIndicator: 获取文件大小失败: %s, 错误码: %d\n"), filePath, GetLastError());
        CloseHandle(hFile);
        return false;
    }
    
    // 计算文件中包含的K线数据记录数
    DWORD recordCount = fileSize / sizeof(KLINE_DATA);
    if (recordCount == 0)
    {
        TRACE(_T("CKLineIndicator: K线数据文件为空: %s\n"), filePath);
        CloseHandle(hFile);
        return false;
    }
    
    // 分配内存用于读取数据
	KLINE_DATA* pBuffer = new KLINE_DATA[recordCount];
    if (!pBuffer)
    {
        TRACE(_T("CKLineIndicator: 内存分配失败，无法读取K线数据\n"));
        CloseHandle(hFile);
        return false;
    }
    
    // 读取文件内容
    DWORD bytesRead;
    BOOL bResult = ReadFile(
        hFile,                       // 文件句柄
        pBuffer,                     // 缓冲区
        fileSize,                    // 要读取的字节数
        &bytesRead,                  // 实际读取的字节数
        NULL                         // 重叠结构
    );
    
    // 关闭文件句柄
    CloseHandle(hFile);
    
    // 检查读取是否成功
    if (!bResult || bytesRead != fileSize)
    {
        TRACE(_T("CKLineIndicator: 读取K线数据文件失败: %s, 错误码: %d\n"), filePath, GetLastError());
        delete[] pBuffer;
        return false;
    }
    
    // 将二进制数据转换为KLINE_DATA结构
    for (DWORD i = 0; i < recordCount; i++)
    {
		KLINE_DATA& rawData = pBuffer[i];
        
        // 添加到结果向量
        klineData.push_back(rawData);
    }
    
    // 释放缓冲区
    delete[] pBuffer;
    
    TRACE(_T("CKLineIndicator: 成功从文件加载 %d 条K线数据: %s\n"), klineData.size(), filePath);
    return true;
} 