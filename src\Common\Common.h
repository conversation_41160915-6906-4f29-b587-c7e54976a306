﻿#pragma once

// https://apphq.longhuvip.com/w1/api/index.php?Order=9&st=6000&a=KanPanNew&c=YiDongKanPan&apiv=w29&Type=1
// https://d.10jqka.com.cn/v6/time/hs_1A0001/last.js		// 上证
// https://d.10jqka.com.cn/v6/time/hs_399001/last.js		// 深证
// https://d.10jqka.com.cn/v6/time/hs_399006/last.js		// 创业
// https://d.10jqka.com.cn/v6/time/hs_1B0688/last.js		// 科创50
// https://d.10jqka.com.cn/v6/time/151_899050/last.js		// 北证50


// 股票类型枚举
enum StockType {
    STOCK_TYPE_NORMAL = 0,   // 普通A股（涨跌幅限制10%）
    STOCK_TYPE_ST,           // ST股票（涨跌幅限制5%）
    STOCK_TYPE_GEM,          // 创业板（涨跌幅限制20%）
    STOCK_TYPE_STAR,         // 科创板（涨跌幅限制20%）
    STOCK_TYPE_BSE,          // 北交所股票（涨跌幅限制30%）
    STOCK_TYPE_NEW,          // 新股（首日涨幅限制44%，首日无跌幅限制）
    STOCK_TYPE_INDEX         // 指数（无涨跌幅限制）
};

// 根据股票代码和名称判断股票类型
StockType GetStockType(const CString& stockCode, const CString& stockName);

// 获取对应类型股票的涨停幅度
float GetLimitUpRate(StockType type, BOOL isFirstDay = FALSE);

// 获取对应类型股票的跌停幅度
float GetLimitDownRate(StockType type, BOOL isFirstDay = FALSE);

// 判断是否为新股
BOOL IsStockNew(const CString& stockCode);

// 判断是否为新股首日
BOOL IsStockFirstTradingDay(const CString& stockCode);

// 根据股票代码获取市场类型（新方法）
MarketType DetermineMarketInfo(const std::string& code);

// 原函数保留兼容性
void DetermineMarketInfo(const std::string& code, std::string& market, MarketType& marketType);

// 根据股票代码获取市场标识（SH/SZ/BJ）
std::string GetMarketPrefix(const std::string& code);

float  RoundUp(float  val, int n);

float CalcLimitUpPrice(std::string code, std::string name, float fPreClose);
float CalcLimitDownPrice(std::string code, std::string name, float fPreClose);
float CalcLimitUpPercent(std::string code, std::string name, float fPreClose);
float CalcLimitDownPercent(std::string code, std::string name, float fPreClose);

BOOL IsLimitUp(CString strCode, float fPreClose, float fClose);
BOOL IsLimitDown(CString strCode, float fPreClose, float fClose);
CString GetWeekString(int nWeek);