<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <ProjectGuid>{38E1663B-FE6B-436C-B5FC-CFD60BAC3D9F}</ProjectGuid>
    <Keyword>MFCProj</Keyword>
    <RootNamespace>Stock</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>NotSet</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>NotSet</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>..\Grid\Include;..\Grid\celltypes;..\</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <AdditionalLibraryDirectories>..\lib\OutD</AdditionalLibraryDirectories>
      <AdditionalDependencies>sqlite3mc.lib;zlib-1.2.11.lib;wininet.lib</AdditionalDependencies>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>..\Grid\Include;..\Grid\celltypes;..\Stock</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>..\lib\OutR</AdditionalLibraryDirectories>
      <AdditionalDependencies>sqlite3mc.lib;zlib-1.2.11.lib;wininet.lib</AdditionalDependencies>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\Common\Common.h" />
    <ClInclude Include="..\Common\StringUtil.h" />
    <ClInclude Include="..\Common\WinHttp.h" />
    <ClInclude Include="..\Common\define.h" />
    <ClInclude Include="..\Common\httpHeader.h" />
    <ClInclude Include="..\Common\JSONValue.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTafnt.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTAutoSize.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTbutn.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTDropGrid.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTdtp.h" />
    <ClInclude Include="..\Grid\CellTypes\ugctelps.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTExpand.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTLabeled.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTMail.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTMailSort.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTMarquee.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTmfnt.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTNote.h" />
    <ClInclude Include="..\Grid\CellTypes\ugctnotewnd.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTOutlookHeader.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTpie.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTpro1.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTprog.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTRado.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTsarw.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTsldr.h" />
    <ClInclude Include="..\Grid\CellTypes\UGCTSpin.h" />
    <ClInclude Include="..\Grid\Include\UG64Bit.h" />
    <ClInclude Include="..\Grid\Include\UGCBType.h" />
    <ClInclude Include="..\Grid\Include\UGCell.h" />
    <ClInclude Include="..\Grid\Include\ugceltyp.h" />
    <ClInclude Include="..\Grid\Include\UGCnrBtn.h" />
    <ClInclude Include="..\Grid\Include\UGCTarrw.h" />
    <ClInclude Include="..\Grid\Include\UGCtrl.h" />
    <ClInclude Include="..\Grid\Include\ugdefine.h" />
    <ClInclude Include="..\Grid\Include\UGDLType.h" />
    <ClInclude Include="..\Grid\Include\UGDrgDrp.h" />
    <ClInclude Include="..\Grid\Include\UGDrwHnt.h" />
    <ClInclude Include="..\Grid\Include\UGDtaSrc.h" />
    <ClInclude Include="..\Grid\Include\UGEdit.h" />
    <ClInclude Include="..\Grid\Include\UGEditBase.h" />
    <ClInclude Include="..\Grid\Include\ugformat.h" />
    <ClInclude Include="..\Grid\Include\uggdinfo.h" />
    <ClInclude Include="..\Grid\Include\UGGrid.h" />
    <ClInclude Include="..\Grid\Include\UGHint.h" />
    <ClInclude Include="..\Grid\Include\ughscrol.h" />
    <ClInclude Include="..\Grid\Include\uglstbox.h" />
    <ClInclude Include="..\Grid\Include\UGMEdit.h" />
    <ClInclude Include="..\Grid\Include\UGMemMan.h" />
    <ClInclude Include="..\Grid\Include\UGMultiS.h" />
    <ClInclude Include="..\Grid\Include\ugprint.h" />
    <ClInclude Include="..\Grid\Include\ugptrlst.h" />
    <ClInclude Include="..\Grid\Include\ugsidehd.h" />
    <ClInclude Include="..\Grid\Include\UGStrOp.h" />
    <ClInclude Include="..\Grid\Include\ugstruct.h" />
    <ClInclude Include="..\Grid\Include\ugtab.h" />
    <ClInclude Include="..\Grid\Include\UGTopHdg.h" />
    <ClInclude Include="..\Grid\Include\ugvscrol.h" />
    <ClInclude Include="..\Grid\Include\ugxpthemes.h" />
    <ClInclude Include="..\inc\sqlite3mc.h" />
    <ClInclude Include="ColorDef.h" />
    <ClInclude Include="framework.h" />
    <ClInclude Include="Kline\KLineIndicator.h" />
    <ClInclude Include="Kline\KLineInfo.h" />
    <ClInclude Include="Kline\KLineSignal.h" />
    <ClInclude Include="Kline\KLineTime.h" />
    <ClInclude Include="Kline\KLineView.h" />
    <ClInclude Include="Kline\KLineVolume.h" />
    <ClInclude Include="LoadingDlg.h" />
    <ClInclude Include="MainFrm.h" />
    <ClInclude Include="NetData.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="Stock.h" />
    <ClInclude Include="StockDef.h" />
    <ClInclude Include="StockDoc.h" />
    <ClInclude Include="StockSplitter.h" />
    <ClInclude Include="StockView.h" />
    <ClInclude Include="Symbol\SymbolBar.h" />
    <ClInclude Include="Symbol\SymbolGrid.h" />
    <ClInclude Include="Symbol\SymbolView.h" />
    <ClInclude Include="TabPage.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="Time\TimeInfo.h" />
    <ClInclude Include="Time\TimeLine.h" />
    <ClInclude Include="Time\TimeView.h" />
    <ClInclude Include="StockStatusBar.h" />
    <ClInclude Include="ColumnConfigDlg.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Common\Common.cpp" />
    <ClCompile Include="..\Common\httpHeader.cpp" />
    <ClCompile Include="..\Common\Json.cpp" />
    <ClCompile Include="..\Common\JSONValue.cpp" />
    <ClCompile Include="..\Common\WinHttp.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTafnt.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTAutoSize.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTbutn.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTDropGrid.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTdtp.cpp" />
    <ClCompile Include="..\Grid\CellTypes\ugctelps.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTExpand.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTLabeled.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTMail.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTMailSort.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTMarquee.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTmfnt.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTNote.cpp" />
    <ClCompile Include="..\Grid\CellTypes\ugctnotewnd.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTOutlookHeader.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTpie.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTpro1.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTprog.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTRado.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTsarw.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTsldr.cpp" />
    <ClCompile Include="..\Grid\CellTypes\UGCTSpin.cpp" />
    <ClCompile Include="..\Grid\Source\UGCBType.cpp" />
    <ClCompile Include="..\Grid\Source\UGCell.cpp" />
    <ClCompile Include="..\Grid\Source\UGCelTyp.cpp" />
    <ClCompile Include="..\Grid\Source\UGCnrBtn.cpp" />
    <ClCompile Include="..\Grid\Source\UGCTarrw.cpp" />
    <ClCompile Include="..\Grid\Source\UGCtrl.cpp" />
    <ClCompile Include="..\Grid\Source\UGDLType.cpp" />
    <ClCompile Include="..\Grid\Source\UGDrgDrp.cpp" />
    <ClCompile Include="..\Grid\Source\UGDrwHnt.cpp" />
    <ClCompile Include="..\Grid\Source\UGDtaSrc.cpp" />
    <ClCompile Include="..\Grid\Source\UGEdit.cpp" />
    <ClCompile Include="..\Grid\Source\UGEditBase.cpp" />
    <ClCompile Include="..\Grid\Source\ugformat.cpp" />
    <ClCompile Include="..\Grid\Source\uggdinfo.cpp" />
    <ClCompile Include="..\Grid\Source\UGGrid.cpp" />
    <ClCompile Include="..\Grid\Source\UGHint.cpp" />
    <ClCompile Include="..\Grid\Source\ughscrol.cpp" />
    <ClCompile Include="..\Grid\Source\ugLstBox.cpp" />
    <ClCompile Include="..\Grid\Source\UGMEdit.cpp" />
    <ClCompile Include="..\Grid\Source\UGMemMan.cpp" />
    <ClCompile Include="..\Grid\Source\UGMultiS.cpp" />
    <ClCompile Include="..\Grid\Source\ugprint.cpp" />
    <ClCompile Include="..\Grid\Source\ugptrlst.cpp" />
    <ClCompile Include="..\Grid\Source\ugsidehd.cpp" />
    <ClCompile Include="..\Grid\Source\UGStrOp.cpp" />
    <ClCompile Include="..\Grid\Source\ugtab.cpp" />
    <ClCompile Include="..\Grid\Source\UGTopHdg.cpp" />
    <ClCompile Include="..\Grid\Source\ugvscrol.cpp" />
    <ClCompile Include="..\Grid\Source\UGXPThemes.cpp" />
    <ClCompile Include="Kline\KLineIndicator.cpp" />
    <ClCompile Include="Kline\KLineInfo.cpp" />
    <ClCompile Include="Kline\KLineSignal.cpp" />
    <ClCompile Include="Kline\KLineTime.cpp" />
    <ClCompile Include="Kline\KLineView.cpp" />
    <ClCompile Include="Kline\KLineVolume.cpp" />
    <ClCompile Include="LoadingDlg.cpp" />
    <ClCompile Include="MainFrm.cpp" />
    <ClCompile Include="NetData.cpp" />
    <ClCompile Include="pch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Stock.cpp" />
    <ClCompile Include="StockDoc.cpp" />
    <ClCompile Include="StockSplitter.cpp" />
    <ClCompile Include="StockView.cpp" />
    <ClCompile Include="Symbol\SymbolBar.cpp" />
    <ClCompile Include="Symbol\SymbolGrid.cpp" />
    <ClCompile Include="Symbol\SymbolView.cpp" />
    <ClCompile Include="TabPage.cpp" />
    <ClCompile Include="StockStatusBar.cpp" />
    <ClCompile Include="Time\TimeInfo.cpp" />
    <ClCompile Include="Time\TimeLine.cpp" />
    <ClCompile Include="Time\TimeView.cpp" />
    <ClCompile Include="ColumnConfigDlg.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Stock.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="res\Stock.rc2" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\Stock.ico" />
    <Image Include="res\StockDoc.ico" />
    <Image Include="res\Toolbar.bmp" />
  </ItemGroup>
  <ItemGroup>
    <Manifest Include="res\Stock.manifest" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>