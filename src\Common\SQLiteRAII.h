﻿#pragma once
#include "pch.h"
#include <stdexcept>
#include <string>

// RAII包装类用于管理SQLite数据库连接
class SQLiteDB {
private:
    sqlite3* db_;
    
public:
    explicit SQLiteDB(const char* filename) : db_(nullptr) {
        if (sqlite3_open(filename, &db_) != SQLITE_OK) {
            std::string error = "无法打开数据库: ";
            if (db_) {
                error += sqlite3_errmsg(db_);
                sqlite3_close(db_);
            }
            throw std::runtime_error(error);
        }
    }
    
    ~SQLiteDB() {
        if (db_) {
            sqlite3_close(db_);
        }
    }
    
    // 禁用拷贝构造和赋值
    SQLiteDB(const SQLiteDB&) = delete;
    SQLiteDB& operator=(const SQLiteDB&) = delete;
    
    // 支持移动语义
    SQLiteDB(SQLiteDB&& other) noexcept : db_(other.db_) {
        other.db_ = nullptr;
    }
    
    SQLiteDB& operator=(SQLiteDB&& other) noexcept {
        if (this != &other) {
            if (db_) {
                sqlite3_close(db_);
            }
            db_ = other.db_;
            other.db_ = nullptr;
        }
        return *this;
    }
    
    sqlite3* get() const { return db_; }
    operator sqlite3*() const { return db_; }
    
    // 执行SQL语句
    void exec(const char* sql) {
        char* errMsg = nullptr;
        if (sqlite3_exec(db_, sql, nullptr, nullptr, &errMsg) != SQLITE_OK) {
            std::string error = "SQL执行失败: ";
            error += errMsg;
            sqlite3_free(errMsg);
            throw std::runtime_error(error);
        }
    }
    
    // 开始事务
    void beginTransaction() {
        exec("BEGIN TRANSACTION");
    }
    
    // 提交事务
    void commitTransaction() {
        exec("COMMIT");
    }
    
    // 回滚事务
    void rollbackTransaction() {
        exec("ROLLBACK");
    }
};

// RAII包装类用于管理SQLite预编译语句
class SQLiteStmt {
private:
    sqlite3_stmt* stmt_;
    
public:
    SQLiteStmt(sqlite3* db, const char* sql) : stmt_(nullptr) {
        if (sqlite3_prepare_v2(db, sql, -1, &stmt_, nullptr) != SQLITE_OK) {
            std::string error = "预编译语句失败: ";
            error += sqlite3_errmsg(db);
            throw std::runtime_error(error);
        }
    }
    
    ~SQLiteStmt() {
        if (stmt_) {
            sqlite3_finalize(stmt_);
        }
    }
    
    // 禁用拷贝构造和赋值
    SQLiteStmt(const SQLiteStmt&) = delete;
    SQLiteStmt& operator=(const SQLiteStmt&) = delete;
    
    // 支持移动语义
    SQLiteStmt(SQLiteStmt&& other) noexcept : stmt_(other.stmt_) {
        other.stmt_ = nullptr;
    }
    
    SQLiteStmt& operator=(SQLiteStmt&& other) noexcept {
        if (this != &other) {
            if (stmt_) {
                sqlite3_finalize(stmt_);
            }
            stmt_ = other.stmt_;
            other.stmt_ = nullptr;
        }
        return *this;
    }
    
    sqlite3_stmt* get() const { return stmt_; }
    operator sqlite3_stmt*() const { return stmt_; }
    
    // 绑定参数的便利方法
    void bindText(int index, const char* text) {
        sqlite3_bind_text(stmt_, index, text, -1, SQLITE_TRANSIENT);
    }
    
    void bindInt(int index, int value) {
        sqlite3_bind_int(stmt_, index, value);
    }
    
    void bindDouble(int index, double value) {
        sqlite3_bind_double(stmt_, index, value);
    }
    
    // 执行语句
    int step() {
        return sqlite3_step(stmt_);
    }
    
    // 重置语句
    void reset() {
        sqlite3_reset(stmt_);
        sqlite3_clear_bindings(stmt_);
    }
};

// RAII事务管理类
class SQLiteTransaction {
private:
    SQLiteDB& db_;
    bool committed_;
    
public:
    explicit SQLiteTransaction(SQLiteDB& db) : db_(db), committed_(false) {
        db_.beginTransaction();
    }
    
    ~SQLiteTransaction() {
        if (!committed_) {
            try {
                db_.rollbackTransaction();
            } catch (...) {
                // 析构函数中不抛出异常
            }
        }
    }
    
    void commit() {
        db_.commitTransaction();
        committed_ = true;
    }
    
    void rollback() {
        if (!committed_) {
            db_.rollbackTransaction();
            committed_ = true;
        }
    }
};
