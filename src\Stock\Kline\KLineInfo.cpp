﻿// KLineInfo.cpp: CKLineInfo 类的实现
//

#include "pch.h"
#include "..\framework.h"
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "KLineInfo.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CKLineInfo

IMPLEMENT_DYNCREATE(CKLineInfo, CView)

BEGIN_MESSAGE_MAP(CKLineInfo, CView)
	ON_WM_ERASEBKGND()
	ON_WM_SIZE()
	ON_WM_TIMER()
	ON_WM_LBUTTONDOWN()
	ON_WM_MOUSEMOVE()
	ON_WM_MOUSEWHEEL()
END_MESSAGE_MAP()

// CKLineInfo 构造/析构

CKLineInfo::CKLineInfo() noexcept
{
	// 初始化成员变量
	m_strCode = _T("");
	m_strName = _T("");
	
	// 初始化K线数据
	m_dateCurrent = CTime::GetCurrentTime();
	m_dblOpen = 0.0;
	m_dblClose = 0.0;
	m_dblHigh = 0.0;
	m_dblLow = 0.0;
	m_dblVolume = 0.0;
	m_dblAmount = 0.0;
	m_dblChange = 0.0;
	m_dblChangePercent = 0.0;
	
	// 初始化扩展数据
	m_dblPreClose = 0.0;
	m_dblVolumeRatio = 0.0;
	m_dblTurnover = 0.0;
	m_nBuyVolume = 0;
	m_nSellVolume = 0;
	m_dblCirculatingValue = 0.0;
	m_dblTotalValue = 0.0;
	m_dblMainInflow = 0.0;
	m_dblMainOutflow = 0.0;
	m_dblCirculatingShares = 0.0;
	m_dblTotalShares = 0.0;
	m_dblPE = 0.0;
	m_dblPETTM = 0.0;
	
	// 初始化滚动相关变量
	m_nIndustryScrollPos = 0;
	m_nIndustryTargetScrollPos = 0;
	m_dblIndustryScrollOffset = 0.0;
	
	m_nAlertScrollPos = 0;
	m_nAlertTargetScrollPos = 0;
	m_dblAlertScrollOffset = 0.0;
	
	m_dwLastUpdateTime = 0;
	
	// 初始化几个示例预警信息
	KLAlertInfo alert1;
	alert1.strType = _T("突破");
	alert1.strReason = _T("最高价突破5日均线");
	alert1.strTime = _T("14:28:35");
	alert1.bPositive = TRUE;
	alert1.strName = _T("测试股票A");
	alert1.strCode = _T("600001");
	m_vecAlerts.push_back(alert1);
	
	KLAlertInfo alert2;
	alert2.strType = _T("MACD");
	alert2.strReason = _T("MACD金叉，买入信号");
	alert2.strTime = _T("13:56:10");
	alert2.bPositive = TRUE;
	alert2.strName = _T("测试股票B");
	alert2.strCode = _T("600002");
	m_vecAlerts.push_back(alert2);
	
	KLAlertInfo alert3;
	alert3.strType = _T("KDJ");
	alert3.strReason = _T("KDJ超买，注意获利了结");
	alert3.strTime = _T("11:32:05");
	alert3.bPositive = FALSE;
	alert3.strName = _T("测试股票C");
	alert3.strCode = _T("600003");
	m_vecAlerts.push_back(alert3);
	
	KLAlertInfo alert4;
	alert4.strType = _T("均线");
	alert4.strReason = _T("跌破10日均线，观察处理");
	alert4.strTime = _T("10:45:20");
	alert4.bPositive = FALSE;
	alert4.strName = _T("测试股票D");
	alert4.strCode = _T("600004");
	m_vecAlerts.push_back(alert4);
	
	KLAlertInfo alert5;
	alert5.strType = _T("成交量");
	alert5.strReason = _T("放量突破前期高点");
	alert5.strTime = _T("09:48:55");
	alert5.bPositive = TRUE;
	alert5.strName = _T("测试股票E");
	alert5.strCode = _T("600005");
	m_vecAlerts.push_back(alert5);
	
	// 防止鼠标初始位置非法
	m_ptLastMouse.x = -1;
	m_ptLastMouse.y = -1;
}

CKLineInfo::~CKLineInfo()
{
}

// 获取文档指针
CStockDoc* CKLineInfo::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 设置股票代码和名称
void CKLineInfo::SetStockInfo(const CString& strCode, const CString& strName)
{
	m_strCode = strCode;
	m_strName = strName;
	
	// 重绘视图
	Invalidate();
}

// 响应文档更新
void CKLineInfo::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 调用基类的OnUpdate方法
	CView::OnUpdate(pSender, lHint, pHint);
	
	// 从文档获取当前股票代码和名称
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
		CString strCurrentName = CString(pDoc->GetStockName(std::string(strCurrentStock)).c_str());
		
		// 如果当前显示的股票代码与文档中的不同，则更新
		if (m_strCode != strCurrentStock)
		{
			// 设置新的股票信息，这会触发视图更新
			SetStockInfo(strCurrentStock, strCurrentName);
			
			TRACE(_T("CKLineInfo::OnUpdate - 股票信息已更新: %s (%s)\n"), strCurrentStock, strCurrentName);
		}
	}
}

// 设置当前K线数据
void CKLineInfo::SetCurrentKLineData(const CTime& date, double open, double close, 
                                    double high, double low, double volume, double amount)
{
	m_dateCurrent = date;
	m_dblOpen = open;
	m_dblClose = close;
	m_dblHigh = high;
	m_dblLow = low;
	m_dblVolume = volume;
	m_dblAmount = amount;
	
	// 计算涨跌额和涨跌幅
	m_dblChange = close - open;
	if (open > 0)
		m_dblChangePercent = m_dblChange / open * 100.0;
	else
		m_dblChangePercent = 0.0;
	
	// 重绘视图
	Invalidate();
}

// 添加行业概念数据
void CKLineInfo::AddIndustryData(const CString& strType, const CString& strName, 
                                 const CString& strChange, BOOL bPositive)
{
	IndustryData data;
	data.strType = strType;
	data.strName = strName;
	data.strChange = strChange;
	data.clrChange = bPositive ? RGB(255, 0, 0) : RGB(0, 255, 0);
	
	m_vecIndustryData.push_back(data);
	
	// 重绘视图
	Invalidate();
}

// 清空行业概念数据
void CKLineInfo::ClearIndustryData()
{
	m_vecIndustryData.clear();
	
	// 重绘视图
	Invalidate();
}

BOOL CKLineInfo::PreCreateWindow(CREATESTRUCT& cs)
{
	// 移除窗口边框
	cs.style &= ~WS_BORDER;
	return CView::PreCreateWindow(cs);
}

// 初始化视图
void CKLineInfo::OnInitialUpdate()
{
	CView::OnInitialUpdate();
	
	// 先更新一次股票数据
	UpdateStockData();
	
	// 启动用于更新数据的定时器 - 更改为2秒更新一次，避免过于频繁刷新
	SetTimer(TIMER_DATA_UPDATE, 2000, NULL);
	
	// 启动预警自动滚动定时器
	SetTimer(TIMER_ALERT_SCROLL, 5000, NULL); // 每5秒滚动一次
}

// CKLineInfo 绘图
void CKLineInfo::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;
	
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 创建内存DC和位图用于双缓冲绘图
	CDC memDC;
	memDC.CreateCompatibleDC(pDC);
	CBitmap memBitmap;
	memBitmap.CreateCompatibleBitmap(pDC, rectClient.Width(), rectClient.Height());
	CBitmap* pOldBitmap = memDC.SelectObject(&memBitmap);
	
	// 设置背景为黑色
	memDC.FillSolidRect(rectClient, RGB(0, 0, 0));
	
	// 绘制基本信息区域到内存DC
	DrawBasicInfo(&memDC);
	
	// 绘制价格信息区域到内存DC
	DrawPriceInfo(&memDC);
	
	// 绘制行业概念区域到内存DC
	DrawIndustryInfo(&memDC);
	
	// 绘制预警区域到内存DC
	DrawAlertInfo(&memDC);
	
	// 将内存DC的内容复制到屏幕DC，一次性更新显示
	pDC->BitBlt(0, 0, rectClient.Width(), rectClient.Height(), &memDC, 0, 0, SRCCOPY);
	
	// 清理资源
	memDC.SelectObject(pOldBitmap);
	memBitmap.DeleteObject();
}

// 绘制基本信息
void CKLineInfo::DrawBasicInfo(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建标题字体，调小字体大小
	CFont titleFont;
	titleFont.CreateFont(28, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 选择字体
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 顶部区域，减小高度
	CRect rcTopArea(rectClient.left + 10, rectClient.top + 8, 
				 rectClient.right - 10, rectClient.top + 45);
	
	// 绘制股票名称（左对齐）
	CRect rcName = rcTopArea;
	rcName.right = rcName.left + rectClient.Width() / 3;
	pDC->DrawText(m_strName, rcName, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制股票代码（居中对齐）
	CRect rcCode = rcTopArea;
	rcCode.left = rcName.right;
	rcCode.right = rcCode.left + rectClient.Width() / 3;
	pDC->DrawText(m_strCode, rcCode, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制涨幅（右对齐）
	CRect rcChange = rcTopArea;
	rcChange.left = rcCode.right;
	CString strChangePercent;
	strChangePercent.Format(_T("%+.2f%%"), m_dblChangePercent);
	
	// 根据涨跌设置颜色
	COLORREF clrChange = (m_dblChange > 0) ? RGB(255, 0, 0) : 
						  (m_dblChange < 0) ? RGB(0, 255, 0) : RGB(255, 255, 255);
	pDC->SetTextColor(clrChange);
	pDC->DrawText(strChangePercent, rcChange, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	
	// 恢复文本颜色
	pDC->SetTextColor(RGB(255, 255, 255));
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
	
	// 绘制标题下方的深红色分隔线
	CPen linePen(PS_SOLID, 2, RGB(192, 0, 0));
	CPen* pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rectClient.left + 10, rcTopArea.bottom + 5);
	pDC->LineTo(rectClient.right - 10, rcTopArea.bottom + 5);
	
	// 恢复原笔
	pDC->SelectObject(pOldPen);
}

// 绘制价格信息
void CKLineInfo::DrawPriceInfo(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 获取文档对象以访问股票数据
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;
		
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(220, 220, 220));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建标签字体
	CFont labelFont;
	labelFont.CreateFont(22, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, 
					    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					    DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 创建数据字体
	CFont dataFont;
	dataFont.CreateFont(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					  DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					  DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 起始Y坐标（从分割线下方开始）
	int startY = 70;
	int rowHeight = 30;
	int firstColWidth = 80;
	int secondColWidth = 120;
	int colGap = 10;
	
	// 左右两栏的起始X坐标
	int leftColX = rectClient.left + 10;
	int rightColX = rectClient.left + rectClient.Width() / 2 + 10;
	
	// 根据数据计算格式化字符串
	CString strPrice, strChange, strChangePercent, strOpen, strHigh, strLow, strPreClose;
	CString strVolume, strAmount, strAmplitude, strVolumeRatio, strTurnover;
	CString strBuyVolume, strSellVolume, strCirculatingValue, strTotalValue;
	CString strCirculatingShares, strTotalShares, strPE, strPETTM;
	
	// 格式化当前价格（保留两位小数）
	strPrice.Format(_T("%.2f"), m_dblClose);
	
	// 格式化涨跌额（包含正负号，保留两位小数）
	strChange.Format(_T("%+.2f"), m_dblChange);
	
	// 格式化涨跌幅（包含正负号和百分号，保留两位小数）
	strChangePercent.Format(_T("%+.2f%%"), m_dblChangePercent);
	
	// 格式化开盘价和昨收价（保留两位小数）
	strOpen.Format(_T("%.2f"), m_dblOpen);
	strPreClose.Format(_T("%.2f"), m_dblPreClose);
	
	// 格式化最高价和最低价（保留两位小数）
	strHigh.Format(_T("%.2f"), m_dblHigh);
	strLow.Format(_T("%.2f"), m_dblLow);
	
	// 格式化振幅（保留两位小数，加百分号）
	double amplitude = (m_dblHigh - m_dblLow) / m_dblPreClose * 100.0;
	strAmplitude.Format(_T("%.2f%%"), amplitude);
	
	// 格式化量比（保留两位小数）
	strVolumeRatio.Format(_T("%.2f"), m_dblVolumeRatio);
	
	// 格式化换手率（保留两位小数，加百分号）
	strTurnover.Format(_T("%.2f%%"), m_dblTurnover);
	
	// 格式化成交量（单位转换：手）
	double dblVolumeHand = m_dblVolume / 100.0;
	if (dblVolumeHand >= 10000) {
		strVolume.Format(_T("%.2f万"), dblVolumeHand / 10000.0);
	} else {
		strVolume.Format(_T("%.0f"), dblVolumeHand);
	}
	
	// 格式化成交额（单位转换：万、亿）
	if (m_dblAmount >= 100000000) {
		strAmount.Format(_T("%.2f亿"), m_dblAmount / 100000000.0);
	} else if (m_dblAmount >= 10000) {
		strAmount.Format(_T("%.2f万"), m_dblAmount / 10000.0);
	} else {
		strAmount.Format(_T("%.0f"), m_dblAmount);
	}
	
	// 格式化内盘和外盘（单位：手）
	if (m_nBuyVolume >= 10000) {
		strBuyVolume.Format(_T("%.2f万"), m_nBuyVolume / 10000.0);
	} else {
		strBuyVolume.Format(_T("%d"), m_nBuyVolume);
	}
	
	if (m_nSellVolume >= 10000) {
		strSellVolume.Format(_T("%.2f万"), m_nSellVolume / 10000.0);
	} else {
		strSellVolume.Format(_T("%d"), m_nSellVolume);
	}
	
	// 格式化市值（单位：亿）
	strCirculatingValue.Format(_T("%.2f亿"), m_dblCirculatingValue);
	strTotalValue.Format(_T("%.2f亿"), m_dblTotalValue);
	
	// 格式化股本（单位：亿）
	strCirculatingShares.Format(_T("%.2f亿"), m_dblCirculatingShares);
	strTotalShares.Format(_T("%.2f亿"), m_dblTotalShares);
	
	// 格式化市盈率
	if (m_dblPE > 0)
		strPE.Format(_T("%.2f"), m_dblPE);
	else
		strPE = _T("--");
		
	if (m_dblPETTM > 0)
		strPETTM.Format(_T("%.2f"), m_dblPETTM);
	else
		strPETTM = _T("--");
	
	// 计算涨停和跌停价格
	float limitUpPrice = m_dblPreClose * 1.1f;
	float limitDownPrice = m_dblPreClose * 0.9f;
	CString strLimitUp, strLimitDown;
	strLimitUp.Format(_T("%.2f"), limitUpPrice);
	strLimitDown.Format(_T("%.2f"), limitDownPrice);
	
	// 确定价格颜色（根据涨跌）
	COLORREF clrPrice = (m_dblChange > 0) ? RGB(255, 0, 0) : 
	                    (m_dblChange < 0) ? RGB(0, 255, 0) : 
						RGB(255, 255, 255);
	
	// 所有需要绘制的信息项
	struct InfoItem {
		int colX;           // 所在列的X坐标
		int row;            // 行号
		CString label;      // 标签文本
		CString value;      // 值文本
		COLORREF color;     // 值的颜色
	};
	
	// 定义所有需要绘制的信息项
	InfoItem infoItems[] = {
		// 第一列数据
		{leftColX, 0, _T("最新"), strPrice, clrPrice},
		{leftColX, 1, _T("涨跌"), strChange, clrPrice},
		{leftColX, 2, _T("涨幅"), strChangePercent, clrPrice},
		{leftColX, 3, _T("振幅"), strAmplitude, RGB(0, 255, 255)},
		{leftColX, 4, _T("总手"), strVolume, RGB(0, 255, 255)},
		{leftColX, 5, _T("金额"), strAmount, RGB(0, 255, 255)},
		{leftColX, 6, _T("涨停"), strLimitUp, RGB(255, 0, 0)},
		{leftColX, 7, _T("外盘"), strBuyVolume, RGB(255, 0, 0)},
		{leftColX, 8, _T("总市值"), strTotalValue, RGB(0, 255, 255)},
		{leftColX, 9, _T("总股本"), strTotalShares, RGB(0, 255, 255)},
		
		// 第二列数据
		{rightColX, 0, _T("开盘"), strOpen, (m_dblOpen > m_dblPreClose) ? RGB(255, 0, 0) : (m_dblOpen < m_dblPreClose) ? RGB(0, 255, 0) : RGB(255, 255, 255)},
		{rightColX, 1, _T("最高"), strHigh, (m_dblHigh > m_dblPreClose) ? RGB(255, 0, 0) : (m_dblHigh < m_dblPreClose) ? RGB(0, 255, 0) : RGB(255, 255, 255)},
		{rightColX, 2, _T("最低"), strLow, (m_dblLow > m_dblPreClose) ? RGB(255, 0, 0) : (m_dblLow < m_dblPreClose) ? RGB(0, 255, 0) : RGB(255, 255, 255)},
		{rightColX, 3, _T("量比"), strVolumeRatio, (m_dblVolumeRatio > 1.0) ? RGB(255, 0, 0) : RGB(0, 255, 0)},
		{rightColX, 4, _T("昨收"), strPreClose, RGB(0, 255, 255)},
		{rightColX, 5, _T("换手"), strTurnover, RGB(0, 255, 255)},
		{rightColX, 6, _T("跌停"), strLimitDown, RGB(0, 255, 0)},
		{rightColX, 7, _T("内盘"), strSellVolume, RGB(0, 255, 0)},
		{rightColX, 8, _T("流通值"), strCirculatingValue, RGB(0, 255, 255)},
		{rightColX, 9, _T("流通股"), strCirculatingShares, RGB(0, 255, 255)},
	};
	
	// 绘制所有信息项
	int itemCount = sizeof(infoItems) / sizeof(infoItems[0]);
	for (int i = 0; i < itemCount; i++) {
		int itemY = startY + infoItems[i].row * rowHeight;
		
		// 绘制标签
		pDC->SetTextColor(RGB(180, 180, 180)); // 标签使用浅灰色
		pDC->SelectObject(&labelFont);
		CRect rcLabel(infoItems[i].colX, itemY, infoItems[i].colX + firstColWidth, itemY + rowHeight);
		pDC->DrawText(infoItems[i].label, rcLabel, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
		
		// 绘制数值（使用对应的颜色）
		pDC->SetTextColor(infoItems[i].color);
		pDC->SelectObject(&dataFont);
		CRect rcValue(infoItems[i].colX + firstColWidth, itemY, infoItems[i].colX + firstColWidth + secondColWidth, itemY + rowHeight);
		pDC->DrawText(infoItems[i].value, rcValue, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	}
	
	// 最后一行显示市盈率信息，单独绘制
	int lastLineY = startY + 10 * rowHeight;
	pDC->SetTextColor(RGB(180, 180, 180));
	pDC->SelectObject(&labelFont);
	pDC->TextOut(leftColX, lastLineY, _T("市盈率"));
	pDC->SetTextColor(RGB(0, 255, 255));
	pDC->SelectObject(&dataFont);
	pDC->TextOut(leftColX + firstColWidth, lastLineY, strPE);
	
	pDC->SetTextColor(RGB(180, 180, 180));
	pDC->SelectObject(&labelFont);
	pDC->TextOut(rightColX, lastLineY, _T("TTM"));
	pDC->SetTextColor(RGB(0, 255, 255));
	pDC->SelectObject(&dataFont);
	pDC->TextOut(rightColX + firstColWidth, lastLineY, strPETTM);
	
	// 添加底部的分隔线
	int bottomY = startY + 11 * rowHeight + 5;
	
	CPen linePen(PS_SOLID, 2, RGB(100, 100, 100));
	CPen* pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rectClient.left + 10, bottomY);
	pDC->LineTo(rectClient.right - 10, bottomY);
	
	// 恢复原笔
	pDC->SelectObject(pOldPen);
}

// 绘制行业概念信息
void CKLineInfo::DrawIndustryInfo(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 获取价格信息区域底部位置
	int priceInfoBottom = 70 + 11 * 30 + 10; // startY + 11*rowHeight + 间隔
	
	// 行业概念区高度 (总高度减去价格区和顶部，再除以2)
	int industryHeight = (rectClient.Height() - priceInfoBottom) / 2;
	
	// 行业概念区域
	CRect rcIndustry(rectClient.left + 10, priceInfoBottom, 
				   rectClient.right - 10, priceInfoBottom + industryHeight);
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建标题字体
	CFont titleFont;
	titleFont.CreateFont(22, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 创建内容字体
	CFont contentFont;
	contentFont.CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					     DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					     DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 选择字体
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	// 计算可用宽度并分配列宽
	int availableWidth = rcIndustry.Width();
	
	// 三个字段的列宽比例： 类别:名称:涨幅 = 2:5:3
	int totalUnits = 10;
	int categoryWidth = availableWidth * 2 / totalUnits; // 类别
	int nameWidth = availableWidth * 5 / totalUnits;     // 名称
	int changeWidth = availableWidth * 3 / totalUnits;   // 涨幅
	
	// 绘制表头
	int headerHeight = 35;
	CRect rcHeader(rcIndustry.left, rcIndustry.top + 5, rcIndustry.right, rcIndustry.top + 5 + headerHeight);
	
	// 表头背景 - 使用稍深的黑色
	pDC->FillSolidRect(rcHeader, RGB(0, 0, 0));
	
	// 计算各列起始位置
	int col1Start = rcIndustry.left;               // 类别
	int col2Start = col1Start + categoryWidth;     // 名称
	int col3Start = col2Start + nameWidth;         // 涨幅
	
	// 类别表头（左对齐）
	CRect rcCategoryHeader(col1Start, rcHeader.top, col2Start, rcHeader.bottom);
	pDC->SetTextColor(RGB(180, 180, 180));
	pDC->DrawText(_T("类别"), rcCategoryHeader, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 名称表头（左对齐）
	CRect rcNameHeader(col2Start, rcHeader.top, col3Start, rcHeader.bottom);
	pDC->DrawText(_T("名称"), rcNameHeader, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 涨幅表头（右对齐）
	CRect rcChangeHeader(col3Start, rcHeader.top, rcIndustry.right, rcHeader.bottom);
	pDC->DrawText(_T("涨幅"), rcChangeHeader, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制表头下方的分隔线
	CPen linePen(PS_SOLID, 1, RGB(100, 100, 100));
	CPen* pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rcIndustry.left, rcHeader.bottom);
	pDC->LineTo(rcIndustry.right, rcHeader.bottom);
	
	// 添加分隔线下方的保护区域 (固定高度5像素)
	int protectionHeight = 5;
	CRect rcProtection(rcIndustry.left, rcHeader.bottom, rcIndustry.right, rcHeader.bottom + protectionHeight);
	pDC->FillSolidRect(rcProtection, RGB(0, 0, 0));
	
	// 设置行高
	int rowHeight = 26;
	int headerBottom = rcHeader.bottom + protectionHeight; // 包括保护区域
	
	// 定义内容区域 - 从表头(含保护区域)下方开始
	CRect rcContent(rcIndustry.left, headerBottom, rcIndustry.right, rcIndustry.bottom);
	
	// 在内容区域绘制剪切区域，确保内容不会溢出到表头
	pDC->IntersectClipRect(rcContent);
	
	// 获取当前鼠标位置以确定悬停行
	int hoverItem = HitTestIndustryItem(m_ptLastMouse);
	
	// 绘制数据行
	int rowCount = (int)m_vecIndustryData.size();
	int maxRows = (rcContent.Height()) / rowHeight;
	maxRows = min(maxRows, rowCount);
	
	// 确保滚动位置合法
	if (m_nIndustryScrollPos < 0) {
		m_nIndustryScrollPos = 0;
		m_nIndustryTargetScrollPos = 0;
		m_dblIndustryScrollOffset = 0.0;
	}
	else if (m_nIndustryScrollPos > rowCount - maxRows && rowCount > maxRows) {
		m_nIndustryScrollPos = rowCount - maxRows;
		m_nIndustryTargetScrollPos = m_nIndustryScrollPos;
		m_dblIndustryScrollOffset = 0.0;
	}
	
	// 计算平滑滚动的像素偏移量
	int pixelOffset = -(int)(m_dblIndustryScrollOffset * rowHeight);
	
	// 可能需要多绘制一行以实现平滑滚动效果
	int extraRowsNeeded = m_dblIndustryScrollOffset > 0 ? 1 : 0;
	
	// 使用滚动位置，绘制可见的行
	for (int i = 0; i < maxRows + extraRowsNeeded; i++) {
		int dataIndex = i + m_nIndustryScrollPos;
		if (dataIndex >= rowCount) {
			break; // 超出数据范围
		}
		
		int rowTop = headerBottom + i * rowHeight + pixelOffset;
		const IndustryData& data = m_vecIndustryData[dataIndex];
		
		// 判断当前行是否被鼠标悬停
		bool isHovered = (i == hoverItem);
		
		// 只绘制在可见区域内的行
		if (rowTop + rowHeight > headerBottom && rowTop < rcIndustry.bottom) {
			// 绘制背景（悬停行使用高亮背景，交替行使用深色背景）
			if (isHovered) {
				pDC->FillSolidRect(CRect(rcIndustry.left, rowTop, rcIndustry.right, rowTop + rowHeight), RGB(40, 40, 80));
			} 
			else if (dataIndex % 2 == 1) { // 注意这里是基于数据索引而不是视图索引
				pDC->FillSolidRect(CRect(rcIndustry.left, rowTop, rcIndustry.right, rowTop + rowHeight), RGB(15, 15, 15));
			}
			
			// 类别（左对齐）
			CRect rcCategory(col1Start, rowTop, col2Start, rowTop + rowHeight);
			// 根据类型设置不同颜色
			pDC->SetTextColor(data.strType == _T("行业") ? 
				(isHovered ? RGB(255, 255, 128) : RGB(255, 255, 0)) : 
				(isHovered ? RGB(128, 255, 255) : RGB(0, 255, 255)));
			pDC->DrawText(data.strType, rcCategory, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
			
			// 名称（左对齐）
			CRect rcName(col2Start, rowTop, col3Start, rowTop + rowHeight);
			pDC->SetTextColor(isHovered ? RGB(220, 220, 220) : RGB(255, 255, 255));
			pDC->DrawText(data.strName, rcName, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
			
			// 涨幅（右对齐）
			CRect rcChange(col3Start, rowTop, rcIndustry.right, rowTop + rowHeight);
			pDC->SetTextColor(isHovered ? 
				(data.clrChange == RGB(255, 0, 0) ? RGB(255, 128, 128) : RGB(128, 255, 128)) : 
				data.clrChange);
			pDC->DrawText(data.strChange, rcChange, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
		}
	}
	
	// 重置剪切区域
	pDC->SelectClipRgn(NULL);
	
	// 绘制底部分隔线
	pDC->MoveTo(rcIndustry.left, rcIndustry.bottom);
	pDC->LineTo(rcIndustry.right, rcIndustry.bottom);
	
	// 恢复原笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 绘制预警信息
void CKLineInfo::DrawAlertInfo(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 获取行业概念区域底部位置
	int priceInfoBottom = 70 + 11 * 30 + 10; // startY + 11*rowHeight + 间隔
	int industryHeight = (rectClient.Height() - priceInfoBottom) / 2;
	int alertTop = priceInfoBottom + industryHeight;
	
	// 预警区域
	CRect rcAlert(rectClient.left + 10, alertTop, 
				rectClient.right - 10, rectClient.bottom - 10);
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建内容字体
	CFont contentFont;
	contentFont.CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					     DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					     DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 创建表头字体
	CFont headerFont;
	headerFont.CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
					    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
					    DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	// 选择字体
	CFont* pOldFont = pDC->SelectObject(&headerFont);
	
	// 计算可用宽度并分配列宽
	int availableWidth = rcAlert.Width(); // 使用全部宽度
	
	// 四个字段按1:1:1:1比例分配宽度
	int colWidth = availableWidth / 4; 
	
	// 绘制表头
	int headerHeight = 35;
	CRect rcHeader(rcAlert.left, rcAlert.top + 5, rcAlert.right, rcAlert.top + 5 + headerHeight);
	
	// 表头背景 - 使用稍深的黑色
	pDC->FillSolidRect(rcHeader, RGB(0, 0, 0));
	
	// 计算各列起始位置（调整列顺序）
	int col1Start = rcAlert.left; // 预警时间
	int col2Start = col1Start + colWidth; // 股票名称
	int col3Start = col2Start + colWidth; // 股票代码
	int col4Start = col3Start + colWidth; // 预警原因
	
	// 时间表头（左对齐），改名为"时间"
	CRect rcTimeHeader(col1Start, rcHeader.top, col2Start, rcHeader.bottom);
	pDC->SetTextColor(RGB(180, 180, 180));
	pDC->DrawText(_T("时间"), rcTimeHeader, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 股票名称表头（左对齐）
	CRect rcNameHeader(col2Start, rcHeader.top, col3Start, rcHeader.bottom);
	pDC->DrawText(_T("股票名称"), rcNameHeader, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 股票代码表头（居中对齐）
	CRect rcCodeHeader(col3Start, rcHeader.top, col4Start, rcHeader.bottom);
	pDC->DrawText(_T("股票代码"), rcCodeHeader, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	
	// 预警原因表头（右对齐）
	CRect rcReasonHeader(col4Start, rcHeader.top, rcAlert.right, rcHeader.bottom);
	pDC->DrawText(_T("预警原因"), rcReasonHeader, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制表头下方的分隔线
	CPen linePen(PS_SOLID, 1, RGB(100, 100, 100));
	CPen* pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rcAlert.left, rcHeader.bottom);
	pDC->LineTo(rcAlert.right, rcHeader.bottom);
	
	// 添加分隔线下方的保护区域 (固定高度5像素)
	int protectionHeight = 5;
	CRect rcProtection(rcAlert.left, rcHeader.bottom, rcAlert.right, rcHeader.bottom + protectionHeight);
	pDC->FillSolidRect(rcProtection, RGB(0, 0, 0));
	
	// 设置行高
	int rowHeight = 26; // 减小行高
	int headerBottom = rcHeader.bottom + protectionHeight; // 包括保护区域
	
	// 定义内容区域 - 从表头(含保护区域)下方开始
	CRect rcContent(rcAlert.left, headerBottom, rcAlert.right, rcAlert.bottom);
	
	// 在内容区域绘制剪切区域，确保内容不会溢出到表头
	pDC->IntersectClipRect(rcContent);
	
	int maxRows = rcContent.Height() / rowHeight;
	maxRows = min(maxRows, (int)m_vecAlerts.size());
	
	// 确保滚动位置合法
	if (m_nAlertScrollPos < 0) {
		m_nAlertScrollPos = 0;
		m_nAlertTargetScrollPos = 0;
		m_dblAlertScrollOffset = 0.0;
	}
	else if (m_nAlertScrollPos > (int)m_vecAlerts.size() - maxRows && (int)m_vecAlerts.size() > maxRows) {
		m_nAlertScrollPos = (int)m_vecAlerts.size() - maxRows;
		m_nAlertTargetScrollPos = m_nAlertScrollPos;
		m_dblAlertScrollOffset = 0.0;
	}
	
	// 计算平滑滚动的像素偏移量
	int pixelOffset = -(int)(m_dblAlertScrollOffset * rowHeight);
	
	// 可能需要多绘制一行以实现平滑滚动效果
	int extraRowsNeeded = m_dblAlertScrollOffset > 0 ? 1 : 0;
	
	// 切换到内容字体
	pDC->SelectObject(&contentFont);
	
	// 获取当前鼠标位置以确定悬停行
	int hoverItem = HitTestAlertItem(m_ptLastMouse);
	
	// 绘制预警数据
	for (int i = 0; i < maxRows + extraRowsNeeded; i++) {
		int dataIndex = i + m_nAlertScrollPos;
		if (dataIndex >= (int)m_vecAlerts.size()) {
			break; // 超出数据范围
		}
		
		const KLAlertInfo& alert = m_vecAlerts[dataIndex];
		int rowTop = headerBottom + i * rowHeight + pixelOffset;
		
		// 判断当前行是否被鼠标悬停
		bool isHovered = (i == hoverItem);
		
		// 只绘制在可见区域内的行
		if (rowTop + rowHeight > headerBottom && rowTop < rcAlert.bottom) {
			// 如果是悬停行，绘制背景高亮
			if (isHovered) {
				pDC->FillSolidRect(CRect(rcAlert.left, rowTop, rcAlert.right, rowTop + rowHeight), RGB(40, 40, 80));
			}
			// 非悬停行保持黑色背景
			else {
				pDC->FillSolidRect(CRect(rcAlert.left, rowTop, rcAlert.right, rowTop + rowHeight), RGB(0, 0, 0));
			}
			
			// 时间（左对齐）
			CRect rcTime(col1Start, rowTop, col2Start, rowTop + rowHeight);
			// 使用浅灰色显示时间
			pDC->SetTextColor(isHovered ? RGB(200, 200, 200) : RGB(160, 160, 160));
			// 这里使用固定时间，实际应用中应该从alert对象中获取
			pDC->DrawText(alert.strTime, rcTime, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
			
			// 股票名称（左对齐）
			CRect rcName(col2Start, rowTop, col3Start, rowTop + rowHeight);
			// 悬停时使用更亮的黄色
			pDC->SetTextColor(isHovered ? RGB(255, 255, 128) : RGB(255, 255, 0));
			pDC->DrawText(alert.strName, rcName, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
			
			// 股票代码（居中对齐）
			CRect rcCode(col3Start, rowTop, col4Start, rowTop + rowHeight);
			// 悬停时使用更亮的青色
			pDC->SetTextColor(isHovered ? RGB(128, 255, 255) : RGB(0, 255, 255));
			pDC->DrawText(alert.strCode, rcCode, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
			
			// 预警原因 (直接放在最右侧，右对齐)
			CRect rcReason(col4Start, rowTop, rcAlert.right, rowTop + rowHeight);
			// 悬停时使用更亮的红色
			pDC->SetTextColor(isHovered ? RGB(255, 128, 128) : RGB(255, 0, 0));
			pDC->DrawText(alert.strReason, rcReason, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
		}
	}
	
	// 重置剪切区域
	pDC->SelectClipRgn(NULL);
	
	// 恢复原笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 防止闪烁
BOOL CKLineInfo::OnEraseBkgnd(CDC* pDC)
{
	// 在双缓冲绘图模式下，直接返回TRUE，不擦除背景
	// 这避免了双重擦除引起的闪烁
	return TRUE;
}

// 处理窗口大小变化
void CKLineInfo::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);
	
	// 强制重绘
	Invalidate();
}

// 处理定时器消息，用于预警区域滚动和平滑滚动
void CKLineInfo::OnTimer(UINT_PTR nIDEvent)
{
	switch (nIDEvent)
	{
	case TIMER_ALERT_SCROLL:
		// 自动滚动预警列表
		if (!m_vecAlerts.empty())
		{
			m_nAlertTargetScrollPos = (m_nAlertTargetScrollPos + 1) % m_vecAlerts.size();
			StartSmoothScroll();
		}
		break;
		
	case TIMER_SMOOTH_SCROLL:
		// 平滑滚动动画
		UpdateSmoothScroll();
		break;
		
	case TIMER_DATA_UPDATE:
		// 更新股票数据 - 只有当股票代码不为空时才更新
		if (!m_strCode.IsEmpty())
		{
			UpdateStockData();
		}
		break;
	}

	CView::OnTimer(nIDEvent);
}

// 处理鼠标左键点击
void CKLineInfo::OnLButtonDown(UINT nFlags, CPoint point)
{
	// 检查点击是否在预警区域内的某个条目上
	int itemIndex = HitTestAlertItem(point);
	if (itemIndex != -1) {
		// 点击有效，获取实际数据索引
		int dataIndex = itemIndex + m_nAlertScrollPos;
		if (dataIndex < (int)m_vecAlerts.size()) {
			const KLAlertInfo& alert = m_vecAlerts[dataIndex];
			
			// 弹出消息框显示点击的股票信息
			CString message;
			message.Format(_T("您点击了股票：%s (%s)\n预警原因：%s"), 
						  alert.strName, alert.strCode, alert.strReason);
			MessageBox(message, _T("股票链接"), MB_ICONINFORMATION);
			
			// 这里可以添加跳转到详细股票页面的代码
			// 例如：GetDocument()->ViewStockDetail(alert.strCode);
		}
	}
	
	// 检查点击是否在行业概念区域内的某个条目上
	int industryIndex = HitTestIndustryItem(point);
	if (industryIndex != -1) {
		// 点击有效，获取实际数据索引
		int dataIndex = industryIndex + m_nIndustryScrollPos;
		if (dataIndex < (int)m_vecIndustryData.size()) {
			const IndustryData& data = m_vecIndustryData[dataIndex];
			
			// 弹出消息框显示点击的行业概念信息
			CString message;
			message.Format(_T("您点击了%s：%s\n当前涨幅：%s"), 
						  data.strType, data.strName, data.strChange);
			MessageBox(message, _T("行业概念链接"), MB_ICONINFORMATION);
			
			// 这里可以添加跳转到行业概念页面的代码
			// 例如：GetDocument()->ViewIndustryConcept(data.strType, data.strName);
		}
	}
	
	CView::OnLButtonDown(nFlags, point);
}

// 辅助函数 - 检测点是否在预警区域的特定行内
int CKLineInfo::HitTestAlertItem(CPoint point)
{
	// 计算预警区域位置
	CRect rectClient;
	GetClientRect(&rectClient);
	
	int priceInfoBottom = 70 + 11 * 30 + 10;
	int industryHeight = (rectClient.Height() - priceInfoBottom) / 2;
	int alertTop = priceInfoBottom + industryHeight;
	
	CRect rcAlert(rectClient.left + 10, alertTop, 
				rectClient.right - 10, rectClient.bottom - 10);
	
	// 判断点是否在预警区域内
	if (!rcAlert.PtInRect(point)) {
		return -1;
	}
	
	// 计算表头区域
	int headerHeight = 35;
	int protectionHeight = 5; // 保护区域高度
	int headerBottom = rcAlert.top + 5 + headerHeight + protectionHeight; // 包括分隔线和保护区域
	
	// 判断点是否在表头及保护区域以下
	if (point.y <= headerBottom) {
		return -1;
	}
	
	// 计算行高和点击的行
	int rowHeight = 26;
	
	// 考虑滚动偏移量
	int pixelOffset = -(int)(m_dblAlertScrollOffset * rowHeight);
	int rowOffset = (point.y - headerBottom - pixelOffset);
	int rowIndex = rowOffset / rowHeight;
	
	// 判断是否在有效范围内
	CRect rcContent(rcAlert.left, headerBottom, rcAlert.right, rcAlert.bottom);
	int maxRows = rcContent.Height() / rowHeight;
	if (rowIndex >= 0 && rowIndex < min(maxRows, (int)m_vecAlerts.size() - m_nAlertScrollPos)) {
		return rowIndex; // 返回视图中的行索引（不是数据索引）
	}
	
	return -1;
}

// 判断点是否在行业概念区域的特定行内，返回行索引，-1表示不在任何行内
int CKLineInfo::HitTestIndustryItem(CPoint point)
{
	// 计算行业概念区域位置
	CRect rectClient;
	GetClientRect(&rectClient);
	
	int priceInfoBottom = 70 + 11 * 30 + 10;
	int industryHeight = (rectClient.Height() - priceInfoBottom) / 2;
	
	CRect rcIndustry(rectClient.left + 10, priceInfoBottom, 
				   rectClient.right - 10, priceInfoBottom + industryHeight);
	
	// 判断点是否在行业概念区域内
	if (!rcIndustry.PtInRect(point)) {
		return -1;
	}
	
	// 计算表头区域
	int headerHeight = 35;
	int protectionHeight = 5; // 保护区域高度
	int headerBottom = rcIndustry.top + 5 + headerHeight + protectionHeight; // 包括分隔线和保护区域
	
	// 判断点是否在表头及保护区域以下
	if (point.y <= headerBottom) {
		return -1;
	}
	
	// 计算行高和点击的行
	int rowHeight = 26;
	
	// 考虑滚动偏移量
	int pixelOffset = -(int)(m_dblIndustryScrollOffset * rowHeight);
	int rowOffset = (point.y - headerBottom - pixelOffset);
	int rowIndex = rowOffset / rowHeight;
	
	// 判断是否在有效范围内
	CRect rcContent(rcIndustry.left, headerBottom, rcIndustry.right, rcIndustry.bottom);
	int maxRows = rcContent.Height() / rowHeight;
	if (rowIndex >= 0 && rowIndex < min(maxRows, (int)m_vecIndustryData.size() - m_nIndustryScrollPos)) {
		return rowIndex; // 返回视图中的行索引（不是数据索引）
	}
	
	return -1;
}

// 处理鼠标移动
void CKLineInfo::OnMouseMove(UINT nFlags, CPoint point)
{
	// 检查鼠标是否在预警区域内的某个条目上
	int alertItemIndex = HitTestAlertItem(point);
	int lastAlertItemIndex = HitTestAlertItem(m_ptLastMouse);
	
	// 检查鼠标是否在行业概念区域内的某个条目上
	int industryItemIndex = HitTestIndustryItem(point);
	int lastIndustryItemIndex = HitTestIndustryItem(m_ptLastMouse);
	
	// 只有当鼠标移动到不同的行或从非高亮区移入高亮区/从高亮区移出非高亮区时才重绘
	if (alertItemIndex != lastAlertItemIndex || industryItemIndex != lastIndustryItemIndex)
	{
		// 保存鼠标位置
		m_ptLastMouse = point;
		
		// 改变鼠标样式为手型光标（如果在链接上）
		if (alertItemIndex != -1 || industryItemIndex != -1)
		{
			::SetCursor(::LoadCursor(NULL, IDC_HAND));
		}
		else
		{
			::SetCursor(::LoadCursor(NULL, IDC_ARROW));
		}
		
		// 计算预警区域位置
		CRect rectClient;
		GetClientRect(&rectClient);
		
		int priceInfoBottom = 70 + 11 * 30 + 10;
		int industryHeight = (rectClient.Height() - priceInfoBottom) / 2;
		int alertTop = priceInfoBottom + industryHeight;
		
		// 如果预警区域的行状态发生变化，需要重绘
		if (lastAlertItemIndex != alertItemIndex)
		{
			// 仅重绘受影响的行，而不是整个预警区域
			if (lastAlertItemIndex != -1 || alertItemIndex != -1)
			{
				CRect rcAlert(rectClient.left + 10, alertTop, 
							rectClient.right - 10, rectClient.bottom - 10);
				
				// 计算表头高度
				int headerHeight = 35;
				int headerBottom = rcAlert.top + 5 + headerHeight;
				int rowHeight = 26;
				
				// 如果之前有高亮行，需要重绘该行
				if (lastAlertItemIndex != -1)
				{
					int lastRowTop = headerBottom + 5 + lastAlertItemIndex * rowHeight;
					CRect rcLastRow(rcAlert.left, lastRowTop, rcAlert.right, lastRowTop + rowHeight);
					InvalidateRect(rcLastRow, FALSE); // 不擦除背景，减少闪烁
				}
				
				// 如果当前有新的高亮行，需要重绘该行
				if (alertItemIndex != -1)
				{
					int rowTop = headerBottom + 5 + alertItemIndex * rowHeight;
					CRect rcCurrentRow(rcAlert.left, rowTop, rcAlert.right, rowTop + rowHeight);
					InvalidateRect(rcCurrentRow, FALSE); // 不擦除背景，减少闪烁
				}
			}
		}
		
		// 如果行业概念区域的行状态发生变化，需要重绘
		if (lastIndustryItemIndex != industryItemIndex)
		{
			// 仅重绘受影响的行，而不是整个行业概念区域
			if (lastIndustryItemIndex != -1 || industryItemIndex != -1)
			{
				CRect rcIndustry(rectClient.left + 10, priceInfoBottom, 
							   rectClient.right - 10, priceInfoBottom + industryHeight);
				
				// 计算表头高度
				int headerHeight = 35;
				int headerBottom = rcIndustry.top + 5 + headerHeight + 2; // 加上分隔线高度
				int rowHeight = 26;
				
				// 如果之前有高亮行，需要重绘该行
				if (lastIndustryItemIndex != -1)
				{
					int lastRowTop = headerBottom + 5 + lastIndustryItemIndex * rowHeight;
					CRect rcLastRow(rcIndustry.left, lastRowTop, rcIndustry.right, lastRowTop + rowHeight);
					InvalidateRect(rcLastRow, FALSE); // 不擦除背景，减少闪烁
				}
				
				// 如果当前有新的高亮行，需要重绘该行
				if (industryItemIndex != -1)
				{
					int rowTop = headerBottom + 5 + industryItemIndex * rowHeight;
					CRect rcCurrentRow(rcIndustry.left, rowTop, rcIndustry.right, rowTop + rowHeight);
					InvalidateRect(rcCurrentRow, FALSE); // 不擦除背景，减少闪烁
				}
			}
		}
	}

	CView::OnMouseMove(nFlags, point);
}

// 处理鼠标滚轮消息
BOOL CKLineInfo::OnMouseWheel(UINT nFlags, short zDelta, CPoint pt)
{
	// 将屏幕坐标转换为客户区坐标
	CPoint point = pt;
	ScreenToClient(&point);
	
	// 计算预警区域位置
	CRect rectClient;
	GetClientRect(&rectClient);
	
	int priceInfoBottom = 70 + 11 * 30 + 10;
	int industryHeight = (rectClient.Height() - priceInfoBottom) / 2;
	int alertTop = priceInfoBottom + industryHeight;
	
	CRect rcAlert(rectClient.left + 10, alertTop, 
				rectClient.right - 10, rectClient.bottom - 10);
	
	CRect rcIndustry(rectClient.left + 10, priceInfoBottom, 
				   rectClient.right - 10, priceInfoBottom + industryHeight);
	
	// 计算滚动方向和数量
	int scrollLines = zDelta > 0 ? -1 : 1; // 向上滚轮为负，向下滚轮为正
	
	// 判断鼠标在哪个区域
	if (rcIndustry.PtInRect(point)) {
		// 行业概念区域
		int rowCount = (int)m_vecIndustryData.size();
		int maxRows = (rcIndustry.Height() - 50) / 26; // 粗略计算可见行数
		
		// 计算新的目标滚动位置
		int newPos = m_nIndustryTargetScrollPos + scrollLines;
		
		// 确保滚动位置在有效范围内
		if (newPos < 0) {
			newPos = 0;
		}
		else if (newPos > rowCount - maxRows && rowCount > maxRows) {
			newPos = rowCount - maxRows;
		}
		
		// 如果有变化才更新
		if (newPos != m_nIndustryTargetScrollPos) {
			m_nIndustryTargetScrollPos = newPos;
			StartSmoothScroll();
		}
		
		return TRUE; // 已处理消息
	}
	else if (rcAlert.PtInRect(point)) {
		// 预警区域
		int rowCount = (int)m_vecAlerts.size();
		int maxRows = (rcAlert.Height() - 50) / 26; // 粗略计算可见行数
		
		// 计算新的目标滚动位置
		int newPos = m_nAlertTargetScrollPos + scrollLines;
		
		// 确保滚动位置在有效范围内
		if (newPos < 0) {
			newPos = 0;
		}
		else if (newPos > rowCount - maxRows && rowCount > maxRows) {
			newPos = rowCount - maxRows;
		}
		
		// 如果有变化才更新
		if (newPos != m_nAlertTargetScrollPos) {
			m_nAlertTargetScrollPos = newPos;
			StartSmoothScroll();
		}
		
		return TRUE; // 已处理消息
	}
	
	return CView::OnMouseWheel(nFlags, zDelta, pt);
}

// 开始平滑滚动动画
void CKLineInfo::StartSmoothScroll()
{
	// 这里不需要特别的初始化，滚动更新会在TIMER_SMOOTH_SCROLL定时器中进行
	// 仅需确保定时器已启动
	Invalidate();
}

// 更新平滑滚动
void CKLineInfo::UpdateSmoothScroll()
{
	bool needRedraw = false;
	
	// 更新行业概念区域滚动
	if (m_nIndustryScrollPos != m_nIndustryTargetScrollPos) {
		// 平滑过渡到目标位置
		double diff = m_nIndustryTargetScrollPos - m_nIndustryScrollPos - m_dblIndustryScrollOffset;
		m_dblIndustryScrollOffset += diff * 0.25; // 使用0.25的系数实现平滑效果
		
		// 如果偏移量足够接近整数，则完成滚动
		if (fabs(m_dblIndustryScrollOffset) >= 0.95) {
			int intOffset = (int)(m_dblIndustryScrollOffset + (m_dblIndustryScrollOffset > 0 ? 0.5 : -0.5));
			m_nIndustryScrollPos += intOffset;
			m_dblIndustryScrollOffset -= intOffset;
			
			// 确保滚动位置在有效范围内
			int rowCount = (int)m_vecIndustryData.size();
			if (m_nIndustryScrollPos < 0) {
				m_nIndustryScrollPos = 0;
				m_nIndustryTargetScrollPos = 0;
				m_dblIndustryScrollOffset = 0;
			}
			else if (m_nIndustryScrollPos > rowCount) {
				m_nIndustryScrollPos = rowCount;
				m_nIndustryTargetScrollPos = rowCount;
				m_dblIndustryScrollOffset = 0;
			}
		}
		
		needRedraw = true;
	}
	
	// 更新预警区域滚动
	if (m_nAlertScrollPos != m_nAlertTargetScrollPos) {
		// 平滑过渡到目标位置
		double diff = m_nAlertTargetScrollPos - m_nAlertScrollPos - m_dblAlertScrollOffset;
		m_dblAlertScrollOffset += diff * 0.25; // 使用0.25的系数实现平滑效果
		
		// 如果偏移量足够接近整数，则完成滚动
		if (fabs(m_dblAlertScrollOffset) >= 0.95) {
			int intOffset = (int)(m_dblAlertScrollOffset + (m_dblAlertScrollOffset > 0 ? 0.5 : -0.5));
			m_nAlertScrollPos += intOffset;
			m_dblAlertScrollOffset -= intOffset;
			
			// 确保滚动位置在有效范围内
			int rowCount = (int)m_vecAlerts.size();
			if (m_nAlertScrollPos < 0) {
				m_nAlertScrollPos = 0;
				m_nAlertTargetScrollPos = 0;
				m_dblAlertScrollOffset = 0;
			}
			else if (m_nAlertScrollPos > rowCount) {
				m_nAlertScrollPos = rowCount;
				m_nAlertTargetScrollPos = rowCount;
				m_dblAlertScrollOffset = 0;
			}
		}
		
		needRedraw = true;
	}
	
	// 如果需要重绘，则刷新视图
	if (needRedraw) {
		Invalidate(FALSE); // 不擦除背景，减少闪烁
	}
}

// 添加UpdateStockData方法来从文档获取最新的数据
void CKLineInfo::UpdateStockData()
{
	// 获取当前时间
	DWORD dwCurrentTime = GetTickCount();
	
	// 如果距上次更新的时间小于500毫秒，则跳过本次更新
	if (dwCurrentTime - m_dwLastUpdateTime < 500)
		return;
	
	m_dwLastUpdateTime = dwCurrentTime;
	
	// 获取文档对象以访问股票数据
	CStockDoc* pDoc = GetDocument();
	if (!pDoc || m_strCode.IsEmpty())
		return;
		
	// 从文档获取当前股票数据
	int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
	if (stockIndex < 0)
		return;
		
	const StockData* pStock = pDoc->GetStock(stockIndex);
	if (!pStock)
		return;
	
	// 更新基本行情数据
	m_dblPreClose = pStock->_preClose;
	m_dblOpen = pStock->_Open;
	m_dblClose = pStock->_Close;
	m_dblHigh = pStock->_High;
	m_dblLow = pStock->_Low;
	m_dblVolume = pStock->_Volume;
	m_dblAmount = pStock->_Amount;
	m_dblVolumeRatio = pStock->_VolumeRatio;
	m_dblTurnover = pStock->_Turnover;
	m_nBuyVolume = pStock->_buyVolume;
	m_nSellVolume = pStock->_sellVolume;
	m_dblCirculatingValue = pStock->_CirculatingValue;
	
	// 更新扩展数据
	m_dblTotalValue = pStock->_CirculatingValue * 1.5; // 估算总市值（实际应从pStock中获取）
	m_dblMainInflow = pStock->_MainInflow;
	m_dblMainOutflow = pStock->_MainOutflow;
	m_dblCirculatingShares = pStock->_CirculatingValue / m_dblClose; // 估算流通股本
	m_dblTotalShares = m_dblTotalValue / m_dblClose; // 估算总股本
	
	// 如果股票有实际交易数据，则计算市盈率
	if (m_dblClose > 0) {
		// 这里假设每股收益为0.5元，实际开发中应从数据库或API获取
		double eps = 0.5; 
		if (eps > 0) {
			m_dblPE = m_dblClose / eps;
			m_dblPETTM = m_dblClose / (eps * 0.9); // 假设TTM数据略小于静态PE
		} else {
			m_dblPE = 0;
			m_dblPETTM = 0;
		}
	} else {
		m_dblPE = 0;
		m_dblPETTM = 0;
	}
	
	// 计算涨跌额和涨跌幅
	if (m_dblPreClose > 0) {
		m_dblChange = m_dblClose - m_dblPreClose;
		m_dblChangePercent = (m_dblClose / m_dblPreClose - 1.0) * 100.0;
	} else {
		m_dblChange = 0.0;
		m_dblChangePercent = 0.0;
	}
	
	// 清除并重新加载行业和概念数据
	m_vecIndustryData.clear();
	
	// 添加行业信息
	if (!pStock->_Industry.empty()) {
		AddIndustryData(_T("行业"), CString(pStock->_Industry.c_str()),
			_T("+2.3%"), TRUE); // 行业涨幅应从其他接口获取，这里使用示例数据
	}
	
	// 添加板块信息
	if (!pStock->_Plate.empty()) {
		AddIndustryData(_T("板块"), CString(pStock->_Plate.c_str()),
			_T("+1.8%"), TRUE); // 板块涨幅应从其他接口获取，这里使用示例数据
	}
	
	// 添加题材信息
	if (!pStock->_Theme.empty()) {
		// 将主题字符串分割为多个题材
		std::string theme = pStock->_Theme;
		size_t pos = 0;
		std::string token;
		std::string delimiter = ",";
		
		while ((pos = theme.find(delimiter)) != std::string::npos) {
			token = theme.substr(0, pos);
			if (!token.empty()) {
				AddIndustryData(_T("题材"), CString(token.c_str()),
					_T("+3.2%"), TRUE); // 题材涨幅应从其他接口获取，这里使用示例数据
			}
			theme.erase(0, pos + delimiter.length());
		}
		if (!theme.empty()) {
			AddIndustryData(_T("题材"), CString(theme.c_str()),
				_T("+1.5%"), TRUE);
		}
	}
	
	// 添加风格信息
	if (!pStock->_Style.empty()) {
		AddIndustryData(_T("风格"), CString(pStock->_Style.c_str()),
			_T("-0.5%"), FALSE); // 风格涨幅应从其他接口获取，这里使用示例数据
	}
	
	// 更新K线图数据 - 如果有最新的K线数据
	if (!pStock->_vecKLine.empty()) {
		const KLINE_DATA& lastKLine = pStock->_vecKLine.back();
		// 转换DWORD日期为CTime
		WORD year, month, day;
		year = (WORD)(lastKLine._Date / 10000);
		month = (WORD)((lastKLine._Date % 10000) / 100);
		day = (WORD)(lastKLine._Date % 100);
		CTime klineDate(year, month, day, 0, 0, 0);
		
		// 设置K线数据
		SetCurrentKLineData(klineDate, 
			lastKLine._Open, lastKLine._Close, 
			lastKLine._High, lastKLine._Low,
			lastKLine._Volume, lastKLine._Amount);
	}
	
	// 更新界面
	Invalidate(FALSE);
} 