﻿// KLineIndicator.h: CKLineSignal 类的接口
//

#pragma once

class CStockDoc;

// CKLineSignal 视图 - 用于显示K线技术指标
class CKLineSignal : public CView
{
protected: // 仅从序列化创建
	CKLineSignal() noexcept;
	DECLARE_DYNCREATE(CKLineSignal)

// 特性
public:
	CStockDoc* GetDocument() const;

// 操作
public:
	// 设置股票代码
	void SetStockCode(const CString& strCode);

// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnInitialUpdate();
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint);

// 实现
public:
	virtual ~CKLineSignal();

protected:
	// 股票基本信息
	CString m_strCode;  // 股票代码
	
	// 绘制函数
	void DrawMACD(CDC* pDC, const CRect& rcArea);
	void DrawRSI(CDC* pDC, const CRect& rcArea);
	void DrawKDJ(CDC* pDC, const CRect& rcArea);
	
	// 绘制指标选择区域
	void DrawIndicatorSelector(CDC* pDC);

// 生成的消息映射函数
protected:
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	DECLARE_MESSAGE_MAP()
	
private:
	// 当前选中的指标
	int m_nCurrentIndicator;  // 0=MACD, 1=RSI, 2=KDJ
};
