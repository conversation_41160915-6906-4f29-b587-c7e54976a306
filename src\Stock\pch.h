﻿// pch.h: 这是预编译标头文件。
// 下方列出的文件仅编译一次，提高了将来生成的生成性能。
// 这还将影响 IntelliSense 性能，包括代码完成和许多代码浏览功能。
// 但是，如果此处列出的文件中的任何一个在生成之间有更新，它们全部都将被重新编译。
// 请勿在此处添加要频繁更新的文件，这将使得性能优势无效。

#ifndef PCH_H
#define PCH_H

#pragma warning(disable:4251)
#pragma warning(disable:4996)
#pragma warning(disable:4099)
#pragma warning(disable:4018)
#pragma warning(disable:4244)

// 添加要在此处预编译的标头
#include "framework.h"
#include "StockDef.h"
#include "ColorDef.h"

#include "..\common\WinHttp.h"
#include "..\Common\JSON.h"
#include "..\Common\JSONValue.h"
#include "..\Common\Common.h"
#include "..\..\inc\sqlite3mc.h"
#include "..\..\inc\zlib.h"
#include "ugctrl.h"
#include "UGCTsarw.h"	

//////////////////////////////////////////////////////////////////////////
// C++ 标准库
#include <string>
#include <vector>
#include <queue>
#include <map>
#include <stack>
#include <tuple>
#include <algorithm>
#include <iosfwd>
#include <sstream>
#include <fstream>
#include <iostream>
#include <iomanip>
#include <assert.h>
#include <memory>
#include <functional>
#include <iterator>
#include <deque>

using namespace std;
#endif //PCH_H
