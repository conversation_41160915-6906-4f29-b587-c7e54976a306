﻿#pragma once
#include "resource.h"
#include "afxwin.h"
#include "afxcmn.h"
#include "Symbol/SymbolGrid.h"

// CColumnConfigDlg 对话框类
class CColumnConfigDlg : public CDialog
{
    DECLARE_DYNAMIC(CColumnConfigDlg)

public:
    CColumnConfigDlg(CWnd* pParent = NULL);
    virtual ~CColumnConfigDlg();

    // 设置列信息数组
    void SetColumnInfos(COLUMN_INFO* pColInfos, int count);
    
    // 获取修改后的列信息数组
    const COLUMN_INFO* GetColumnInfos() const;
    
    // 获取列数
    int GetColumnCount() const;

// 对话框数据
    enum { IDD = IDD_COLUMN_CONFIG };

protected:
    virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
    virtual BOOL OnInitDialog();

    // 更新列表框显示
    void UpdateColumnList();
    
    // 更新控件状态
    void UpdateControlStates();
    
    // 更新列类型下拉框
    void InitColumnTypeCombo();
    
    // 更新对齐方式下拉框
    void InitAlignmentCombo();
    
    // 获取列类型名称
    CString GetColumnTypeName(int typeIndex);
    
    // 获取对齐方式名称
    CString GetAlignmentName(int alignIndex);

    DECLARE_MESSAGE_MAP()

private:
    // 列信息数组
    COLUMN_INFO m_columnInfos[NUM_COLS_MAX];
    
    // 列数
    int m_columnCount;
    
    // 控件变量
    CListBox m_listColumns;
    CComboBox m_comboColumnType;
    CComboBox m_comboAlignment;
    CEdit m_editColumnWidth;
    CEdit m_editColumnName;
    
    // 当前选中的列索引
    int m_currentSelIndex;

public:
    afx_msg void OnBnClickedBtnMoveUp();
    afx_msg void OnBnClickedBtnMoveDown();
    afx_msg void OnBnClickedBtnShow();
    afx_msg void OnBnClickedBtnHide();
    afx_msg void OnBnClickedBtnReset();
    afx_msg void OnBnClickedBtnAdd();
    afx_msg void OnBnClickedBtnDelete();
    afx_msg void OnLbnSelchangeListColumns();
    afx_msg void OnCbnSelchangeComboColumnType();
    afx_msg void OnCbnSelchangeComboAlignment();
    afx_msg void OnEnChangeEditColumnWidth();
    afx_msg void OnEnChangeEditColumnName();
    afx_msg void OnBnClickedOk();
}; 