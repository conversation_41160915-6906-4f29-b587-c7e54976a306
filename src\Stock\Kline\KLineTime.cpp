﻿// TimeLineView.cpp: CKLineTime 类的实现
//

#include "pch.h"
#include "..\framework.h"
// SHARED_HANDLERS 可以在实现预览、缩略图和搜索筛选器句柄的
// ATL 项目中进行定义，并允许与该项目共享文档代码。
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "KLineTime.h"
#include <vector>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// CKLineTime

IMPLEMENT_DYNCREATE(CKLineTime, CView)

BEGIN_MESSAGE_MAP(CKLineTime, CView)

    ON_WM_SIZE()
    ON_WM_LBUTTONDOWN()
    ON_WM_MOUSEMOVE()
    ON_WM_RBUTTONDOWN()
    ON_WM_ERASEBKGND()
    ON_WM_TIMER()
    ON_WM_KEYDOWN()
    ON_WM_MOUSEWHEEL()
END_MESSAGE_MAP()

// CKLineTime 构造/析构

CKLineTime::CKLineTime() noexcept
{
	// 初始化股票代码
	m_strCode = _T("");
	
	// 初始化分时图参数
    m_basePrice = 10.50;
    m_highestPrice = 11.55;
    m_lowestPrice = 10.45;
    m_maxVolume = 0;
    m_totalVolume = 0;
    m_totalAmount = 0;
    m_bLimitCoordinate = true;  // 默认使用涨停板坐标
    
    // 初始化十字光标相关
    m_bShowCrossCursor = false;
    m_crossCursorPos = CPoint(0, 0);
    m_crossDataIndex = -1;
    
    // 初始化动态演示相关
    m_nDemoIndex = 0;
    m_bDemoRunning = false;
    
    // 初始化集合竞价区域显示控制
    m_bShowAuctionArea = true;  // 默认显示集合竞价区域
    
    // 初始化显示参数
    m_bgColor = RGB(0, 0, 0);            // 黑色背景
    m_priceLinesColor = RGB(255, 255, 255); // 白色价格线
    m_avgLineColor = RGB(255, 255, 0);    // 黄色均线
    m_nowPriceLineColor = RGB(255, 255, 0); // 黄色当前价格线
    m_upVolumeColor = RGB(255, 0, 0);     // 红色上涨柱
    m_downVolumeColor = RGB(0, 255, 0);   // 绿色下跌柱
    m_flatVolumeColor = RGB(255, 255, 255); // 白色平盘柱
 }

CKLineTime::~CKLineTime()
{
}

// 获取文档指针
CStockDoc* CKLineTime::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 设置股票代码
void CKLineTime::SetStockCode(const CString& strCode)
{
	// 检查是否需要更新股票代码
	if (m_strCode != strCode)
	{
		m_strCode = strCode;
		

		// 延迟重绘，避免立即重绘导致的闪烁
		SetTimer(1001, 50, NULL);
	}
}

// 响应文档更新
void CKLineTime::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 调用基类的OnUpdate方法
	CView::OnUpdate(pSender, lHint, pHint);
	
	// 从文档获取当前股票代码
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
		
		// 如果当前显示的股票代码与文档中的不同，则更新
		if (m_strCode != strCurrentStock)
		{
			// 设置新的股票代码，这会触发数据加载和视图更新
			SetStockCode(strCurrentStock);
			
			TRACE(_T("CKLineTime::OnUpdate - 股票代码已更新: %s\n"), strCurrentStock);
		}
	}
}

BOOL CKLineTime::PreCreateWindow(CREATESTRUCT& cs)
{
	// TODO: 在此处通过修改
	//  CREATESTRUCT cs 来修改窗口类或样式

	return CView::PreCreateWindow(cs);
}

// 初始化视图
void CKLineTime::OnInitialUpdate()
{
	CView::OnInitialUpdate();
	
	// 从文档获取当前股票代码
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
		if (!strCurrentStock.IsEmpty())
		{
			// 设置股票代码，加载对应的分时数据
			SetStockCode(strCurrentStock);
		}
	}
	
	// 重新计算图表区域
	CRect rcClient;
	GetClientRect(&rcClient);
	CClientDC dc(this);
	CalculateChartRect(&dc, rcClient);
}

// CKLineTime 绘图

void CKLineTime::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;

    // 获取客户区大小
    CRect clientRect;
    GetClientRect(clientRect);

    // 计算各区域大小
    CalculateChartRect(pDC, clientRect);

    // 创建内存DC以避免闪烁
    CDC memDC;
    CBitmap memBitmap;
    memDC.CreateCompatibleDC(pDC);
    memBitmap.CreateCompatibleBitmap(pDC, clientRect.Width(), clientRect.Height());
    CBitmap* pOldBitmap = memDC.SelectObject(&memBitmap);

    // 创建微软雅黑 Light字体
    CFont font;
    font.CreatePointFont(90, _T("微软雅黑 Light"));
    CFont* pOldFont = memDC.SelectObject(&font);

    // 绘制背景
    DrawBackground(&memDC, clientRect);
    
    // 绘制信息栏
    DrawInfoBar(&memDC);
    
    // 绘制价格图
    DrawPriceChart(&memDC);
    
    // 绘制成交量图
    DrawVolumeChart(&memDC);
    
    // 绘制时间轴
    DrawTimeAxis(&memDC);
    
    // 绘制十字光标（如果需要显示）
    DrawCrossCursor(&memDC);

    // 恢复原字体
    memDC.SelectObject(pOldFont);
    font.DeleteObject();

    // 将内存DC内容拷贝到屏幕
    pDC->BitBlt(0, 0, clientRect.Width(), clientRect.Height(), &memDC, 0, 0, SRCCOPY);
    
    // 清理
    memDC.SelectObject(pOldBitmap);
    memBitmap.DeleteObject();
    memDC.DeleteDC();
}

// 计算各区域大小
void CKLineTime::CalculateChartRect(CDC* pDC, const CRect& clientRect)
{
    // 计算各区域高度比例: 信息栏30像素, 价格图约70%, 成交量图约30%, 时间轴30像素
    int totalHeight = clientRect.Height();
    int infoHeight = 30;        // 固定30像素高度
    int timeAxisHeight = 30;    // 时间轴固定30像素高度
    
    // 使用double进行精确计算，避免整数除法精度损失
    double priceHeightRatio = 0.7;  // 价格图占可用区域的70%
    double volumeHeightRatio = 0.3; // 成交量图占可用区域的30%
    
    // 计算可用区域高度(总高度减去固定高度部分)
    int availableHeight = totalHeight - infoHeight - timeAxisHeight;
    
    // 更精确地计算各区域高度
    int priceHeight = static_cast<int>(availableHeight * priceHeightRatio + 0.5); // 四舍五入
    int volumeHeight = availableHeight - priceHeight; // 确保总高度精确匹配
    
    // 设置各区域
    m_chartRect.infoRect = CRect(clientRect.left, clientRect.top, clientRect.right, clientRect.top + infoHeight);
    m_chartRect.priceRect = CRect(clientRect.left, m_chartRect.infoRect.bottom, clientRect.right, m_chartRect.infoRect.bottom + priceHeight);
    // 成交量区上移，上框与分时区底框完全重合
    m_chartRect.volumeRect = CRect(clientRect.left, m_chartRect.priceRect.bottom, clientRect.right, m_chartRect.priceRect.bottom + volumeHeight);
    // 时间轴紧贴成交量区下方，完全无间隙
    m_chartRect.timeAxisRect = CRect(clientRect.left, m_chartRect.volumeRect.bottom, clientRect.right, m_chartRect.volumeRect.bottom + timeAxisHeight);
}

// 绘制背景
void CKLineTime::DrawBackground(CDC* pDC, const CRect& rect)
{
    // 设置背景颜色
    pDC->FillSolidRect(rect, m_bgColor);
    
}

// 绘制信息栏
void CKLineTime::DrawInfoBar(CDC* pDC)
{
    //if (m_timeData.empty())
    //    return;
    //    
    //// 获取要显示的数据 - 如果有有效的十字光标索引，显示该位置的数据，否则显示最新数据
    //const StockTimeData& displayData = (m_crossDataIndex >= 0 && m_crossDataIndex < (int)m_timeData.size()) ? 
    //                                  m_timeData[m_crossDataIndex] : m_timeData.back();
    //
    //// 创建更小的字体用于信息栏文字显示
    //CFont smallFont;
    //smallFont.CreatePointFont(70, _T("微软雅黑 Light")); // 使用微软雅黑 Light
    //CFont* pOldFont = pDC->SelectObject(&smallFont);
    //
    //// 设置文本颜色和背景模式
    //pDC->SetTextColor(RGB(255, 255, 255));
    //pDC->SetBkMode(TRANSPARENT);
    //
    //// 准备显示信息
    //CString timeStr = displayData.time.Format(_T("%Y-%m-%d %H:%M:%S"));
    //CString priceStr, changePercentStr, volumeStr, amountStr;
    //
    //priceStr.Format(_T("价格: %.2f"), displayData.price);
    //
    //double changePercent = (displayData.price - m_basePrice) / m_basePrice * 100;
    //if (changePercent > 0)
    //    changePercentStr.Format(_T("+%.2f%%"), changePercent);
    //else
    //    changePercentStr.Format(_T("%.2f%%"), changePercent);
    //
    //// 如果显示的是实时位置的数据（鼠标移动到的点），显示该位置的成交量
    //if (m_crossDataIndex >= 0 && m_crossDataIndex < (int)m_timeData.size()) {
    //    volumeStr.Format(_T("成交量: %.0f手"), displayData.volume / 100); // 股票以手为单位，1手=100股
    //    amountStr.Format(_T("成交额: %.2f万"), displayData.amount / 10000); // 以万为单位
    //} else {
    //    // 显示总成交量和总成交额
    //    volumeStr.Format(_T("成交量: %.0f手"), m_totalVolume / 100); // 股票以手为单位，1手=100股
    //    amountStr.Format(_T("成交额: %.2f万"), m_totalAmount / 10000); // 以万为单位
    //}
    //
    //// 绘制文本
    //CRect textRect = m_chartRect.infoRect;
    //int leftMargin = 70;  // 恢复左侧边距为70像素，与分时图区域保持一致
    //int rightMargin = 10;
    //int itemSpacing = 20; // 各项之间的间距
    //textRect.DeflateRect(leftMargin, 2, rightMargin, 2);  // 垂直方向缩进2像素，美化显示
    //
    //// 计算各个文本的宽度
    //CSize timeSize = pDC->GetTextExtent(timeStr);
    //CSize priceSize = pDC->GetTextExtent(priceStr);
    //CSize changeLabelSize = pDC->GetTextExtent(_T("涨幅: "));
    //CSize changeValueSize = pDC->GetTextExtent(changePercentStr);
    //CSize volumeSize = pDC->GetTextExtent(volumeStr);
    //CSize amountSize = pDC->GetTextExtent(amountStr);
    //
    //// 从左到右依次绘制各个信息项
    //int currLeft = textRect.left;
    //
    //// 绘制时间
    //pDC->DrawText(timeStr, CRect(currLeft, textRect.top, currLeft + timeSize.cx, textRect.bottom), DT_LEFT | DT_VCENTER | DT_SINGLELINE);
    //currLeft += timeSize.cx + itemSpacing;
    //
    //// 绘制价格
    //pDC->DrawText(priceStr, CRect(currLeft, textRect.top, currLeft + priceSize.cx, textRect.bottom), DT_LEFT | DT_VCENTER | DT_SINGLELINE);
    //currLeft += priceSize.cx + itemSpacing;
    //
    //// 绘制涨幅标签（白色）
    //CString changeLabelText = _T("涨幅: ");
    //pDC->DrawText(changeLabelText, CRect(currLeft, textRect.top, currLeft + changeLabelSize.cx, textRect.bottom), DT_LEFT | DT_VCENTER | DT_SINGLELINE);
    //
    //// 保存原始颜色
    //COLORREF oldTextColor = pDC->GetTextColor();
    //
    //// 根据涨跌设置颜色
    //if (changePercent > 0) {
    //    pDC->SetTextColor(RGB(255, 0, 0)); // 红色表示上涨
    //} else if (changePercent < 0) {
    //    pDC->SetTextColor(RGB(0, 255, 0)); // 绿色表示下跌
    //} else {
    //    pDC->SetTextColor(RGB(255, 255, 255)); // 白色表示平盘
    //}
    //
    //// 绘制涨幅百分比值
    //pDC->DrawText(changePercentStr, CRect(currLeft + changeLabelSize.cx, textRect.top, currLeft + changeLabelSize.cx + changeValueSize.cx, textRect.bottom), DT_LEFT | DT_VCENTER | DT_SINGLELINE);
    //
    //// 恢复原始颜色
    //pDC->SetTextColor(oldTextColor);
    //currLeft += changeLabelSize.cx + changeValueSize.cx + itemSpacing;
    //
    //// 绘制成交量
    //pDC->DrawText(volumeStr, CRect(currLeft, textRect.top, currLeft + volumeSize.cx, textRect.bottom), DT_LEFT | DT_VCENTER | DT_SINGLELINE);
    //currLeft += volumeSize.cx + itemSpacing;
    //
    //// 绘制成交额
    //pDC->DrawText(amountStr, CRect(currLeft, textRect.top, textRect.right, textRect.bottom), DT_LEFT | DT_VCENTER | DT_SINGLELINE);
    //
    //// 恢复原有字体并释放新字体
    //pDC->SelectObject(pOldFont);
    //smallFont.DeleteObject();
}

// 绘制价格图
void CKLineTime::DrawPriceChart(CDC* pDC)
{
    //if (m_timeData.empty())
    //    return;
    //    
    //CRect chartRect = m_chartRect.priceRect;
    //// 留出左右两侧的边距
    //int leftMargin = 70;
    //int rightMargin = 70;
    //chartRect.DeflateRect(leftMargin, 10, rightMargin, 10);
    //
    //// 计算集合竞价区与分时区的分界点
    //int auctionEndIndex = 0;
    //CTime auctionEndTime(2023, 1, 1, 9, 30, 0); // 9:30分界
    //
    //for (size_t i = 0; i < m_timeData.size(); i++) {
    //    if (m_timeData[i].time >= auctionEndTime) {
    //        auctionEndIndex = i;
    //        break;
    //    }
    //}
    //
    //// 集合竞价区宽度比例
    //double auctionWidthRatio = 0.1;
    //double auctionWidth = chartRect.Width() * auctionWidthRatio;
    //double normalWidth = chartRect.Width() - auctionWidth;
    //
    //// 计算正常交易区起始X坐标
    //int chartLeft = chartRect.left;
    //int chartRight = chartRect.right;
    //
    //// 如果显示集合竞价区域，则调整正常交易区起始位置
    //if (m_bShowAuctionArea) {
    //    chartLeft = chartRect.left + auctionWidth;
    //}
    //
    //// 计算可见区域的最高和最低价格
    //double priceMax, priceMin;
    //
    //// 确定价格边界
    //if (m_bLimitCoordinate) {
    //    // 涨停板坐标：基准价格的 ±10%
    //    priceMax = m_basePrice * 1.1;
    //    priceMin = m_basePrice * 0.9;
    //} else {
    //    // 实时价格坐标：取数据中的最高最低价，并留出一些边距
    //    priceMax = m_highestPrice * 1.01;
    //    priceMin = m_lowestPrice * 0.99;
    //}
    //
    //double priceRange = priceMax - priceMin;
    //double priceMid = (priceMax + priceMin) / 2;
    //
    //// 计算价格到Y坐标的转换比例
    //double priceToY = chartRect.Height() / priceRange;
    //
    //// 绘制价格坐标
    //DrawCoordinates(pDC);
    //
    //// 如果显示集合竞价区域，则绘制集合竞价区
    //if (m_bShowAuctionArea) {
    //    // 创建成交量区域的副本并调整边距
    //    CRect volumeRect = m_chartRect.volumeRect;
    //    volumeRect.DeflateRect(leftMargin, 10, rightMargin, 0);
    //    
    //    // 绘制集合竞价区域（价格图和成交量图）
    //    DrawAuctionArea(pDC, chartRect, volumeRect, auctionEndIndex);
    //}
    //
    //// ===== 修改部分开始 =====
    //// 修改基准价格线的Y坐标计算逻辑，使其位置固定
    //// 不再使用价格计算方式，而是固定在分时图区域的中间位置
    //
    //// 旧的计算方式，会随坐标系切换而变化
    ////double priceToY = chartRect.Height() / priceRange;
    ////int basePriceY = chartRect.bottom - (int)((m_basePrice - priceMin) * priceToY);
    ////basePriceY = max(chartRect.top, min(chartRect.bottom, basePriceY));
    //
    //// 新的计算方式，固定在分时图区域的中间位置
    //int basePriceY = chartRect.top + chartRect.Height() / 2;
    //
    //// 绘制基准价格线（深红色粗线）- 零轴横线
    //CPen basePricePen(PS_SOLID, 3, RGB(128, 0, 0)); // 深红色粗线
    //CPen* pOldPen = pDC->SelectObject(&basePricePen);
    //pDC->MoveTo(chartRect.left, basePriceY);
    //pDC->LineTo(chartRect.right, basePriceY);
    //// ===== 修改部分结束 =====
    //
    //// 动态演示的索引边界
    //int displayEndIndex = m_timeData.size();
    //if (m_bDemoRunning && m_nDemoIndex > 0) {
    //    displayEndIndex = min(m_nDemoIndex, (int)m_timeData.size());
    //}
    //
    //// 创建新的仅包含9:30之后数据的数组
    //std::vector<CPoint> pricePoints;
    //std::vector<CPoint> avgPoints;

    //// 收集正常交易时段的价格点和均价点
    //for (size_t i = auctionEndIndex; i < displayEndIndex; i++) {
    //    // 计算X坐标（在正常交易区）
    //    // 使用浮点数计算以提高精度，最后再转为整数
    //    double relativePosition = static_cast<double>(i - auctionEndIndex) / (m_timeData.size() - auctionEndIndex);
    //    int x;
    //    
    //    // 根据是否显示集合竞价区域，调整X坐标计算
    //    if (m_bShowAuctionArea) {
    //        x = chartLeft + static_cast<int>(normalWidth * relativePosition + 0.5); // 有集合竞价区域时
    //    } else {
    //        x = chartRect.left + static_cast<int>(chartRect.Width() * relativePosition + 0.5); // 无集合竞价区域时
    //    }
    //    
    //    // 计算价格的Y坐标，提高精度
    //    double priceOffset = m_timeData[i].price - priceMin;
    //    int priceY = chartRect.bottom - static_cast<int>(priceOffset * priceToY + 0.5); // 四舍五入
    //    priceY = max(chartRect.top, min(chartRect.bottom, priceY));
    //    
    //    // 计算均价的Y坐标，提高精度
    //    double avgOffset = m_timeData[i].avgPrice - priceMin;
    //    int avgY = chartRect.bottom - static_cast<int>(avgOffset * priceToY + 0.5); // 四舍五入
    //    avgY = max(chartRect.top, min(chartRect.bottom, avgY));
    //    
    //    // 添加到点集合
    //    pricePoints.push_back(CPoint(x, priceY));
    //    avgPoints.push_back(CPoint(x, avgY));
    //}
    //
    //// 绘制价格线（白色实线）
    //if (pricePoints.size() > 1) {
    //    CPen pricePen(PS_SOLID, 1, m_priceLinesColor);
    //    pDC->SelectObject(&pricePen);
    //    
    //    pDC->MoveTo(pricePoints[0]);
    //    for (size_t i = 1; i < pricePoints.size(); i++) {
    //        pDC->LineTo(pricePoints[i]);
    //    }
    //}
    //
    //// 绘制均线（黄色实线）
    //if (avgPoints.size() > 1) {
    //    CPen avgPen(PS_SOLID, 1, m_avgLineColor);
    //    pDC->SelectObject(&avgPen);
    //    
    //    pDC->MoveTo(avgPoints[0]);
    //    for (size_t i = 1; i < avgPoints.size(); i++) {
    //        pDC->LineTo(avgPoints[i]);
    //    }
    //}
    //
    //// ===== 添加实时价格横线 开始 =====
    //// 获取当前价格
    //double latestPrice = !m_timeData.empty() ? m_timeData.back().price : m_basePrice;

    //// 计算实时价格线的Y坐标
    //double priceOffset = latestPrice - priceMin;
    //int latestPriceY = chartRect.bottom - static_cast<int>(priceOffset * priceToY + 0.5);
    //latestPriceY = max(chartRect.top, min(chartRect.bottom, latestPriceY)); // 确保在可见区域内

    //// 创建白色虚线笔
    //CPen nowPricePen(PS_DASH, 1, m_nowPriceLineColor); // 使用类变量定义的颜色
    //CPen* pNowPriceOldPen = pDC->SelectObject(&nowPricePen);

    //// 确保实时价格线从分时区最左侧开始绘制，包括集合竞价区
    //int chartLeftWithMargin = chartRect.left; // 从最左侧开始
    //pDC->MoveTo(chartLeftWithMargin, latestPriceY);
    //pDC->LineTo(chartRect.right, latestPriceY);

    //// 绘制实时价格标签
    //CString priceLabel;
    //priceLabel.Format(_T("%.2f"), latestPrice);

    //// 保存原有文本和背景设置
    //COLORREF oldTextColor = pDC->GetTextColor();
    //COLORREF oldBkColor = pDC->GetBkColor();
    //int oldBkMode = pDC->GetBkMode();

    //// 设置文本背景为黑色，文本颜色为黄色
    //pDC->SetBkColor(RGB(0, 0, 0));
    //pDC->SetTextColor(RGB(255, 255, 0));

    //// 计算文本大小
    //CSize textSize = pDC->GetTextExtent(priceLabel);

    //// 绘制价格标签在线的右端
    //CRect textRect(chartRect.right - textSize.cx - 5, latestPriceY - textSize.cy/2, 
    //              chartRect.right - 5, latestPriceY + textSize.cy/2);

    //// 先填充背景色
    //pDC->FillSolidRect(textRect, RGB(0, 0, 0));
    //// 绘制文本
    //pDC->SetBkMode(TRANSPARENT);
    //pDC->DrawText(priceLabel, textRect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);

    //// 恢复原有设置
    //pDC->SetTextColor(oldTextColor);
    //pDC->SetBkColor(oldBkColor);
    //pDC->SetBkMode(oldBkMode);
    //pDC->SelectObject(pNowPriceOldPen);
    //// ===== 添加实时价格横线 结束 =====
    //
    //// 绘制均匀分布的7条深红色竖线，将整个分时区域（不含竞价区）分为8个部分
    //CPen verticalGridPen(PS_DOT, 1, RGB(128, 0, 0)); // 深红色虚线
    //CPen specialSolidPen(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色实线（用于第2、第6根竖线）
    //CPen thickSolidPen(PS_SOLID, 2, RGB(160, 32, 32)); // 改为更亮的深红色粗实线，与水平中线保持一致
    //CPen* pGridOldPen = pDC->SelectObject(&verticalGridPen);  // 保存之前的画笔

    //int numSections = 8; // 将正常交易区分为8个部分

    //// 计算竖线的起始位置，根据是否显示集合竞价区域调整
    //int gridLeft = m_bShowAuctionArea ? chartLeft : chartRect.left;
    //int gridWidth = m_bShowAuctionArea ? normalWidth : chartRect.Width();

    //// 计算并存储分隔线位置，供后续判断重叠使用
    //int separatorX = 0;
    //if (m_bShowAuctionArea) {
    //    separatorX = chartRect.left + auctionWidth; // 集合竞价区与正常交易区的分隔线X坐标
    //}

    //// 存储垂直线位置数组，用于后续在成交量图中绘制相同位置的分隔线
    //std::vector<int> vertLinePositions;

    //for (int i = 1; i < numSections; i++) {
    //    // 计算分隔线X坐标，使用更精确的计算
    //    int gridSpacing = gridWidth / numSections;
    //    int gridX = gridLeft + (i * gridSpacing);
    //    
    //    // 如果显示集合竞价区域并且竖线位置接近分隔线，则跳过，避免重叠
    //    if (m_bShowAuctionArea && abs(gridX - separatorX) < 5)
    //        continue;
    //    
    //    // 存储有效的垂直线位置
    //    vertLinePositions.push_back(gridX);
    //    
    //    // 根据线条索引选择不同的画笔
    //    if (i == 2 || i == 6) {
    //        // 第2根和第6根竖线使用深红色实线
    //        pDC->SelectObject(&specialSolidPen);
    //    } else if (i == 4) {
    //        // 第4根竖线使用深红色粗实线
    //        pDC->SelectObject(&thickSolidPen);
    //    } else {
    //        // 其他竖线使用默认的深红色虚线
    //        pDC->SelectObject(&verticalGridPen);
    //    }
    //    
    //    // 绘制分隔线
    //    pDC->MoveTo(gridX, chartRect.top);
    //    pDC->LineTo(gridX, chartRect.bottom);
    //}

    //// 将垂直线位置存储到类成员变量中，供其他绘制函数使用
    //m_vertLinePositions = vertLinePositions;
    //
    //// 显示动态演示的当前位置标记
    //if (m_bDemoRunning && displayEndIndex > auctionEndIndex && displayEndIndex < (int)m_timeData.size()) {
    //    // 获取当前位置，提高精度
    //    double relativePos = static_cast<double>(displayEndIndex - auctionEndIndex) / (m_timeData.size() - auctionEndIndex);
    //    
    //    // 根据是否显示集合竞价区域，计算不同的X坐标
    //    int currentX;
    //    if (m_bShowAuctionArea) {
    //        currentX = chartLeft + static_cast<int>(normalWidth * relativePos + 0.5); // 有集合竞价区域时
    //    } else {
    //        currentX = chartRect.left + static_cast<int>(chartRect.Width() * relativePos + 0.5); // 无集合竞价区域时
    //    }
    //    
    //    // 绘制一个醒目的红色竖线标记当前位置
    //    CPen currentPosPen(PS_SOLID, 3, RGB(255, 0, 0)); // 鲜红色粗线
    //    pDC->SelectObject(&currentPosPen);
    //    pDC->MoveTo(currentX, chartRect.top);
    //    pDC->LineTo(currentX, chartRect.bottom);
    //}
    //
    //// 不再绘制边框
    //
    //// 恢复原来的画笔
    //pDC->SelectObject(pGridOldPen);
}

// 绘制成交量图
void CKLineTime::DrawVolumeChart(CDC* pDC)
{
    //if (m_timeData.empty())
    //    return;
    //    
    //CRect chartRect = m_chartRect.volumeRect;
    //// 留出左右两侧的边距，与价格图对齐，移除底部边距使其与时间轴紧贴
    //int leftMargin = 70;
    //int rightMargin = 70;
    //// 成交量区上边距需要与分时图区底部边距保持一致，修改为10像素
    //chartRect.DeflateRect(leftMargin, 10, rightMargin, 0);
    //
    //// 计算集合竞价区与分时区的分界点
    //int auctionEndIndex = 0;
    //CTime auctionEndTime(2023, 1, 1, 9, 30, 0); // 9:30分界
    //
    //for (size_t i = 0; i < m_timeData.size(); i++) {
    //    if (m_timeData[i].time >= auctionEndTime) {
    //        auctionEndIndex = i;
    //        break;
    //    }
    //}
    //
    //// 集合竞价区宽度比例（与价格图一致）
    //double auctionWidthRatio = 0.1;
    //double auctionWidth = chartRect.Width() * auctionWidthRatio;
    //double normalWidth = chartRect.Width() - auctionWidth;
    //
    //// 计算正常交易区起始X坐标
    //int chartLeft = chartRect.left;
    //int chartRight = chartRect.right;
    //
    //// 如果显示集合竞价区域，则调整正常交易区起始位置
    //if (m_bShowAuctionArea) {
    //    chartLeft = chartRect.left + auctionWidth;
    //}
    //
    //// 绘制水平深红色实线，将成交量区分割为三个区域
    //CPen redLinePen(PS_SOLID, 2, RGB(128, 0, 0)); // 深红色实线
    //CPen* pOldPen = pDC->SelectObject(&redLinePen);
    //
    //// 计算三等分的位置
    //int firstDividerY = chartRect.top + chartRect.Height() / 3;
    //int secondDividerY = chartRect.top + chartRect.Height() * 2 / 3;
    //
    //// 绘制两条水平分隔线
    //pDC->MoveTo(chartRect.left, firstDividerY);
    //pDC->LineTo(chartRect.right, firstDividerY);
    //
    //pDC->MoveTo(chartRect.left, secondDividerY);
    //pDC->LineTo(chartRect.right, secondDividerY);
    //
    //// 计算每个时间点的宽度
    //double barWidth;
    //
    //// 动态演示的索引边界
    //int displayEndIndex = m_timeData.size();
    //if (m_bDemoRunning && m_nDemoIndex > 0) {
    //    displayEndIndex = min(m_nDemoIndex, (int)m_timeData.size());
    //}
    //
    //// 只绘制9:30之后的成交量数据
    //for (size_t i = auctionEndIndex; i < displayEndIndex; i++) {
    //    // 计算X坐标，只在正常交易区绘制
    //    double relativePosition = static_cast<double>(i - auctionEndIndex) / (m_timeData.size() - auctionEndIndex);
    //    
    //    // 根据是否显示集合竞价区域，调整X坐标计算
    //    int x;
    //    double chartWidth;
    //    
    //    if (m_bShowAuctionArea) {
    //        x = chartLeft + static_cast<int>(normalWidth * relativePosition + 0.5); // 有集合竞价区域时
    //        chartWidth = normalWidth;
    //    } else {
    //        x = chartRect.left + static_cast<int>(chartRect.Width() * relativePosition + 0.5); // 无集合竞价区域时
    //        chartWidth = chartRect.Width();
    //    }
    //    
    //    // 计算柱宽
    //    barWidth = static_cast<double>(chartWidth) / (m_timeData.size() - auctionEndIndex);
    //    if (barWidth < 1) barWidth = 1;
    //    
    //    // 计算高度，提高精度
    //    double volumeRatio = m_timeData[i].volume / m_maxVolume;
    //    int height = static_cast<int>(chartRect.Height() * volumeRatio + 0.5); // 四舍五入
    //    if (height < 1) height = 1;
    //    
    //    // 设置颜色
    //    COLORREF lineColor;
    //    switch (m_timeData[i].priceDirection) {
    //        case 1:  // 上涨
    //            lineColor = RGB(255, 255, 0); // 黄色表示上涨
    //            break;
    //        case -1: // 下跌
    //            lineColor = RGB(0, 162, 232); // 蓝色表示下跌
    //            break;
    //        default: // 平盘
    //            lineColor = RGB(255, 255, 255); // 白色表示价格未变
    //            break;
    //    }
    //    
    //    // 绘制成交量竖线，而不是柱状图
    //    CPen volumePen(PS_SOLID, 1, lineColor);
    //    CPen* pOldVolumePen = pDC->SelectObject(&volumePen);
    //    
    //    // 计算竖线中心点X坐标
    //    int lineX = x + (int)(barWidth / 2);
    //    
    //    // 以底部对齐绘制竖线
    //    pDC->MoveTo(lineX, chartRect.bottom);
    //    pDC->LineTo(lineX, chartRect.bottom - height);
    //    
    //    // 恢复之前的画笔状态
    //    pDC->SelectObject(pOldVolumePen);
    //}
    //
    //// 如果在动态演示中，绘制一个指示当前位置的标记
    //if (m_bDemoRunning && displayEndIndex > auctionEndIndex && displayEndIndex < (int)m_timeData.size()) {
    //    // 获取当前位置，提高精度
    //    double relativePos = static_cast<double>(displayEndIndex - auctionEndIndex) / (m_timeData.size() - auctionEndIndex);
    //    int currentX = chartRect.left + auctionWidth + static_cast<int>(normalWidth * relativePos + 0.5); // 四舍五入
    //    
    //    // 绘制一个醒目的红色竖线标记当前位置
    //    CPen currentPosPen(PS_SOLID, 3, RGB(255, 0, 0)); // 鲜红色粗线
    //    CPen* pTempPen = pDC->SelectObject(&currentPosPen);
    //    pDC->MoveTo(currentX, chartRect.top);
    //    pDC->LineTo(currentX, chartRect.bottom);
    //    pDC->SelectObject(pTempPen); // 使用完毕立即恢复临时存储的画笔
    //}
    //
    //// 绘制均匀分布的竖线，与价格图竖线位置完全对齐
    //CPen verticalGridPen(PS_DOT, 1, RGB(128, 0, 0)); // 深红色虚线
    //CPen specialSolidPen(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色实线
    //CPen thickSolidPen(PS_SOLID, 2, RGB(160, 32, 32)); // 更亮的深红色粗实线
    //CPen* pGridOldPen = pDC->SelectObject(&verticalGridPen);  // 保存之前的画笔

    //// 使用之前在价格图中计算和存储的垂直线位置，确保完全对齐
    //for (size_t i = 0; i < m_vertLinePositions.size(); i++) {
    //    int gridX = m_vertLinePositions[i];
    //    
    //    // 根据线索选择合适的画笔样式，保持与价格图一致
    //    // 假设第1和第5个位置(索引0和4)使用实线，第3个位置(索引2)使用粗线
    //    if (i == 1 || i == 5) {
    //        pDC->SelectObject(&specialSolidPen);
    //    } else if (i == 3) {
    //        pDC->SelectObject(&thickSolidPen);
    //    } else {
    //        pDC->SelectObject(&verticalGridPen);
    //    }
    //    
    //    // 绘制分隔线
    //    pDC->MoveTo(gridX, chartRect.top);
    //    pDC->LineTo(gridX, chartRect.bottom);
    //}

    //// 恢复原有画笔
    //pDC->SelectObject(pGridOldPen);
    //
    //// 绘制成交量刻度（右侧）
    //pDC->SetTextColor(RGB(200, 200, 200));
    //pDC->SetBkMode(TRANSPARENT);
    //
    //// 创建字体用于成交量刻度显示
    //CFont smallFont;
    //smallFont.CreatePointFont(70, _T("微软雅黑 Light")); // 使用微软雅黑 Light
    //CFont* pLocalFont = pDC->SelectObject(&smallFont);
    //
    //// 添加水平分割线 - 在成交量图绘制3条均匀分布的水平线
    //CPen horizontalGridPen(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细线
    //pDC->SelectObject(&horizontalGridPen);
    //
    //int numHLines = 3; // 将成交量区分为4个区域
    //int volHeight = chartRect.Height();
    //int volSpacing = volHeight / (numHLines + 1);
    //
    //for (int i = 1; i <= numHLines; i++) {
    //    int y = chartRect.top + (i * volSpacing);
    //    
    //    // 中间线绘制为更亮的深红色粗线
    //    if (i == numHLines / 2 + 1) {
    //        CPen thickHGridPen(PS_SOLID, 2, RGB(160, 32, 32)); // 更亮的深红色粗线
    //        CPen* pTempPen = pDC->SelectObject(&thickHGridPen);
    //        pDC->MoveTo(chartRect.left, y);
    //        pDC->LineTo(chartRect.right, y);
    //        pDC->SelectObject(pTempPen);
    //    } else {
    //        pDC->MoveTo(chartRect.left + leftMargin, y);
    //        pDC->LineTo(chartRect.right - rightMargin, y);
    //    }
    //    
    //    // 在右侧绘制刻度值
    //    double volumeValue = m_maxVolume * (numHLines + 1 - i) / (numHLines + 1);
    //    CString volValueStr;
    //    volValueStr.Format(_T("%.0f"), volumeValue / 100); // 转为手
    //    int textY = y - 8; // 稍微向上偏移文本
    //    pDC->TextOut(chartRect.right + 5, textY, volValueStr);
    //}
    //
    //// 已有顶部最大值和底部0值的绘制
    //CString volStr;
    //volStr.Format(_T("%.0f"), m_maxVolume / 100); // 转为手
    //pDC->TextOut(chartRect.right + 5, chartRect.top, volStr);
    //
    //volStr.Format(_T("0"));
    //pDC->TextOut(chartRect.right + 5, chartRect.bottom - 15, volStr);
    //
    //// 恢复原有字体并释放新字体
    //pDC->SelectObject(pLocalFont);
    //smallFont.DeleteObject();
    //
    //pDC->SelectObject(pOldPen);
}

// 绘制时间轴
void CKLineTime::DrawTimeAxis(CDC* pDC)
{
    CRect chartRect = m_chartRect.timeAxisRect;
    // 留出左右边距，与上方图表对齐
    int leftMargin = 70;
    int rightMargin = 70;
    // 只保留左右边距，上边距设为0，使时间轴文字紧贴成交量区
    chartRect.DeflateRect(leftMargin, 0, rightMargin, 5);
    
    // 创建更小的字体用于时间轴标签显示
    CFont smallFont;
    smallFont.CreatePointFont(70, _T("微软雅黑 Light")); // 使用微软雅黑 Light
    CFont* pOldFont = pDC->SelectObject(&smallFont);
    
    // 绘制时间刻度
    pDC->SetTextColor(RGB(200, 200, 200));
    pDC->SetBkMode(TRANSPARENT);
    
    // 主要时间点
    CStringArray timeLabels;
    timeLabels.Add(_T("9:15"));
    timeLabels.Add(_T("9:30"));
    timeLabels.Add(_T("10:00"));
    timeLabels.Add(_T("10:30"));
    timeLabels.Add(_T("11:00"));
    timeLabels.Add(_T("11:30"));
    timeLabels.Add(_T("13:30"));
    timeLabels.Add(_T("14:00"));
    timeLabels.Add(_T("14:30"));
    timeLabels.Add(_T("15:00"));
    
    // 集合竞价区宽度比例
    double auctionWidthRatio = 0.1;
    double auctionWidth = chartRect.Width() * auctionWidthRatio;
    double normalWidth = chartRect.Width() - auctionWidth;
    
    // 计算并绘制时间刻度
    for (int i = 0; i < timeLabels.GetSize(); i++) {
        int x;
        
        if (i == 0) {
            // 9:15 开始点
            x = chartRect.left;
        }
        else if (i == 1) {
            // 9:30 分界点
            x = chartRect.left + auctionWidth;
        }
        else {
            // 其他时间点，在分时区均匀分布
            double ratio = (i - 1.0) / (timeLabels.GetSize() - 2.0);
            x = chartRect.left + auctionWidth + (int)(normalWidth * ratio);
        }
        
        // 绘制时间标签 - 修改为顶部对齐，使标签紧贴成交量区
        CRect textRect(x - 20, chartRect.top, x + 20, chartRect.top + 15);
        pDC->DrawText(timeLabels[i], textRect, DT_CENTER | DT_TOP | DT_SINGLELINE);
        
        // 绘制刻度线 - 保持刻度线在顶部
        pDC->MoveTo(x, chartRect.top);
        pDC->LineTo(x, chartRect.top + 5);
    }
    
    // 恢复原有字体并释放新字体
    pDC->SelectObject(pOldFont);
    smallFont.DeleteObject();
}

// 绘制价格和涨幅坐标
void CKLineTime::DrawCoordinates(CDC* pDC)
{
    CRect chartRect = m_chartRect.priceRect;
    int leftMargin = 70;
    int rightMargin = 70;
    int chartLeft = chartRect.left + leftMargin;
    int chartRight = chartRect.right - rightMargin;
    
    // 计算价格范围
    double priceRange;
    double priceMid;
    
    if (m_bLimitCoordinate) {
        // 使用涨停板坐标
        priceMid = m_basePrice;
        priceRange = m_basePrice * 0.2; // 假设涨跌停幅度为10%，所以总范围是20%
    } else {
        // 使用实时价格坐标
        priceMid = (m_highestPrice + m_lowestPrice) / 2;
        priceRange = (m_highestPrice - m_lowestPrice) * 1.1; // 留出10%的余量
        if (priceRange < 0.01) priceRange = 0.01; // 防止除零错误
    }
    
    double priceMin = priceMid - priceRange / 2;
    double priceMax = priceMid + priceRange / 2;
    
    // 创建更小的字体用于坐标点显示
    CFont smallFont;
    smallFont.CreatePointFont(70, _T("微软雅黑 Light")); // 使用微软雅黑 Light
    CFont* pOldFont = pDC->SelectObject(&smallFont);
    
    // 设置文本属性
    pDC->SetTextColor(RGB(200, 200, 200));
    pDC->SetBkMode(TRANSPARENT);
    
    // 准备深红色细线画笔用于绘制分割线
    CPen gridPen(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细线
    CPen* pOldPen = pDC->SelectObject(&gridPen);
    
    // 绘制顶部价格坐标 - 根据与基准价格的关系设置颜色
    double topPrice = priceMax;
    double topPercent = (topPrice - m_basePrice) / m_basePrice * 100;
    
    if (topPrice > m_basePrice) {
        pDC->SetTextColor(RGB(255, 0, 0)); // 红色
    } else if (topPrice < m_basePrice) {
        pDC->SetTextColor(RGB(0, 255, 0)); // 绿色
    } else {
        pDC->SetTextColor(RGB(255, 255, 255)); // 白色
    }
    
    // 绘制左侧顶部价格刻度
    CString topPriceStr;
    topPriceStr.Format(_T("%.2f"), topPrice);
    // 调整文本位置，确保与顶部边界线精确对齐
    CRect topLeftTextRect(chartRect.left, chartRect.top, chartLeft - 5, chartRect.top + 20);
    pDC->DrawText(topPriceStr, topLeftTextRect, DT_RIGHT | DT_TOP | DT_SINGLELINE);
    
    // 绘制右侧顶部涨幅刻度
    CString topPercentStr;
    if (topPercent > 0)
        topPercentStr.Format(_T("+%.2f%%"), topPercent);
    else
        topPercentStr.Format(_T("%.2f%%"), topPercent);
        
    CRect topRightTextRect(chartRight + 5, chartRect.top, chartRect.right, chartRect.top + 20);
    pDC->DrawText(topPercentStr, topRightTextRect, DT_LEFT | DT_TOP | DT_SINGLELINE);
    
    // ===== 修改部分开始 =====
    // 绘制水平网格线，采用固定的12等分区域（11条分割线），不受坐标系切换影响
    int numGridLines = 11; // 11条分割线分成12个区域
    
    // 先绘制所有水平分割线 - 这部分不依赖于价格计算，确保位置固定
    for (int i = 1; i <= numGridLines; i++) {
        // 计算在12等分区域中的位置，使用精确的浮点数计算
        double ratio = static_cast<double>(i) / (numGridLines + 1);
        
        // 使用更精确的坐标计算，确保网格线均匀分布
        int totalHeight = chartRect.Height();
        int gridSpacing = totalHeight / (numGridLines + 1);
        int y = chartRect.top + (i * gridSpacing);
        
        // 如果是第6根水平线（中线），使用更粗的线和不同颜色
        if (i == 6) {
            CPen thickGridPen(PS_SOLID, 2, RGB(160, 32, 32)); // 更亮的深红色粗线
            pDC->SelectObject(&thickGridPen);
            pDC->MoveTo(chartRect.left, y); // 从图表最左侧开始，包括左边距
            pDC->LineTo(chartRect.right, y); // 一直到图表最右侧，包括右边距
            pDC->SelectObject(&gridPen); // 恢复原来的细线画笔
        } else {
            // 绘制普通水平分割线
            pDC->MoveTo(chartLeft, y);
            pDC->LineTo(chartRight, y);
        }
        
        // 计算该位置对应的价格值（基于当前选择的坐标系）
        double price = priceMin + (priceRange * ratio);
        double percent = (price - m_basePrice) / m_basePrice * 100;
        
        // 根据价格与基准价格的关系设置不同颜色，不随坐标系切换而变化
        COLORREF textColor;
        if (price > m_basePrice) {
            // 价格高于基准价格，显示红色
            textColor = RGB(255, 0, 0);
        } else if (price < m_basePrice) {
            // 价格低于基准价格，显示绿色
            textColor = RGB(0, 255, 0);
        } else {
            // 价格等于基准价格，显示白色
            textColor = RGB(255, 255, 255);
        }
        pDC->SetTextColor(textColor);
        
        // 绘制左侧价格刻度
        CString priceStr;
        priceStr.Format(_T("%.2f"), price);
        CRect leftTextRect(chartRect.left, y - 10, chartLeft - 5, y + 10);
        pDC->DrawText(priceStr, leftTextRect, DT_RIGHT | DT_VCENTER | DT_SINGLELINE);
        
        // 绘制右侧涨幅刻度
        CString percentStr;
        if (percent > 0)
            percentStr.Format(_T("+%.2f%%"), percent);
        else
            percentStr.Format(_T("%.2f%%"), percent);
            
        CRect rightTextRect(chartRight + 5, y - 10, chartRect.right, y + 10);
        pDC->DrawText(percentStr, rightTextRect, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
    }
    // ===== 修改部分结束 =====
    
    // 绘制底部价格坐标 - 根据与基准价格的关系设置颜色
    double bottomPrice = priceMin;
    double bottomPercent = (bottomPrice - m_basePrice) / m_basePrice * 100;
    
    if (bottomPrice > m_basePrice) {
        pDC->SetTextColor(RGB(255, 0, 0)); // 红色
    } else if (bottomPrice < m_basePrice) {
        pDC->SetTextColor(RGB(0, 255, 0)); // 绿色
    } else {
        pDC->SetTextColor(RGB(255, 255, 255)); // 白色
    }
    
    // 绘制左侧底部价格刻度
    CString bottomPriceStr;
    bottomPriceStr.Format(_T("%.2f"), bottomPrice);
    // 调整文本位置，确保与底部边界线精确对齐
    CRect bottomLeftTextRect(chartRect.left, chartRect.bottom - 20, chartLeft - 5, chartRect.bottom);
    pDC->DrawText(bottomPriceStr, bottomLeftTextRect, DT_RIGHT | DT_BOTTOM | DT_SINGLELINE);
    
    // 绘制右侧底部涨幅刻度
    CString bottomPercentStr;
    if (bottomPercent > 0)
        bottomPercentStr.Format(_T("+%.2f%%"), bottomPercent);
    else
        bottomPercentStr.Format(_T("%.2f%%"), bottomPercent);
        
    CRect bottomRightTextRect(chartRight + 5, chartRect.bottom - 20, chartRect.right, chartRect.bottom);
    pDC->DrawText(bottomPercentStr, bottomRightTextRect, DT_LEFT | DT_BOTTOM | DT_SINGLELINE);
    
    // 恢复原有画笔
    pDC->SelectObject(pOldPen);
    
    // 恢复原有字体并释放新字体
    pDC->SelectObject(pOldFont);
    smallFont.DeleteObject();
}


// 窗口大小改变时重新计算图表布局
void CKLineTime::OnSize(UINT nType, int cx, int cy)
{
    CView::OnSize(nType, cx, cy);
    
    // 重绘
    Invalidate();
}

// 拦截擦除背景消息，避免闪烁
BOOL CKLineTime::OnEraseBkgnd(CDC* pDC)
{
    // 返回TRUE表示我们已经处理了背景擦除
    // 实际上我们不做任何擦除操作，因为双缓冲绘图会完全覆盖窗口
    return TRUE;
}

// CKLineTime 消息处理程序

void CKLineTime::OnTimer(UINT_PTR nIDEvent)
{
    //if (nIDEvent == TIMER_DEMO)
    //{
    //    // 更新演示索引，每次向前移动多个数据点以提高流畅度
    //    m_nDemoIndex += 3; // 每次移动3个数据点
    //    
    //    // 如果到达数据末尾，重置为0
    //    if (m_nDemoIndex >= (int)m_timeData.size())
    //    {
    //        m_nDemoIndex = 0;
    //    }
    //    
    //    // 仅重绘价格图和成交量图区域
    //    InvalidateRect(m_chartRect.priceRect, FALSE);
    //    InvalidateRect(m_chartRect.volumeRect, FALSE);
    //}
    
    CView::OnTimer(nIDEvent);
}

// 开始/停止动态演示
void CKLineTime::OnStartStopDemo()
{
    m_bDemoRunning = !m_bDemoRunning;
    
    if (m_bDemoRunning)
    {
        // 开始演示，重置索引并启动定时器
        m_nDemoIndex = 0;
        SetTimer(TIMER_DEMO, 100, NULL); // 每100毫秒更新一次
    }
    else
    {
        // 停止演示，关闭定时器
        KillTimer(TIMER_DEMO);
        
        // 重绘整个视图
        Invalidate();
    }
}

// 左键点击时处理十字光标
void CKLineTime::OnLButtonDown(UINT nFlags, CPoint point)
{
    // 检查点击是否在价格图区域或成交量图区域内
    bool isInPriceChart = m_chartRect.priceRect.PtInRect(point);
    bool isInVolumeChart = m_chartRect.volumeRect.PtInRect(point);
    
    if (isInPriceChart || isInVolumeChart) {
        // 切换十字光标显示状态
        m_bShowCrossCursor = !m_bShowCrossCursor;
        
        if (m_bShowCrossCursor) {
            // 如果启用十字光标，立即更新位置
            OnMouseMove(nFlags, point);
        } else {
            // 如果关闭十字光标，重绘整个视图
            Invalidate();
        }
    }
    else {
        // 点击在其他区域，关闭十字光标
        if (m_bShowCrossCursor) {
            m_bShowCrossCursor = false;
            Invalidate(); // 重绘整个视图
        }
    }
    
    CView::OnLButtonDown(nFlags, point);
}

// 鼠标移动时更新十字光标位置
void CKLineTime::OnMouseMove(UINT nFlags, CPoint point)
{
    //// 检查鼠标是否在价格图区域或成交量图区域内
    //bool isInPriceChart = m_chartRect.priceRect.PtInRect(point);
    //bool isInVolumeChart = m_chartRect.volumeRect.PtInRect(point);
    //
    //if (isInPriceChart || isInVolumeChart) {
    //    // 获取chartRect
    //    CRect chartRect = isInPriceChart ? m_chartRect.priceRect : m_chartRect.volumeRect;
    //    
    //    // 留出左右两侧的边距
    //    int leftMargin = 70;
    //    int rightMargin = 70;
    //    chartRect.DeflateRect(leftMargin, isInPriceChart ? 10 : 5, rightMargin, isInPriceChart ? 10 : 0);
    //    
    //    // 计算集合竞价区与分时区的分界点
    //    int auctionEndIndex = 0;
    //    CTime auctionEndTime(2023, 1, 1, 9, 30, 0); // 9:30分界
    //    
    //    for (size_t i = 0; i < m_timeData.size(); i++) {
    //        if (m_timeData[i].time >= auctionEndTime) {
    //            auctionEndIndex = i;
    //            break;
    //        }
    //    }
    //    
    //    // 集合竞价区宽度比例
    //    double auctionWidthRatio = 0.1;
    //    double auctionWidth = chartRect.Width() * auctionWidthRatio;
    //    double normalWidth = chartRect.Width() - auctionWidth;
    //    
    //    // 点击位置的X坐标相对于左边距的偏移
    //    int relativeX = point.x - chartRect.left;
    //    
    //    // 计算正常交易区起始X坐标
    //    int chartLeft = chartRect.left;
    //    if (m_bShowAuctionArea) {
    //        chartLeft = chartRect.left + auctionWidth;
    //    }
    //    
    //    // 判断鼠标是否在集合竞价区域内
    //    bool isInAuctionArea = m_bShowAuctionArea && (relativeX < auctionWidth);
    //    
    //    // 如果鼠标在集合竞价区，不显示十字光标，但仍然更新信息
    //    if (isInAuctionArea) {
    //        m_bShowCrossCursor = false;
    //        
    //        // 计算对应的数据索引
    //        int dataIndex = (int)((double)relativeX / auctionWidth * auctionEndIndex);
    //        dataIndex = max(0, min(dataIndex, (int)m_timeData.size() - 1));
    //        m_crossDataIndex = dataIndex;
    //        
    //        // 强制重绘，更新信息栏
    //        Invalidate();
    //        
    //        return;
    //    }
    //    
    //    // 开启十字光标
    //    m_bShowCrossCursor = true;
    //    m_crossCursorPos = point;
    //    
    //    // 计算对应的数据索引
    //    double positionRatio;
    //    if (m_bShowAuctionArea) {
    //        positionRatio = (double)(relativeX - auctionWidth) / normalWidth;
    //    } else {
    //        positionRatio = (double)relativeX / chartRect.Width();
    //    }
    //    
    //    int dataIndex = auctionEndIndex + (int)(positionRatio * (m_timeData.size() - auctionEndIndex));
    //    dataIndex = max(0, min(dataIndex, (int)m_timeData.size() - 1));
    //    m_crossDataIndex = dataIndex;
    //    
    //    // 强制重绘，显示十字光标
    //    Invalidate();
    //} else {
    //    // 如果鼠标不在图表区域内，关闭十字光标
    //    if (m_bShowCrossCursor) {
    //        m_bShowCrossCursor = false;
    //        Invalidate();
    //    }
    //}
    
    CView::OnMouseMove(nFlags, point);
}

// 绘制十字光标
void CKLineTime::DrawCrossCursor(CDC* pDC)
{
    //if (!m_bShowCrossCursor || m_crossDataIndex < 0 || m_crossDataIndex >= (int)m_timeData.size())
    //    return;
    //
    //// 计算集合竞价区域分隔线位置
    //CRect priceChartRect = m_chartRect.priceRect;
    //CRect volumeChartRect = m_chartRect.volumeRect;
    //priceChartRect.DeflateRect(70, 10, 70, 10);
    //volumeChartRect.DeflateRect(70, 10, 70, 0);
    //
    //double auctionWidthRatio = 0.1;
    //double priceAuctionWidth = priceChartRect.Width() * auctionWidthRatio;
    //double volumeAuctionWidth = volumeChartRect.Width() * auctionWidthRatio;
    //
    //int priceSeparatorX = priceChartRect.left + priceAuctionWidth;
    //int volumeSeparatorX = volumeChartRect.left + volumeAuctionWidth;
    //
    //// 检查光标是否在集合竞价区域
    //bool isInAuctionArea = m_bShowAuctionArea && 
    //    ((m_crossCursorPos.x < priceSeparatorX && m_chartRect.priceRect.PtInRect(m_crossCursorPos)) ||
    //     (m_crossCursorPos.x < volumeSeparatorX && m_chartRect.volumeRect.PtInRect(m_crossCursorPos)));
    //     
    //// 如果在集合竞价区域不显示十字光标
    //if (isInAuctionArea) 
    //    return;
    //
    //// 十字光标颜色设置为白色
    //COLORREF cursorColor = RGB(255, 255, 255);
    //
    //// 创建实线画笔 (PS_SOLID样式)
    //CPen cursorPen(PS_SOLID, 1, cursorColor);
    //CPen* pOldPen = pDC->SelectObject(&cursorPen);
    //
    //// 绘制垂直线 - 穿过价格图和成交量图
    //int verticalTop = min(m_chartRect.priceRect.top, m_chartRect.infoRect.bottom);
    //int verticalBottom = max(m_chartRect.volumeRect.bottom, m_chartRect.timeAxisRect.top);
    //
    //pDC->MoveTo(m_crossCursorPos.x, verticalTop);
    //pDC->LineTo(m_crossCursorPos.x, verticalBottom);
    //
    //// 绘制水平线 - 在当前光标所在的图表区域绘制
    //if (m_chartRect.priceRect.PtInRect(m_crossCursorPos)) {
    //    // 在价格图中绘制水平线
    //    pDC->MoveTo(m_chartRect.priceRect.left, m_crossCursorPos.y);
    //    pDC->LineTo(m_chartRect.priceRect.right, m_crossCursorPos.y);
    //} 
    //else if (m_chartRect.volumeRect.PtInRect(m_crossCursorPos)) {
    //    // 在成交量图中绘制水平线
    //    pDC->MoveTo(m_chartRect.volumeRect.left, m_crossCursorPos.y);
    //    pDC->LineTo(m_chartRect.volumeRect.right, m_crossCursorPos.y);
    //}
    //
    //// 恢复原画笔
    //pDC->SelectObject(pOldPen);
}

// 切换集合竞价区域显示状态
void CKLineTime::OnToggleAuctionArea()
{
    // 切换显示状态
    m_bShowAuctionArea = !m_bShowAuctionArea;
    
    // 重绘
    Invalidate();
}

// 绘制集合竞价区域
void CKLineTime::DrawAuctionArea(CDC* pDC, const CRect& priceChartRect, const CRect& volumeChartRect, int auctionEndIndex)
{
    //if (m_timeData.empty() || auctionEndIndex <= 0 || !m_bShowAuctionArea)
    //    return;
    //    
    //// 集合竞价区宽度比例
    //double auctionWidthRatio = 0.1;
    //
    //// 计算价格图中的集合竞价区域
    //double priceAuctionWidth = priceChartRect.Width() * auctionWidthRatio;
    //CRect priceBgRect(priceChartRect.left, priceChartRect.top, 
    //                priceChartRect.left + priceAuctionWidth, priceChartRect.bottom);
    //
    //// 计算成交量图中的集合竞价区域
    //double volumeAuctionWidth = volumeChartRect.Width() * auctionWidthRatio;
    //CRect volumeBgRect(volumeChartRect.left, volumeChartRect.top, 
    //                 volumeChartRect.left + volumeAuctionWidth, volumeChartRect.bottom);
    //
    //// 绘制集合竞价区域背景色
    //COLORREF auctionBgColor = RGB(30, 0, 0); // 浅深红色背景
    //pDC->FillSolidRect(priceBgRect, auctionBgColor);
    //pDC->FillSolidRect(volumeBgRect, auctionBgColor);
    //
    //// 动态演示的索引边界
    //int displayEndIndex = m_timeData.size();
    //if (m_bDemoRunning && m_nDemoIndex > 0) {
    //    displayEndIndex = min(m_nDemoIndex, (int)m_timeData.size());
    //}
    //
    //// 计算可见区域的最高和最低价格
    //double priceMax, priceMin;
    //
    //// 确定价格边界
    //if (m_bLimitCoordinate) {
    //    // 涨停板坐标：基准价格的 ±10%
    //    priceMax = m_basePrice * 1.1;
    //    priceMin = m_basePrice * 0.9;
    //} else {
    //    // 实时价格坐标：取数据中的最高最低价，并留出一些边距
    //    priceMax = m_highestPrice * 1.01;
    //    priceMin = m_lowestPrice * 0.99;
    //}
    //
    //double priceRange = priceMax - priceMin;
    //// 计算价格到Y坐标的转换比例
    //double priceToY = priceChartRect.Height() / priceRange;
    //
    //// ----- 绘制分时图集合竞价区域 -----
    //
    //// 集合竞价区域价格线点集
    //std::vector<CPoint> auctionPricePoints;
    //std::vector<CPoint> auctionAvgPoints;
    //
    //// 收集集合竞价时段的价格点和均价点
    //for (size_t i = 0; i < auctionEndIndex && i < displayEndIndex; i++) {
    //    // 计算X坐标（在集合竞价区）
    //    double relativePos = static_cast<double>(i) / auctionEndIndex;
    //    int x = priceChartRect.left + static_cast<int>(priceAuctionWidth * relativePos + 0.5); // 四舍五入
    //    
    //    // 计算价格的Y坐标，提高精度
    //    double priceOffset = m_timeData[i].price - priceMin;
    //    int priceY = priceChartRect.bottom - static_cast<int>(priceOffset * priceToY + 0.5); // 四舍五入
    //    priceY = max(priceChartRect.top, min(priceChartRect.bottom, priceY));
    //    
    //    // 计算均价的Y坐标，提高精度
    //    double avgOffset = m_timeData[i].avgPrice - priceMin;
    //    int avgY = priceChartRect.bottom - static_cast<int>(avgOffset * priceToY + 0.5); // 四舍五入
    //    avgY = max(priceChartRect.top, min(priceChartRect.bottom, avgY));
    //    
    //    // 添加到点集合
    //    auctionPricePoints.push_back(CPoint(x, priceY));
    //    auctionAvgPoints.push_back(CPoint(x, avgY));
    //}
    //
    //// 绘制集合竞价价格线（白色虚线）
    //if (auctionPricePoints.size() > 1) {
    //    CPen auctionPricePen(PS_DOT, 1, m_priceLinesColor);
    //    CPen* pOldPen = pDC->SelectObject(&auctionPricePen);
    //    
    //    pDC->MoveTo(auctionPricePoints[0]);
    //    for (size_t i = 1; i < auctionPricePoints.size(); i++) {
    //        pDC->LineTo(auctionPricePoints[i]);
    //    }
    //    pDC->SelectObject(pOldPen);
    //}
    //
    //// 绘制集合竞价均线（黄色虚线）
    //if (auctionAvgPoints.size() > 1) {
    //    CPen auctionAvgPen(PS_DOT, 1, m_avgLineColor);
    //    CPen* pOldPen = pDC->SelectObject(&auctionAvgPen);
    //    
    //    pDC->MoveTo(auctionAvgPoints[0]);
    //    for (size_t i = 1; i < auctionAvgPoints.size(); i++) {
    //        pDC->LineTo(auctionAvgPoints[i]);
    //    }
    //    pDC->SelectObject(pOldPen);
    //}
    //
    //// ----- 绘制成交量图集合竞价区域 -----
    //
    //// 绘制集合竞价期间的成交量数据
    //for (size_t i = 0; i < auctionEndIndex && i < displayEndIndex; i++) {
    //    // 计算X坐标，只在集合竞价区域绘制
    //    double relativePos = static_cast<double>(i) / auctionEndIndex;
    //    int x = volumeChartRect.left + static_cast<int>(volumeAuctionWidth * relativePos + 0.5); // 四舍五入
    //    double auctionBarWidth = static_cast<double>(volumeAuctionWidth) / auctionEndIndex - 1;
    //    if (auctionBarWidth < 1) auctionBarWidth = 1;
    //    
    //    // 计算高度，提高精度
    //    double volumeRatio = m_timeData[i].volume / m_maxVolume;
    //    int height = static_cast<int>(volumeChartRect.Height() * volumeRatio + 0.5); // 四舍五入
    //    if (height < 1) height = 1;
    //    
    //    // 设置颜色 - 使用点线表示集合竞价区的成交量
    //    COLORREF lineColor;
    //    switch (m_timeData[i].priceDirection) {
    //        case 1:  // 上涨
    //            lineColor = RGB(255, 255, 0); // 黄色表示上涨
    //            break;
    //        case -1: // 下跌
    //            lineColor = RGB(0, 162, 232); // 蓝色表示下跌
    //            break;
    //        default: // 平盘
    //            lineColor = RGB(255, 255, 255); // 白色表示价格未变
    //            break;
    //    }
    //    
    //    // 绘制集合竞价成交量竖线 - 使用点线样式
    //    CPen auctionVolumePen(PS_DOT, 1, lineColor);
    //    CPen* pOldPen = pDC->SelectObject(&auctionVolumePen);
    //    
    //    // 计算竖线中心点X坐标
    //    int lineX = x + (int)(auctionBarWidth / 2);
    //    
    //    // 以底部对齐绘制竖线
    //    pDC->MoveTo(lineX, volumeChartRect.bottom);
    //    pDC->LineTo(lineX, volumeChartRect.bottom - height);
    //    
    //    // 恢复之前的画笔状态
    //    pDC->SelectObject(pOldPen);
    //}
    //
    //// ----- 绘制分隔线和边框 -----
    //
    //// 计算分隔线位置
    //int priceSeparatorX = priceChartRect.left + priceAuctionWidth;
    //int volumeSeparatorX = volumeChartRect.left + volumeAuctionWidth;
    //
    //// 绘制集合竞价区与正常交易区的分隔线（深红色实线）
    //CPen separatorPen(PS_SOLID, 2, RGB(160, 32, 32)); // 使用更亮的深红色，与图表中线保持一致
    //CPen* pOldPen = pDC->SelectObject(&separatorPen);
    //
    //// 绘制价格图区域的分隔线
    //pDC->MoveTo(priceSeparatorX, priceChartRect.top);
    //pDC->LineTo(priceSeparatorX, priceChartRect.bottom + 1);
    //
    //// 绘制成交量图区域的分隔线
    //pDC->MoveTo(volumeSeparatorX, volumeChartRect.top);
    //pDC->LineTo(volumeSeparatorX, volumeChartRect.bottom);
    //
    //// 绘制横向分割线
    //CPen horizontalPen(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细线
    //CPen* pHorizOldPen = pDC->SelectObject(&horizontalPen);

    //// 仅当显示集合竞价区域时才绘制中间的横线
    //if (m_bShowAuctionArea) {
    //    // 绘制价格图竞价区中间的横向分割线
    //    int priceAuctionMidY = priceChartRect.top + priceChartRect.Height() / 2;
    //    pDC->MoveTo(priceChartRect.left, priceAuctionMidY);
    //    pDC->LineTo(priceSeparatorX, priceAuctionMidY);
    //    
    //    // 绘制成交量图竞价区中间的横向分割线
    //    int volumeAuctionMidY = volumeChartRect.top + volumeChartRect.Height() / 2;
    //    pDC->MoveTo(volumeChartRect.left, volumeAuctionMidY);
    //    pDC->LineTo(volumeSeparatorX, volumeAuctionMidY);
    //    
    //    // 在集合竞价区绘制更多的水平线，增强区域划分
    //    // 为价格图的竞价区添加上1/4和下1/4位置的水平线
    //    int priceQuarterY1 = priceChartRect.top + priceChartRect.Height() / 4;
    //    int priceQuarterY3 = priceChartRect.top + priceChartRect.Height() * 3 / 4;
    //    
    //    pDC->MoveTo(priceChartRect.left, priceQuarterY1);
    //    pDC->LineTo(priceSeparatorX, priceQuarterY1);
    //    
    //    pDC->MoveTo(priceChartRect.left, priceQuarterY3);
    //    pDC->LineTo(priceSeparatorX, priceQuarterY3);
    //}

    //pDC->SelectObject(pHorizOldPen);
}

// 键盘按键消息处理
void CKLineTime::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags)
{
    switch (nChar)
    {
    case VK_RETURN: // 回车键：切换到K线视图
        SwitchToKLineView();
        break;
    case VK_UP: // 上方向键：切换到上一支股票
        SwitchToPrevStock();
        break;
    case VK_DOWN: // 下方向键：切换到下一支股票
        SwitchToNextStock();
        break;
    case VK_LEFT: // 左方向键：切换到前一日分时图
        SwitchToPrevDay();
        break;
    case VK_RIGHT: // 右方向键：切换到后一日分时图
        SwitchToNextDay();
        break;
    default:
        CView::OnKeyDown(nChar, nRepCnt, nFlags);
        break;
    }
}

// 鼠标滚轮消息处理
BOOL CKLineTime::OnMouseWheel(UINT nFlags, short zDelta, CPoint pt)
{
    // 在切换股票之前隐藏十字光标
    m_bShowCrossCursor = false;
    
    // zDelta > 0 表示滚轮向前滚动(远离用户)，对应上方向键
    // zDelta < 0 表示滚轮向后滚动(朝向用户)，对应下方向键
    if (zDelta > 0) {
        // 向前滚动，切换到上一支股票
        SwitchToPrevStock();
    } else if (zDelta < 0) {
        // 向后滚动，切换到下一支股票
        SwitchToNextStock();
    }
    
    // 返回TRUE表示已处理该消息
    return TRUE;
}

// 切换到K线视图
void CKLineTime::SwitchToKLineView()
{
    // 在实际应用中，这里应该调用文档或主框架的方法来切换视图
    // 这里只是一个模拟实现，显示一个消息框提示用户
   // AfxMessageBox(_T("切换到K线视图"), MB_ICONINFORMATION);
}

// 切换到上一支股票
void CKLineTime::SwitchToPrevStock()
{
    // 获取文档对象
    CStockDoc* pDoc = GetDocument();
    if (pDoc)
    {
        // 调用文档的方法切换到上一支股票
        pDoc->ShowPreviousStock();
    }
}

// 切换到下一支股票
void CKLineTime::SwitchToNextStock()
{
    // 获取文档对象
    CStockDoc* pDoc = GetDocument();
    if (pDoc)
    {
        // 调用文档的方法切换到下一支股票
        pDoc->ShowNextStock();
    }
}

// 切换到前一日分时图
void CKLineTime::SwitchToPrevDay()
{
    // 模拟实现
   // AfxMessageBox(_T("切换到前一日分时图"), MB_ICONINFORMATION);
    
    // 在实际应用中，这里应该重新加载前一天的数据，然后更新显示
    // 可以利用已有的测试数据生成函数，做一些微调来模拟不同日期的数据
    // GenerateTestData(); // 如果要实现实际切换效果，可以取消注释
    // Invalidate(); // 重绘视图
}

// 切换到后一日分时图
void CKLineTime::SwitchToNextDay()
{
    // 模拟实现
    //AfxMessageBox(_T("切换到后一日分时图"), MB_ICONINFORMATION);
    
    // 在实际应用中，这里应该重新加载后一天的数据，然后更新显示
    // 可以利用已有的测试数据生成函数，做一些微调来模拟不同日期的数据
    // GenerateTestData(); // 如果要实现实际切换效果，可以取消注释
    // Invalidate(); // 重绘视图
}
