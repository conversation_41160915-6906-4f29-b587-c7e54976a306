﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="公共定义">
      <UniqueIdentifier>{926c7955-abe7-4e4b-a1da-5e2845f05f98}</UniqueIdentifier>
    </Filter>
    <Filter Include="系统组件">
      <UniqueIdentifier>{4a660e61-9200-46ee-80ca-bac6c02f6884}</UniqueIdentifier>
    </Filter>
    <Filter Include="分时视图">
      <UniqueIdentifier>{e1371a48-c2db-4f56-9e71-f5b5e92dc9c2}</UniqueIdentifier>
    </Filter>
    <Filter Include="技术视图">
      <UniqueIdentifier>{668cbcc8-88a1-418b-ab41-0979d65cdb67}</UniqueIdentifier>
    </Filter>
    <Filter Include="股票列表">
      <UniqueIdentifier>{cfd8c294-50b4-40ae-8a22-5dce50fd5fb0}</UniqueIdentifier>
    </Filter>
    <Filter Include="公共函数">
      <UniqueIdentifier>{c3a3a525-4110-4039-b03d-64d9d5d63cd0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Grid">
      <UniqueIdentifier>{7ee26026-f85d-46c5-b95d-bb8237e6b0d2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Grid\头文件">
      <UniqueIdentifier>{3439aa4b-c87d-4317-878e-788fd8336ee5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Grid\源文件">
      <UniqueIdentifier>{bec2bd88-caf3-4863-ab73-e3549a8e604d}</UniqueIdentifier>
    </Filter>
    <Filter Include="公共函数\头文件">
      <UniqueIdentifier>{56c52971-dfc9-4afc-8b0e-f20ad00cbaf0}</UniqueIdentifier>
    </Filter>
    <Filter Include="公共函数\源文件">
      <UniqueIdentifier>{d0593c95-15c7-40ef-8328-102ae89ee102}</UniqueIdentifier>
    </Filter>
    <Filter Include="数据中心">
      <UniqueIdentifier>{b041dd0a-7a66-4b16-b033-cfe842b071ec}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common">
      <UniqueIdentifier>{926c7955-abe7-4e4b-a1da-5e2845f05f98}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Stock.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="framework.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="MainFrm.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="StockDoc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="StockView.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="pch.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="StockSplitter.h">
      <Filter>系统组件</Filter>
    </ClInclude>
    <ClInclude Include="TabPage.h">
      <Filter>系统组件</Filter>
    </ClInclude>
    <ClInclude Include="StockStatusBar.h">
      <Filter>系统组件</Filter>
    </ClInclude>
    <ClInclude Include="StockDef.h">
      <Filter>公共定义</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UG64Bit.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGCBType.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGCell.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugceltyp.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGCnrBtn.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGCTarrw.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGCtrl.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugdefine.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGDLType.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGDrgDrp.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGDrwHnt.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGDtaSrc.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGEdit.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGEditBase.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugformat.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\uggdinfo.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGGrid.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGHint.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ughscrol.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\uglstbox.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGMEdit.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGMemMan.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGMultiS.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugprint.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugptrlst.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugsidehd.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGStrOp.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugstruct.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugtab.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\UGTopHdg.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugvscrol.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\Include\ugxpthemes.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\define.h">
      <Filter>公共函数\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\Common.h">
      <Filter>公共函数\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\httpHeader.h">
      <Filter>公共函数\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\JSONValue.h">
      <Filter>公共函数\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\inc\sqlite3mc.h">
      <Filter>公共函数\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\WinHttp.h">
      <Filter>公共函数\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTafnt.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTAutoSize.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTbutn.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTDropGrid.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTdtp.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\ugctelps.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTExpand.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTLabeled.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTMail.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTMailSort.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTMarquee.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTmfnt.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTNote.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\ugctnotewnd.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTOutlookHeader.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTpie.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTpro1.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTprog.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTRado.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTsarw.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTsldr.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Grid\CellTypes\UGCTSpin.h">
      <Filter>Grid\头文件</Filter>
    </ClInclude>
    <ClInclude Include="Kline\KLineIndicator.h">
      <Filter>技术视图</Filter>
    </ClInclude>
    <ClInclude Include="Kline\KLineInfo.h">
      <Filter>技术视图</Filter>
    </ClInclude>
    <ClInclude Include="Kline\KLineSignal.h">
      <Filter>技术视图</Filter>
    </ClInclude>
    <ClInclude Include="Kline\KLineTime.h">
      <Filter>技术视图</Filter>
    </ClInclude>
    <ClInclude Include="Kline\KLineView.h">
      <Filter>技术视图</Filter>
    </ClInclude>
    <ClInclude Include="Kline\KLineVolume.h">
      <Filter>技术视图</Filter>
    </ClInclude>
    <ClInclude Include="Time\TimeInfo.h">
      <Filter>分时视图</Filter>
    </ClInclude>
    <ClInclude Include="Time\TimeLine.h">
      <Filter>分时视图</Filter>
    </ClInclude>
    <ClInclude Include="Time\TimeView.h">
      <Filter>分时视图</Filter>
    </ClInclude>
    <ClInclude Include="Symbol\SymbolBar.h">
      <Filter>股票列表</Filter>
    </ClInclude>
    <ClInclude Include="Symbol\SymbolGrid.h">
      <Filter>股票列表</Filter>
    </ClInclude>
    <ClInclude Include="Symbol\SymbolView.h">
      <Filter>股票列表</Filter>
    </ClInclude>
    <ClInclude Include="LoadingDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\StringUtil.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ColumnConfigDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ColorDef.h">
      <Filter>公共定义</Filter>
    </ClInclude>
    <ClInclude Include="NetData.h">
      <Filter>数据中心</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Stock.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="MainFrm.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="StockDoc.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="StockView.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="StockSplitter.cpp">
      <Filter>系统组件</Filter>
    </ClCompile>
    <ClCompile Include="TabPage.cpp">
      <Filter>系统组件</Filter>
    </ClCompile>
    <ClCompile Include="StockStatusBar.cpp">
      <Filter>系统组件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGCBType.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGCell.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGCelTyp.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGCnrBtn.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGCTarrw.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGCtrl.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGDLType.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGDrgDrp.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGDrwHnt.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGDtaSrc.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGEdit.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGEditBase.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\ugformat.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\uggdinfo.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGGrid.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGHint.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\ughscrol.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\ugLstBox.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGMEdit.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGMemMan.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGMultiS.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\ugprint.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\ugptrlst.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\ugsidehd.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGStrOp.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\ugtab.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGTopHdg.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\ugvscrol.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\Source\UGXPThemes.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Common\Common.cpp">
      <Filter>公共函数\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Common\httpHeader.cpp">
      <Filter>公共函数\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Common\JSON.cpp">
      <Filter>公共函数\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Common\JSONValue.cpp">
      <Filter>公共函数\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Common\WinHttp.cpp">
      <Filter>公共函数\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTSpin.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTsldr.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTsarw.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTRado.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTprog.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTpro1.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTpie.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTOutlookHeader.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\ugctnotewnd.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTNote.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTmfnt.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTMarquee.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTMailSort.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTMail.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTLabeled.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTExpand.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\ugctelps.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTdtp.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTDropGrid.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTbutn.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTAutoSize.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\Grid\CellTypes\UGCTafnt.cpp">
      <Filter>Grid\源文件</Filter>
    </ClCompile>
    <ClCompile Include="Kline\KLineIndicator.cpp">
      <Filter>技术视图</Filter>
    </ClCompile>
    <ClCompile Include="Kline\KLineInfo.cpp">
      <Filter>技术视图</Filter>
    </ClCompile>
    <ClCompile Include="Kline\KLineSignal.cpp">
      <Filter>技术视图</Filter>
    </ClCompile>
    <ClCompile Include="Kline\KLineTime.cpp">
      <Filter>技术视图</Filter>
    </ClCompile>
    <ClCompile Include="Kline\KLineView.cpp">
      <Filter>技术视图</Filter>
    </ClCompile>
    <ClCompile Include="Kline\KLineVolume.cpp">
      <Filter>技术视图</Filter>
    </ClCompile>
    <ClCompile Include="Time\TimeInfo.cpp">
      <Filter>分时视图</Filter>
    </ClCompile>
    <ClCompile Include="Time\TimeLine.cpp">
      <Filter>分时视图</Filter>
    </ClCompile>
    <ClCompile Include="Time\TimeView.cpp">
      <Filter>分时视图</Filter>
    </ClCompile>
    <ClCompile Include="Symbol\SymbolBar.cpp">
      <Filter>股票列表</Filter>
    </ClCompile>
    <ClCompile Include="Symbol\SymbolGrid.cpp">
      <Filter>股票列表</Filter>
    </ClCompile>
    <ClCompile Include="Symbol\SymbolView.cpp">
      <Filter>股票列表</Filter>
    </ClCompile>
    <ClCompile Include="LoadingDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ColumnConfigDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NetData.cpp">
      <Filter>数据中心</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Stock.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\Stock.rc2">
      <Filter>资源文件</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\StockDoc.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\Stock.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\Toolbar.bmp">
      <Filter>资源文件</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <Manifest Include="res\Stock.manifest" />
  </ItemGroup>
</Project>