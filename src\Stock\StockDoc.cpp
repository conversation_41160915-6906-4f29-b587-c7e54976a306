﻿// StockDoc.cpp: CStockDoc 类的实现
//

#include "pch.h"
#include "framework.h"
// SHARED_HANDLERS可以在实现预览、缩略图和搜索筛选器句柄的
// ATL项目中进行定义，并允许与该项目共享文档代码。
#ifndef SHARED_HANDLERS
#include "Stock.h"
#endif

#include "StockDoc.h"
#include "LoadingDlg.h"
#include "MainFrm.h"
#include <atlconv.h>  // 支持CT2A和其他字符转换
#include "NetData.h"  // 添加NetData.h以使用CNetData类
#include "Time/TimeInfo.h"  // 添加TimeInfo头文件
#include <Shlwapi.h>  // 添加PathFileExists函数支持

#include "Symbol/SymbolView.h"
#include "Kline/KLineInfo.h"
#include <format>

#include <propkey.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

#pragma comment(lib, "Shlwapi.lib") // 链接Shlwapi库

// CStockDoc

IMPLEMENT_DYNCREATE(CStockDoc, CDocument)

BEGIN_MESSAGE_MAP(CStockDoc, CDocument)
END_MESSAGE_MAP()


// CStockDoc 构造/析构

CStockDoc::CStockDoc() noexcept
	: m_nCurrentStockIndex(-1), 
	  m_pendingUIUpdate(false)
{
	// 初始化股票相关变量
	m_strCurrentStock = "";
	m_pNetData = nullptr;
	m_CountMarket_SH_A = 0;			// 上证A股数量
	m_CountMarket_SZ_A = 0;			// 深证A股数量
	m_CountMarket_GEM = 0;			// 创业板数量
	m_CountMarket_STAR = 0;			// 科创板数量
	m_CountMarket_BJ_A = 0;			// 北证A股数量
	TCHAR szPath[MAX_PATH];
	GetModuleFileName(NULL, szPath, MAX_PATH);
	CString strAppPath(szPath);
	int nIndex = strAppPath.ReverseFind('\\');
	if (nIndex > 0)
		strAppPath = strAppPath.Left(nIndex + 1);

	// 设置数据库路径
	m_strDBPath = strAppPath + _T("Stock.db");
	
	// 初始化股票列表
	InitStockList();
}

CStockDoc::~CStockDoc()
{
	// 智能指针会自动清理m_pNetData，不需要手动删除
	// 如果需要在销毁前执行某些操作，可以在这里添加
	if (m_pNetData)
	{
		m_pNetData->Shutdown();
		// m_pNetData会在智能指针离开作用域时自动释放
	}
}

BOOL CStockDoc::OnNewDocument()
{
	if (!CDocument::OnNewDocument())
		return FALSE;

	return TRUE;
}


// CStockDoc 序列化

void CStockDoc::Serialize(CArchive& ar)
{
	if (ar.IsStoring())
	{
		// TODO: 在此添加存储代码
	}
	else
	{
		// TODO: 在此添加加载代码
	}
}

// CStockDoc 诊断

#ifdef _DEBUG
void CStockDoc::AssertValid() const
{
	CDocument::AssertValid();
}

void CStockDoc::Dump(CDumpContext& dc) const
{
	CDocument::Dump(dc);
}
#endif //_DEBUG


// 初始化股票列表
bool CStockDoc::InitStockList()
{
	try
	{
		// 清空原有列表
		m_vecStocks.clear();
		m_vecSymbols.clear();
		// 清空现有映射
		m_codeToIndexMap.clear();
		m_nameToIndexMap.clear();
		m_nameToCodeMap.clear();

		// 尝试从数据库加载股票列表
		if (!LoadStockListFromDB(m_strDBPath))
		{
			TRACE("从数据库加载股票列表失败\n");
			return false;
		}
		
		// 如果列表为空，则初始化失败
		if (m_vecStocks.empty())
		{
			TRACE("股票列表为空\n");
			return false;
		}
		
		// 默认选中第一支股票
		m_nCurrentStockIndex = 0;
		m_strCurrentStock = m_vecStocks[0]._Code;
		
		// 创建并初始化网络数据对象
		// 智能指针会自动管理之前的m_pNetData
		m_pNetData = std::make_unique<CNetData>();
		
		// 初始化网络数据对象
		if (!m_pNetData->Initialize(this))
		{
			TRACE("实时数据下载初始化失败\n");
			m_pNetData.reset(); // 释放资源，智能指针会自动管理内存
			return false;
		}
		
		
		// 启动定时下载 - 自动开始循环数据更新
		m_pNetData->StartTimedDownload();
		
		TRACE("初始化完成，网络数据自动更新已启动\n");
		
		return true;
	}
	catch (const std::exception& e)
	{
		TRACE("股票列表初始化异常: %s\n", e.what());
		return false;
	}
}


// 从SQLite数据库加载股票列表
BOOL CStockDoc::LoadStockListFromDB(const std::string& dbPath)
{
	sqlite3* db = nullptr;
	sqlite3_stmt* stmt = nullptr;
	BOOL bResult = FALSE;
	
	// 创建加载进度对话框
	std::unique_ptr<CLoadingDlg> pLoadingDlg = std::make_unique<CLoadingDlg>();
	if (pLoadingDlg->Create(AfxGetMainWnd()))
	{
		pLoadingDlg->SetStatusText(_T("正在加载股票列表..."));
		pLoadingDlg->SetProgress(0);
		pLoadingDlg->ShowWindow(SW_SHOW);
		pLoadingDlg->UpdateWindow();
	}
	
	try
	{
		// 尝试打开数据库
		if (sqlite3_open(dbPath.c_str(), &db) != SQLITE_OK)
		{
			std::string errMsg = "无法打开数据库: ";
			errMsg += sqlite3_errmsg(db);
			throw std::runtime_error(errMsg);
		}
		
		// 先获取总股票数，用于计算进度
		const char* countSql = "SELECT COUNT(*) FROM x00";
		int totalStocks = 0;
		
		if (sqlite3_prepare_v2(db, countSql, -1, &stmt, nullptr) == SQLITE_OK)
		{
			if (sqlite3_step(stmt) == SQLITE_ROW)
			{
				totalStocks = sqlite3_column_int(stmt, 0);
			}
			sqlite3_finalize(stmt);
			stmt = nullptr;
		}
		
		// 准备SQL语句
		const char* sql = "SELECT id, code, name, plate, industry, theme, style, circulating_value, actual_circulating FROM x00";
		
		if (sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr) != SQLITE_OK)
		{
			std::string errMsg = "准备SQL语句失败: ";
			errMsg += sqlite3_errmsg(db);
			throw std::runtime_error(errMsg);
		}
		
		// 清空现有列表
		m_vecStocks.clear();
		m_vecSymbols.clear();
		
		// 循环读取记录
		int nCount = 0;
		while (sqlite3_step(stmt) == SQLITE_ROW)
		{
			// 获取股票基本信息
			int id = sqlite3_column_int(stmt, 0);
			const unsigned char* code = sqlite3_column_text(stmt, 1);
			const unsigned char* name = sqlite3_column_text(stmt, 2);
			const unsigned char* plate = sqlite3_column_text(stmt, 3);
			const unsigned char* industry = sqlite3_column_text(stmt, 4);
			const unsigned char* theme = sqlite3_column_text(stmt, 5);
			const unsigned char* style = sqlite3_column_text(stmt, 6);
			double circulating_value = sqlite3_column_double(stmt, 7);
			double actual_circulating = sqlite3_column_double(stmt, 8);
			
			if (code)
			{
				std::string stdCode = reinterpret_cast<const char*>(code);
				std::string stdName = name ? reinterpret_cast<const char*>(name) : stdCode;
				std::string stdPlate = plate ? reinterpret_cast<const char*>(plate) : "";
				std::string stdIndustry = industry ? reinterpret_cast<const char*>(industry) : "";
				std::string stdTheme = theme ? reinterpret_cast<const char*>(theme) : "";
				std::string stdStyle = style ? reinterpret_cast<const char*>(style) : "";
				
				// 添加到列表
				m_vecSymbols.push_back(stdCode);
				
				// 创建StockData对象并设置属性
				StockData symbol;
				symbol._ID						= id;			// 设置数据库ID
				symbol._Code					= stdCode;
				symbol._Name					= stdName;
				symbol._Plate					= stdPlate;
				symbol._Industry				= stdIndustry;
				symbol._Theme					= stdTheme;
				symbol._Style					= stdStyle;
				symbol._CirculatingValue		= circulating_value;
				symbol._ActualCirculatingValue	= actual_circulating;
				
				// 根据股票代码设置市场类型
				symbol._MarketType = DetermineMarketInfo(stdCode);
				
				if		(symbol._MarketType == MARKET_SH_A)	m_CountMarket_SH_A++;
				else if (symbol._MarketType == MARKET_SZ_A)	m_CountMarket_SZ_A++;
				else if (symbol._MarketType == MARKET_GEM)	m_CountMarket_GEM++;
				else if (symbol._MarketType == MARKET_STAR)	m_CountMarket_STAR++;
				else if (symbol._MarketType == MARKET_BJ_A)	m_CountMarket_BJ_A++;


				m_vecStocks.push_back(symbol);

				// 构建股票代码和名称的映射表
				// 
				// 添加代码到索引的映射
				m_codeToIndexMap[symbol._Code] = static_cast<int>(nCount);

				// 添加名称到索引的映射
				m_nameToIndexMap[symbol._Name] = static_cast<int>(nCount);

				// 添加名称到代码的映射
				m_nameToCodeMap[symbol._Name] = symbol._Code;

				bResult = TRUE; // 表示至少加载了一条记录
				nCount++;
				
				// 更新进度
				if (totalStocks > 0 && nCount % 100 == 0)
				{
					int progress = 20 + (nCount * 80 / totalStocks);
					if (pLoadingDlg)
					{
						pLoadingDlg->SetProgress(progress);
						CString strStatus;
						strStatus.Format(_T("正在加载股票列表... %d/%d"), nCount, totalStocks);
						pLoadingDlg->SetStatusText(strStatus);
					}
					
					// 处理消息循环，使UI响应
					MSG msg;
					while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE))
					{
						TranslateMessage(&msg);
						DispatchMessage(&msg);
					}
				}
			}
		}
		
		// 清理
		sqlite3_finalize(stmt);
		sqlite3_close(db);
		
		if (pLoadingDlg)
		{
			pLoadingDlg->SetStatusText(_T("股票列表加载完成!"));
			pLoadingDlg->SetProgress(100);
		}
	}
	catch (const std::exception& e)
	{
		// 确保资源被释放
		if (stmt) sqlite3_finalize(stmt);
		if (db) sqlite3_close(db);
		
		CString errorMsg;
		errorMsg.Format(_T("加载股票列表时发生异常: %s"), CString(e.what()));
		return FALSE;
	}
	
	return bResult;
}



// 设置当前股票
bool CStockDoc::SetCurrentStock(const std::string& stockCode)
{
	// 参数验证
	if (stockCode.empty())
	{
		TRACE("设置当前股票失败：股票代码为空\n");
		return false;
	}
	
	{
		std::lock_guard<std::mutex> stockLock(m_stockMutex);
		if (m_vecSymbols.empty())
		{
			TRACE("设置当前股票失败：股票列表为空\n");
			return false;
		}
	}
	
	{
		std::lock_guard<std::mutex> currentLock(m_currentStockMutex);
		// 如果当前已经是这支股票，无需重复设置
		if (m_strCurrentStock == stockCode)
		{
			TRACE("当前已选中股票 %s，无需重新设置\n", stockCode.c_str());
			return true;
		}
	}
	
	// 使用映射表快速查找股票索引，而不是线性搜索
	int newIndex = GetStockIndex(stockCode);
	
	if (newIndex >= 0)
	{
		{
			std::lock_guard<std::mutex> currentLock(m_currentStockMutex);
			m_nCurrentStockIndex = newIndex;
			m_strCurrentStock = stockCode;
		}

		// 异步下载该股票的分时数据和其他必要数据，避免阻塞UI线程
		std::thread dataThread([this, stockCode]() 
			{
			try {
				// 下载该股票的分时数据
				if (!DownloadStockTimelineData(stockCode))
				{
					TRACE("警告：下载股票 %s 的分时数据失败，但仍将其设为当前股票\n", stockCode.c_str());
				}
			}
			catch (const std::exception& e) {
				TRACE("异步下载股票数据异常: %s\n", e.what());
			}
		});
		dataThread.detach(); // 分离线程
		
		// 通知视图更新
		UpdateAllViews(NULL);
		TRACE("成功设置当前股票：%s\n", stockCode.c_str());
		return true;
	}
	else
	{
		TRACE("设置当前股票失败：未找到股票代码 %s\n", stockCode.c_str());
		return false;
	}
}


// 下载单只股票的分时数据
bool CStockDoc::DownloadStockTimelineData(const std::string& stockCode)
{
	if (stockCode.empty())
	{
		TRACE("股票代码为空\n");
		return false;
	}

	try
	{
		// 获取当前日期
		SYSTEMTIME currentTime;
		GetLocalTime(&currentTime);
		
		// 检查当前是否为交易时段
		bool isTradeDay = true;
		bool shouldUseLastTradeDay = false;
		
		// 检查是否为周末（周六日不交易）
		if (currentTime.wDayOfWeek == 0 || currentTime.wDayOfWeek == 6)
		{
			isTradeDay = false;
			shouldUseLastTradeDay = true;
			TRACE("当前是周末，尝试加载上一个交易日数据\n");
		}
		// 检查是否为交易日的交易时段（9:30-15:00）
		else if (currentTime.wHour < 9 /*|| 
				(currentTime.wHour == 9 && currentTime.wMinute < 30) || 
				currentTime.wHour >= 15*/)
		{
			shouldUseLastTradeDay = true;
			TRACE("当前非交易时段，尝试加载上一个交易日数据\n");
		}
		
		// 构建日期字符串函数
		auto formatDateString = [](const SYSTEMTIME& time) -> CString {
			CString dateStr;
			dateStr.Format(_T("%04d%02d%02d"), time.wYear, time.wMonth, time.wDay);
			return dateStr;
		};
		
		// 获取当前日期字符串
		CString currentDateStr = formatDateString(currentTime);
		
		// 如果需要使用上一个交易日
		if (shouldUseLastTradeDay)
		{
			// 计算上一个可能的交易日（从当前日期向前查找）
			SYSTEMTIME lastTradeDay = currentTime;
			int daysBack = 1;
			
			// 如果当前是周一，需要回退到上周五
			if (currentTime.wDayOfWeek == 1) // 周一
				daysBack = 3;
			else if (currentTime.wDayOfWeek == 0) // 周日
				daysBack = 2;
			
			// 计算上一个交易日的日期
			FILETIME ft;
			SystemTimeToFileTime(&currentTime, &ft);
			ULARGE_INTEGER uli;
			uli.LowPart = ft.dwLowDateTime;
			uli.HighPart = ft.dwHighDateTime;
			
			// 一天的100纳秒数: 24 * 60 * 60 * 10000000
			const ULONGLONG dayInNanoseconds = 24ULL * 60 * 60 * 10000000;
			uli.QuadPart -= dayInNanoseconds * daysBack;
			
			ft.dwLowDateTime = uli.LowPart;
			ft.dwHighDateTime = uli.HighPart;
			FileTimeToSystemTime(&ft, &lastTradeDay);
			
			// 构建上一个交易日的日期字符串
			CString lastTradeDayStr = formatDateString(lastTradeDay);
			TRACE("上一个可能的交易日: %s\n", (LPCTSTR)lastTradeDayStr);
			
			// 尝试检查上一交易日的数据路径
			CString lastTradeDayPath;
			lastTradeDayPath.Format(_T("F:\\Market\\MIN\\%s\\%s.json"), (LPCTSTR)lastTradeDayStr, stockCode.c_str());
			
			if (PathFileExists(lastTradeDayPath))
			{
				TRACE("找到上一交易日数据文件: %s\n", (LPCTSTR)lastTradeDayPath);
				return LoadTimelineDataFromFile(stockCode, lastTradeDayPath);
			}
			
			// 如果仍然没有找到，可以再尝试更早的日期（考虑节假日）
			// 这里可以继续实现更复杂的节假日判断逻辑
			// 简单起见，我们再尝试多回退一天
			if (isTradeDay == false) // 如果是周末或节假日，再多尝试一天
			{
				uli.LowPart = ft.dwLowDateTime;
				uli.HighPart = ft.dwHighDateTime;
				uli.QuadPart -= dayInNanoseconds; // 再回退一天
				
				ft.dwLowDateTime = uli.LowPart;
				ft.dwHighDateTime = uli.HighPart;
				FileTimeToSystemTime(&ft, &lastTradeDay);
				
				lastTradeDayStr = formatDateString(lastTradeDay);
				TRACE("尝试更早的可能交易日: %s\n", (LPCTSTR)lastTradeDayStr);
				
				lastTradeDayPath.Format(_T("F:\\Market\\MIN\\%s\\%s.json"), (LPCTSTR)lastTradeDayStr, stockCode.c_str());
				
				if (PathFileExists(lastTradeDayPath))
				{
					TRACE("找到更早的交易日数据文件: %s\n", (LPCTSTR)lastTradeDayPath);
					return LoadTimelineDataFromFile(stockCode, lastTradeDayPath);
				}
			}
		}
		
		// 检查当前日期的数据文件
		CString localDataPath;
		localDataPath.Format(_T("F:\\Market\\MIN\\%s\\%s.json"), (LPCTSTR)currentDateStr, stockCode.c_str());

		// 检查文件是否存在
		if (PathFileExists(localDataPath))
		{
			TRACE("本地分时数据已存在：%s\n", (LPCTSTR)localDataPath);

			// 从本地文件加载分时数据
			return LoadTimelineDataFromFile(stockCode, localDataPath);
		}
		else
		{
			// 如果当前日期的文件不存在，且在交易时段内，尝试从网络下载
			if (isTradeDay && !shouldUseLastTradeDay)
			{
				TRACE("本地分时数据不存在，从网络下载：%s\n", stockCode.c_str());

				// 确保网络数据对象有效
				if (!m_pNetData)
				{
					TRACE("网络数据对象为空\n");
					return false;
				}

				// 调用NetData类的对应方法下载分时数据
				bool result = m_pNetData->DownloadTimelineData(stockCode);

				if (!result)
				{
					TRACE("下载股票 %s 的分时数据失败\n", stockCode.c_str());
				}

				return result;
			}
			else
			{
				// 非交易时段或非交易日，且没有找到历史数据
				TRACE("非交易时段，且未找到历史分时数据：%s\n", stockCode.c_str());
				return false;
			}
		}
	}
	catch (const std::exception& e)
	{
		TRACE("下载或加载股票分时数据异常: %s\n", e.what());
		return false;
	}
}

// 从本地文件加载分时数据
bool CStockDoc::LoadTimelineDataFromFile(const std::string& stockCode, const CString& filePath)
{
	try
	{
		TRACE("从本地文件加载分时数据: %s\n", (LPCTSTR)filePath);

		// 打开文件
		CFile file;
		if (!file.Open(filePath, CFile::modeRead))
		{
			TRACE("无法打开分时数据文件: %s\n", (LPCTSTR)filePath);
			return false;
		}

		// 读取文件内容
		DWORD fileSize = (DWORD)file.GetLength();
		if (fileSize == 0)
		{
			TRACE("分时数据文件为空: %s\n", (LPCTSTR)filePath);
			file.Close();
			return false;
		}

		// 分配内存
		std::vector<char> buffer(fileSize + 1);
		file.Read(buffer.data(), fileSize);
		buffer[fileSize] = '\0';  // 确保字符串结束
		file.Close();

		// 将文件内容作为JSON字符串解析
		std::string jsonData(buffer.data());

		// 使用GetStockIndex方法快速查找股票索引
		int stockIndex = GetStockIndex(stockCode);

		if (stockIndex >= 0 && stockIndex < static_cast<int>(m_vecStocks.size()))
		{
			StockData& stock = m_vecStocks[stockIndex];

			// 清空原有分时数据
			stock._vecTimeLine.clear();

			// 解析JSON数据并填充到分时数据中
			// 这里假设JSON格式与网络下载的数据格式一致，实际可能需要调整
			// 如果格式不同，需要单独实现解析逻辑
			if (m_pNetData)
			{
				bool result = m_pNetData->ParseTimelineData(stockCode, jsonData);
				if (!result)
				{
					TRACE("解析本地分时数据失败: %s\n", (LPCTSTR)filePath);
					return false;
				}
			}
			else
			{
				TRACE("网络数据对象为空，无法解析本地分时数据\n");
				return false;
			}

			// 通知视图更新
			SafeUpdateAllViews();
			return true;
		}
		else
		{
			TRACE("未找到股票: %s\n", stockCode.c_str());
			return false;
		}
	}
	catch (const std::exception& e)
	{
		TRACE("从本地文件加载分时数据异常: %s\n", e.what());
		return false;
	}
}


// 切换到上一支股票
void CStockDoc::ShowPreviousStock()
{
	// 获取CSymbolGrid当前显示的股票列表
	CMainFrame* pMainFrame = (CMainFrame*)AfxGetMainWnd();
	if (!pMainFrame)
		return;
		
	CSymbolGrid* pGrid = pMainFrame->GetSymbolGrid();
	if (!pGrid)
	{
		TRACE("无法获取CSymbolGrid指针\n");
		return;
	}
	
	// 获取当前选中的行
	long currentRow = pGrid->GetCurrentRow();
	long totalRows = pGrid->GetNumberRows();
	
	if (totalRows <= 0)
	{
		TRACE("无法切换到上一支股票：股票列表为空\n");
		return;
	}
	
	// 计算上一行索引（向上循环）
	long prevRow = (currentRow <= 0) ? (totalRows - 1) : (currentRow - 1);
	
	// 获取上一行的股票代码
	CUGCell cell;
	pGrid->GetCell(0, prevRow, &cell);
	int stockIndex = cell.GetParam();
	
	// 获取股票数据并设置为当前股票
	const StockData* pStockData = GetStock(stockIndex);
	if (!pStockData)
	{
		TRACE("获取股票数据失败，索引: %d\n", stockIndex);
		return;
	}
	
	std::string newStock = pStockData->_Code;
	
	// 设置当前选中行
	pGrid->GotoRow(prevRow);
	
	// 更新当前股票索引和代码
	{
		std::lock_guard<std::mutex> currentLock(m_currentStockMutex);
		m_nCurrentStockIndex = stockIndex;
		m_strCurrentStock = newStock;
	}
	
	// 尝试下载分时数据，失败也不影响切换
	DownloadStockTimelineData(newStock);
	
	// 通知视图更新
	UpdateAllViews(NULL);
	TRACE("已切换到上一支股票：%s\n", newStock.c_str());
}

// 切换到下一支股票
void CStockDoc::ShowNextStock()
{
	// 获取CSymbolGrid当前显示的股票列表
	CMainFrame* pMainFrame = (CMainFrame*)AfxGetMainWnd();
	if (!pMainFrame)
		return;
		
	CSymbolGrid* pGrid = pMainFrame->GetSymbolGrid();
	if (!pGrid)
	{
		TRACE("无法获取CSymbolGrid指针\n");
		return;
	}
	
	// 获取当前选中的行
	long currentRow = pGrid->GetCurrentRow();
	long totalRows = pGrid->GetNumberRows();
	
	if (totalRows <= 0)
	{
		TRACE("无法切换到下一支股票：股票列表为空\n");
		return;
	}
	
	// 计算下一行索引（向下循环）
	long nextRow = (currentRow >= totalRows - 1) ? 0 : (currentRow + 1);
	
	// 获取下一行的股票代码
	CUGCell cell;
	pGrid->GetCell(0, nextRow, &cell);
	int stockIndex = cell.GetParam();
	
	// 获取股票数据并设置为当前股票
	const StockData* pStockData = GetStock(stockIndex);
	if (!pStockData)
	{
		TRACE("获取股票数据失败，索引: %d\n", stockIndex);
		return;
	}
	
	std::string newStock = pStockData->_Code;
	
	// 设置当前选中行
	pGrid->GotoRow(nextRow);
	
	// 更新当前股票索引和代码
	{
		std::lock_guard<std::mutex> currentLock(m_currentStockMutex);
		m_nCurrentStockIndex = stockIndex;
		m_strCurrentStock = newStock;
	}
	
	// 尝试下载分时数据，失败也不影响切换
	DownloadStockTimelineData(newStock);
	
	// 通知视图更新
	UpdateAllViews(NULL);
	TRACE("已切换到下一支股票：%s\n", newStock.c_str());
}

// 获取股票名称
std::string CStockDoc::GetStockName(const std::string& stockCode) const
{
	if (stockCode.empty())
	{
		return "";
	}
	
	// 使用互斥锁保护股票列表访问
	std::lock_guard<std::mutex> lock(m_stockMutex);
	
	// 在股票列表中查找该股票
	for (const auto& stock : m_vecStocks)
	{
		if (stock._Code == stockCode)
		{
			return stock._Name;
		}
	}
	
	return stockCode; // 如果未找到，返回股票代码作为名称
}

// 获取股票所属行业
std::string CStockDoc::GetStockIndustry(const std::string& stockCode) const
{
	if (stockCode.empty())
	{
		return "";
	}
	
	// 使用互斥锁保护股票列表访问
	std::lock_guard<std::mutex> lock(m_stockMutex);
	
	// 在股票列表中查找该股票
	for (const auto& stock : m_vecStocks)
	{
		if (stock._Code == stockCode)
		{
			return stock._Industry;
		}
	}
	
	return ""; // 如果未找到，返回空字符串
}

// 获取股票题材
std::string CStockDoc::GetStockTheme(const std::string& stockCode) const
{
	if (stockCode.empty())
	{
		return "";
	}
	
	// 使用互斥锁保护股票列表访问
	std::lock_guard<std::mutex> lock(m_stockMutex);
	
	// 在股票列表中查找该股票
	for (const auto& stock : m_vecStocks)
	{
		if (stock._Code == stockCode)
		{
			return stock._Theme;
		}
	}
	
	return ""; // 如果未找到，返回空字符串
}

// 获取股票风格
std::string CStockDoc::GetStockStyle(const std::string& stockCode) const
{
	// 在股票列表中查找该股票
	for (const auto& stock : m_vecStocks)
	{
		if (stock._Code == stockCode)
		{
			return stock._Style;
		}
	}
	
	return ""; // 如果未找到，返回空字符串
}

// 获取股票流通值
double CStockDoc::GetStockCirculatingValue(const std::string& stockCode) const
{
	// 在股票列表中查找该股票
	for (const auto& stock : m_vecStocks)
	{
		if (stock._Code == stockCode)
		{
			return stock._CirculatingValue;
		}
	}
	
	return 0.0; // 如果未找到，返回0
}

// 获取股票实际流通值
double CStockDoc::GetStockActualCirculatingValue(const std::string& stockCode) const
{
	// 在股票列表中查找该股票
	for (const auto& stock : m_vecStocks)
	{
		if (stock._Code == stockCode)
		{
			return stock._ActualCirculatingValue;
		}
	}
	
	return 0.0; // 如果未找到，返回0
}

// 获取股票所属市场类型
MarketType CStockDoc::GetStockMarketType(const std::string& stockCode) const
{
	// 在股票列表中查找该股票
	for (const auto& stock : m_vecStocks)
	{
		if (stock._Code == stockCode)
		{
			return stock._MarketType;
		}
	}
	
	// 如果未找到，使用新函数判断
	return DetermineMarketInfo(stockCode);
}

// 获取股票所属市场标识
std::string CStockDoc::GetStockMarket(const std::string& stockCode) const
{
	// 直接通过公共方法判断
	return GetMarketPrefix(stockCode);
}

// 获取股票完整代码（带市场前缀）
std::string CStockDoc::GetFullStockCode(const std::string& stockCode) const
{
	std::string market = GetStockMarket(stockCode);
	if (!market.empty())
	{
		return market + stockCode;
	}
	return stockCode;
}

// 启动实时数据下载
void CStockDoc::StartRealtimeDataDownload()
{
	
}

// 停止实时数据下载
void CStockDoc::StopRealtimeDataDownload()
{
	if (m_pNetData)
	{
		// 停止所有下载任务
		m_pNetData->Shutdown();
		TRACE("实时数据下载已停止\n");
	}
}

// 文档关闭时的清理工作
void CStockDoc::OnCloseDocument()
{
	// 停止实时数据下载
	StopRealtimeDataDownload();
	
	// 调用基类实现
	CDocument::OnCloseDocument();
}


// 根据股票代码获取股票在列表中的索引
int CStockDoc::GetStockIndex(const std::string& stockCode) const
{
	if (stockCode.empty())
		return -1;
		
	std::lock_guard<std::mutex> lock(m_stockMutex);
	
	auto it = m_codeToIndexMap.find(stockCode);
	if (it != m_codeToIndexMap.end())
		return it->second;
		
	// 如果映射表中没有找到，则使用线性搜索作为备选方案
	auto vecIt = std::find(m_vecSymbols.begin(), m_vecSymbols.end(), stockCode);
	if (vecIt != m_vecSymbols.end())
		return static_cast<int>(std::distance(m_vecSymbols.begin(), vecIt));
		
	return -1;  // 未找到
}

// 根据股票名称获取股票在列表中的索引
int CStockDoc::GetStockIndexByName(const std::string& stockName) const
{
	if (stockName.empty())
		return -1;
		
	std::lock_guard<std::mutex> lock(m_stockMutex);
	
	auto it = m_nameToIndexMap.find(stockName);
	if (it != m_nameToIndexMap.end())
		return it->second;
		
	// 如果映射表中没有找到，则使用线性搜索作为备选方案
	for (size_t i = 0; i < m_vecStocks.size(); ++i)
	{
		if (m_vecStocks[i]._Name == stockName)
			return static_cast<int>(i);
	}
		
	return -1;  // 未找到
}

// 根据股票名称获取股票代码
std::string CStockDoc::GetStockCodeByName(const std::string& stockName) const
{
	if (stockName.empty())
		return "";
		
	std::lock_guard<std::mutex> lock(m_stockMutex);
	
	auto it = m_nameToCodeMap.find(stockName);
	if (it != m_nameToCodeMap.end())
		return it->second;
		
	// 如果映射表中没有找到，则使用线性搜索作为备选方案
	for (const auto& stock : m_vecStocks)
	{
		if (stock._Name == stockName)
			return stock._Code;
	}
		
	return "";  // 未找到
}



// 实现线程安全的视图更新方法
void CStockDoc::SafeUpdateAllViews()
{
	// 使用原子操作确保线程安全
	bool expected = false;
	if (!m_pendingUIUpdate.compare_exchange_strong(expected, true)) {
		return; // 已经有待处理的更新
	}

	// 获取应用程序和主窗口指针
	CWinApp* pApp = AfxGetApp();
	CMainFrame* pMainFrame = dynamic_cast<CMainFrame*>(AfxGetMainWnd());
	
	// 检查主窗口是否有效以及视图是否已完成初始化
	if (pApp && pMainFrame && ::IsWindow(pMainFrame->GetSafeHwnd()) && pMainFrame->IsViewInitialized())
	{
		// 使用PostMessage方式，将更新操作推迟到主线程的下一个空闲时段
		pMainFrame->PostMessage(WM_COMMAND, ID_APP_REFRESH, 0);
	}
	else
	{
		TRACE("主窗口无效或视图未完成初始化，跳过视图更新\n");
		
		// 重置更新标志
		std::lock_guard<std::mutex> lock(m_updateMutex);
		m_pendingUIUpdate = false;
		return;
	}
	
	// 添加一个短延迟后重置标志，避免过于频繁的更新请求
	std::thread([this]() {
		std::this_thread::sleep_for(std::chrono::milliseconds(500));
		std::lock_guard<std::mutex> lock(m_updateMutex);
		m_pendingUIUpdate = false;
	}).detach();
}



// 通知TimeInfo视图更新实时数据
void CStockDoc::NotifyTimeInfoUpdate(const CString& stockCode, const CTime& time, double price, 
                                   double avgPrice, double open, double preClose, 
                                   double volume, double amount)
{
	// 锁定更新标记
	std::lock_guard<std::mutex> lock(m_updateMutex);
	
	// 遍历所有视图
	POSITION pos = GetFirstViewPosition();
	while (pos != NULL)
	{
		CView* pView = GetNextView(pos);
		
		// 检查视图是否是CTimeInfo类型
		if (pView && pView->IsKindOf(RUNTIME_CLASS(CTimeInfo)))
		{
			CTimeInfo* pTimeInfo = (CTimeInfo*)pView;
			// 更新分时信息数据
			pTimeInfo->SetStockInfo(stockCode, pTimeInfo->GetDocument()->GetStockName(std::string(stockCode)).c_str());
			// 实际上TimeInfo视图会通过OnTimer自动更新数据，这里只需要通知视图代码已变更
			pTimeInfo->Invalidate(FALSE);
		}
		
		// 检查视图是否是CKLineInfo类型
		if (pView && pView->IsKindOf(RUNTIME_CLASS(CKLineInfo)))
		{
			CKLineInfo* pKLineInfo = (CKLineInfo*)pView;
			// 更新K线信息数据
			pKLineInfo->SetStockInfo(stockCode, pKLineInfo->GetDocument()->GetStockName(std::string(stockCode)).c_str());
			// 实际上KLineInfo视图会通过OnTimer自动更新数据，这里只需要通知视图代码已变更
			pKLineInfo->Invalidate(FALSE);
		}
	}
}

void CStockDoc::UpdateMarketInidexData(const MarketIndexData & data, const std::string & code)
{
	std::lock_guard<std::mutex> lock(m_updateMutex);

	// 更新市场指数数据
	if (code == "000001.SH")
	{
		// 上证指数
		m_MarketData._vecIndexSH.push_back(data);
	}
	else if (code == "399001.SZ")
	{
		// 深证成指
		m_MarketData._vecIndexSZ.push_back(data);
	}
	else if (code == "399006.SZ")
	{
		// 创业板指数
		m_MarketData._vecIndexGEM.push_back(data);
	}
	else if (code == "000688.SH")
	{
		// 科创板指数
		m_MarketData._vecIndexSTAR.push_back(data);
	}
	else if (code == "000002.BJ")
	{
		// 北证指数
		m_MarketData._vecIndexBJ.push_back(data);
	}
	else if (code == "000002.SH")
	{
		// 上证A股指数
		m_MarketData._vecIndexSHA.push_back(data);
	}
	else if (code == "399002.SZ")
	{
		// 深证A股指数
		m_MarketData._vecIndexSZA.push_back(data);
	}
}

