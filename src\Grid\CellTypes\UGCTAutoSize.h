/*************************************************************************
				Class Declaration : CUGCTAutoSize
**************************************************************************
	Source file : UGCTAutoSize.cpp
	Header file : UGCTAutoSize.h
// This software along with its related components, documentation and files ("The Libraries")
// is ?1994-2007 The Code Project (1612916 Ontario Limited) and use of The Libraries is
// governed by a software license agreement ("Agreement").  Copies of the Agreement are
// available at The Code Project (www.codeproject.com), as part of the package you downloaded
// to obtain this file, or directly from our office.  For a copy of the license governing
// this software, you may contact <NAME_EMAIL>, or by calling ************.

	Purpose:
		The CUGCTAutoSize celltype extends the standard celltype providing
		funcitonality to size its cell to make sure that cell's data is
		visible in full.  It will not make columns or rows smaller,
		but it will enlarge them whenever needed.
	
	Details:
		This celltype does not send any notifications to the CUGCtrl derived
		classes.
*************************************************************************/
#ifndef _UGCTAutoSize_H_
#define _UGCTAutoSize_H_

class UG_CLASS_DECL CUGCTAutoSize: public CUGCellType
{
protected:

public:
	CUGCTAutoSize();
	~CUGCTAutoSize();

	virtual void OnDraw(CDC *dc,RECT *rect,int col,long row,CUGCell *cell,int selected,int current);
	virtual void OnChangedCellWidth(int col, long row, int* width);
	virtual void OnChangedCellHeight(int col, long row, int* height);
};

#endif // _UGCTAutoSize_H_