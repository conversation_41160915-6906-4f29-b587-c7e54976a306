﻿// KLineIndicator.h: CKLineIndicator 类的接口
//

#pragma once

class CStockDoc;

// CKLineIndicator 视图 - 用于显示K线技术指标
class CKLineIndicator : public CView
{
protected: // 仅从序列化创建
	CKLineIndicator() noexcept;
	DECLARE_DYNCREATE(CKLineIndicator)

// 特性
public:
	CStockDoc* GetDocument() const;

// 操作
public:
	// 设置股票代码
	void SetStockCode(const CString& strCode);

// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnInitialUpdate();
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint);

// 实现
public:
	virtual ~CKLineIndicator();

protected:
	// 股票基本信息
	CString m_strCode;  // 股票代码
	
	// 绘制函数
	void DrawMACD(CDC* pDC, const CRect& rcArea);
	void DrawRSI(CDC* pDC, const CRect& rcArea);
	void DrawKDJ(CDC* pDC, const CRect& rcArea);
	void DrawBOLL(CDC* pDC, const CRect& rcArea);
	void DrawBBI(CDC* pDC, const CRect& rcArea);
	
	// 绘制指标选择区域
	void DrawIndicatorSelector(CDC* pDC);

// 生成的消息映射函数
protected:
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
	DECLARE_MESSAGE_MAP()
	
private:
	// 当前选中的指标
	int m_nCurrentIndicator;  // 0=MACD, 1=RSI, 2=KDJ, 3=BOLL, 4=BBI
	
	// K线数据
	std::vector<KLINE_DATA> m_vecKLine;  // 本地K线数据
	
	// K线数据加载方法
	bool LoadKLineDataFromLocalFile(const CString& code, std::vector<KLINE_DATA>& klineData);
};
