﻿// StockDoc.h: CStockDoc 类的接口
//


#pragma once

#include <vector>
#include <string>
#include <memory>  // 为智能指针添加头文件
#include <mutex>   // 为互斥锁添加头文件
#include "StockDef.h"
#include "NetData.h"  // 添加NetData.h以引入TaskType定义
#include <unordered_map>  // 添加unordered_map头文件
#include <atomic>  // 添加atomic头文件

// 前向声明
class CNetData;

class CStockDoc : public CDocument
{
protected: // 仅从序列化创建
	CStockDoc() noexcept;
	DECLARE_DYNCREATE(CStockDoc)

// 特性
public:
	// 获取当前股票代码
	std::string GetCurrentStock() const { 
		std::lock_guard<std::mutex> lock(m_currentStockMutex);
		return m_strCurrentStock; 
	}
	
	// 获取股票代码列表
	const std::vector<std::string>& GetStockList() const { 
		std::lock_guard<std::mutex> lock(m_stockMutex);
		return m_vecSymbols; 
	}
	
	// 根据股票代码获取股票在列表中的索引
	int GetStockIndex(const std::string& stockCode) const;
	
	// 根据股票名称获取股票在列表中的索引
	int GetStockIndexByName(const std::string& stockName) const;
	
	// 根据股票名称获取股票代码
	std::string GetStockCodeByName(const std::string& stockName) const;
	
	
	// 获取股票所属行业
	std::string GetStockIndustry(const std::string& stockCode) const;
	
	// 获取股票题材
	std::string GetStockTheme(const std::string& stockCode) const;
	
	// 获取股票风格
	std::string GetStockStyle(const std::string& stockCode) const;
	
	// 获取股票流通值
	double GetStockCirculatingValue(const std::string& stockCode) const;
	
	// 获取股票实际流通值
	double GetStockActualCirculatingValue(const std::string& stockCode) const;
	
	// 获取股票所属市场类型
	MarketType GetStockMarketType(const std::string& stockCode) const;
	
	// 获取股票所属市场标识
	std::string GetStockMarket(const std::string& stockCode) const;
	
	// 获取股票完整代码（带市场前缀）
	std::string GetFullStockCode(const std::string& stockCode) const;

	// 获取网络数据模块指针
	CNetData* GetNetData() { return m_pNetData.get(); }

	// 更新市场指数数据
	void UpdateMarketInidexData(const MarketIndexData& data, const std::string& code);

  

// 操作
public:
	// 股票列表操作方法
	bool InitStockList();                              // 初始化股票列表
	BOOL LoadStockListFromDB(const std::string& dbPath);   // 从数据库加载
	bool SetCurrentStock(const std::string& stockCode);    // 设置当前股票
	void ShowPreviousStock();                          // 显示上一支股票
	void ShowNextStock();                              // 显示下一支股票

	// 获取股票名称
	std::string GetStockName(const std::string& stockCode) const;
	
	// 启动实时数据下载
	void StartRealtimeDataDownload();
	
	// 停止实时数据下载
	void StopRealtimeDataDownload();

	// 下载分时数据
	bool DownloadCurrentStockTimelineData();
	
	
	// 更新单只股票的数据（包括实时和分时数据）
	//bool UpdateStockData(const std::string& stockCode);
	
	// 下载单只股票的分时数据
	bool DownloadStockTimelineData(const std::string& stockCode);
	
	// 从本地文件加载分时数据
	bool LoadTimelineDataFromFile(const std::string& stockCode, const CString& filePath);
	
	// 线程安全的视图更新方法 - 从工作线程中调用此方法代替直接调用UpdateAllViews
	void SafeUpdateAllViews();

// 重写
public:
	virtual BOOL OnNewDocument();
	virtual void Serialize(CArchive& ar);
	virtual void OnCloseDocument();


// 实现
public:
	virtual ~CStockDoc();
#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

public:
	// 股票列表和当前股票索引
	MarketStatData					m_MarketData;
	std::vector<StockData>			m_vecStocks;         // 股票信息列表
	std::vector<std::string>		m_vecSymbols;        // 股票代码列表
	
	int								m_CountMarket_SH_A;			// 上证A股数量
	int								m_CountMarket_SZ_A;			// 深证A股数量
	int								m_CountMarket_GEM;			// 创业板数量
	int								m_CountMarket_STAR;			// 科创板数量
	int								m_CountMarket_BJ_A;			// 北证A股数量
	
private:
	int m_nCurrentStockIndex;                        // 当前股票索引
	std::string m_strCurrentStock;                   // 当前股票代码
	std::string m_strDBPath;                         // 数据库路径
	
	std::unique_ptr<CNetData> m_pNetData;            // 网络数据下载对象，使用智能指针管理
	
	// 互斥锁，用于保护共享资源
	mutable std::mutex m_stockMutex;                 // 保护股票数据访问的互斥锁
	mutable std::mutex m_currentStockMutex;          // 保护当前股票信息的互斥锁
	
	// 索引映射表，用于快速查找股票
	std::unordered_map<std::string, int> m_codeToIndexMap;     // 股票代码到索引的映射
	std::unordered_map<std::string, int> m_nameToIndexMap;     // 股票名称到索引的映射
	std::unordered_map<std::string, std::string> m_nameToCodeMap;  // 股票名称到代码的映射
	
	// 用于保护UI更新的标记和锁
	std::atomic<bool> m_pendingUIUpdate;
	std::mutex m_updateMutex;
	
	// 私有辅助方法
	//bool CreateAndAddTask(TaskType taskType, const std::string& code = "");  // 创建并添加下载任务的通用方法


// 生成的消息映射函数
protected:
	DECLARE_MESSAGE_MAP()

// 新增方法
public:
	// 获取股票数量
	size_t GetStockCount() const { return m_vecStocks.size(); }

	// 获取指定索引的股票
	const StockData* GetStock(size_t index) const {
		if (index < m_vecStocks.size())
			return &m_vecStocks[index];
		return nullptr;
	}
	
	// 通知TimeInfo视图更新实时数据
	void NotifyTimeInfoUpdate(const CString& stockCode, const CTime& time, double price, 
							 double avgPrice, double open, double preClose, 
							 double volume, double amount);
};
