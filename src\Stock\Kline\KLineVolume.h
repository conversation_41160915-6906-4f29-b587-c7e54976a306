﻿// KLine1View.h: CKLineVolume 类的接口
//

#pragma once
#include <vector>
#include "..\StockDef.h"  // 添加对StockDef.h的引用，以使用KLINE_DATA结构

// 按钮ID定义
#define ID_BTN_DAY 1001
#define ID_BTN_WEEK 1002
#define ID_BTN_MONTH 1003
#define ID_BTN_MACD 2001
#define ID_BTN_KDJ 2002
#define ID_BTN_RSI 2003
#define ID_BTN_SHOW_BUTTONBAR 3001  // 显示/隐藏按钮区的按钮ID


// 均线数据结构
struct MAData
{
	int nPeriod;           // 均线周期
	COLORREF clrLine;      // 均线颜色
	bool bShow;            // 是否显示
	std::vector<double> arrMA;  // 均线数据，使用std::vector替代CArray
	
	MAData() : nPeriod(0), clrLine(RGB(255,255,255)), bShow(false) {}
	
	// 自定义赋值操作符
	MAData& operator=(const MAData& src)
	{
		if (this != &src)
		{
			nPeriod = src.nPeriod;
			clrLine = src.clrLine;
			bShow = src.bShow;
			
			// 直接复制vector数据
			arrMA = src.arrMA;
		}
		return *this;
	}
	
	// 添加拷贝构造函数
	MAData(const MAData& src) : 
		nPeriod(src.nPeriod), 
		clrLine(src.clrLine), 
		bShow(src.bShow),
		arrMA(src.arrMA)  // vector的拷贝构造
	{
	}
};


// 按钮结构
struct ButtonInfo
{
	CRect rect;          // 按钮区域
	CString strText;     // 按钮文本
	int nID;             // 按钮ID
	COLORREF clrText;    // 文本颜色
	COLORREF clrBk;      // 背景颜色
	bool bHover;         // 鼠标悬停状态
	bool bSelected;      // 按钮选中状态
	bool bToggle;        // 是否为切换按钮（点击后保持选中状态）
	int nGroupID;        // 按钮组ID（同组按钮互斥选择）

	ButtonInfo() : nID(0), clrText(RGB(255, 255, 255)), clrBk(RGB(0, 0, 0)),
		bHover(false), bSelected(false), bToggle(true), nGroupID(0) {
	}
};


class CKLineVolume : public CView
{
protected: // 仅从序列化创建
	CKLineVolume() noexcept;
	DECLARE_DYNCREATE(CKLineVolume)

// 特性
public:
	CStockDoc* GetDocument() const;
	
	// 获取父视图CKLineView
	class CKLineView* GetParentKLineView() const;

// 操作
public:
	// 设置股票代码
	void SetStockCode(const CString& strCode);

// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint);


// 实现
public:
	virtual ~CKLineVolume();

protected:
	// 股票基本信息
	CString m_strCode;  // 股票代码

	// K线图区域相关成员变量
	CRect m_rectKLineArea;   // K线图区域
	CRect m_rectCoordArea;   // 坐标区域
	int m_nRightAreaWidth;   // 右侧坐标区宽度
	
	// 左侧垂直分割区域
	CRect m_rectInfoBar;     // 信息条区域
	CRect m_rectMainChart;   // K线主图区域
	CRect m_rectVolumeInfoBar; // 成交量信息条区域
	CRect m_rectVolume;      // 成交量区域
	CRect m_rectButtonBar;   // 按钮区域
	
	int m_nInfoBarHeight;    // 信息条高度
	int m_nVolumeInfoBarHeight; // 成交量信息条高度
	int m_nButtonBarHeight;  // 按钮区高度
	bool m_bShowButtonBar;   // 是否显示按钮区
	
	// 按钮数组
	std::vector<ButtonInfo> m_arrButtons;
	int m_nButtonWidth;      // 按钮宽度
	
	// K线数据
	std::vector<KLINE_DATA> m_vecKLine;	// K线数据数组，使用StockDef.h中定义的KLINE_DATA结构
	int m_nKLineCount;			// K线数量
	double m_fMaxPrice;			// 最高价
	double m_fMinPrice;			// 最低价
	double m_fMaxVolume;		// 最大成交量
	double m_fDisplayMaxPrice; // 当前显示范围内的最高价
	double m_fDisplayMinPrice; // 当前显示范围内的最低价
	
	// K线图显示参数
	int m_nKLineWidth;			// K线宽度
	int m_nKLineGap;			// K线间隔
	int m_nDisplayOffset;		// 显示偏移量（用于滚动）
	int m_nDisplayCount;		// 显示的K线数量
	double m_fScaleFactor;      // K线缩放系数
	int m_nBaseKLineWidth;      // 基准K线宽度
	int m_nBaseKLineGap;        // 基准K线间隔
	
	// 均线数据
	std::vector<MAData> m_arrMA;  // 均线数组
	
	// 成交量均线数据
	std::vector<MAData> m_arrVolumeMA;  // 成交量均线数组
	
	// 十字光标相关
	bool m_bShowCrossCursor;   // 是否显示十字光标
	CPoint m_ptCrossCursor;    // 十字光标位置
	int m_nCursorIndex;        // 当前光标对应的K线索引
	bool m_bMouseDown;         // 鼠标左键是否按下

// 生成的消息映射函数
protected:
	DECLARE_MESSAGE_MAP()
public:
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	afx_msg void OnLButtonUp(UINT nFlags, CPoint point);
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	
	// 均线菜单响应函数
	afx_msg void OnMAMenuSelect(UINT nID);
	afx_msg void OnUpdateMAMenu(CCmdUI *pCmdUI);
	afx_msg BOOL OnMouseWheel(UINT nFlags, short zDelta, CPoint pt);
	afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
	
private:
	// 辅助函数
	void InitButtons();
	void DrawInfoBar(CDC* pDC);
	void DrawMainChart(CDC* pDC);
	void DrawVolumeInfoBar(CDC* pDC); // 绘制成交量信息条
	void DrawVolumeChart(CDC* pDC);
	void DrawButtonBar(CDC* pDC);
	void DrawPriceScale(CDC* pDC);  // 价格坐标绘制函数
	int HitTestButton(CPoint point);
	
	// K线相关函数
	void DrawKLine(CDC* pDC);
	void DrawVolume(CDC* pDC);
	void CalcMinMaxValue();
	
	// 均线相关函数
	void InitMA();
	void CalcMA();
	void DrawMA(CDC* pDC);
	
	// 成交量均线相关函数
	void InitVolumeMA();
	void CalcVolumeMA();
	void DrawVolumeMA(CDC* pDC);
	
	// 十字光标相关函数
	void DrawCrossCursor(CDC* pDC);
	void UpdateInfoBarWithCursorData(CDC* pDC);
	int GetKLineIndexFromPoint(CPoint point);
	
	// 本地K线数据管理
	bool LoadKLineDataFromLocalFile(const CString& code, std::vector<KLINE_DATA>& klineData);
public:
	// 动态按钮管理函数
	//int AddButton(const CString& strText, int nID, bool bToggle = true, int nGroupID = 0);
	void SetButtonSelected(int nIndex, bool bSelected);
	void SetButtonSelectedByID(int nID, bool bSelected);
	bool IsButtonSelected(int nIndex) const;
	bool IsButtonSelectedByID(int nID) const;
	int GetSelectedButtonInGroup(int nGroupID) const;
};


