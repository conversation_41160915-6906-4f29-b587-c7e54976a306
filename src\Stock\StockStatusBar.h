﻿#pragma once

#include "pch.h"
#include "StockDef.h"
#include <vector>

// https://d.10jqka.com.cn/v6/time/hs_1A0001/last.js		// 上证
// https://d.10jqka.com.cn/v6/time/hs_399001/last.js		// 深证
// https://d.10jqka.com.cn/v6/time/hs_399006/last.js		// 创业
// https://d.10jqka.com.cn/v6/time/hs_1B0688/last.js		// 科创50
// https://d.10jqka.com.cn/v6/time/151_899050/last.js		// 北证50
//
//
// CStockStatusBar - 自定义状态栏，用于显示大盘信息
class CStockStatusBar : public CStatusBar
{
	DECLARE_DYNAMIC(CStockStatusBar)

public:
	CStockStatusBar();
	virtual ~CStockStatusBar();

	// 初始化状态栏
	BOOL Create(CWnd* pParentWnd, DWORD dwStyle = WS_CHILD | WS_VISIBLE | CBRS_BOTTOM,
		UINT nID = AFX_IDW_STATUS_BAR);

	// 设置大盘指数数据
	void SetMarketIndex(const MarketIndexData& indexData);

	// 批量设置多个指数数据
	void SetMarketIndices(const std::vector<MarketIndexData>& indices);

	// 设置市场统计数据
	void SetMarketStats(const MarketStatData& statsData);

	// 根据指数数据确定在状态栏中的位置
	int GetIndexPosition(const MarketIndexData& indexData);

	// 设置背景色
	BOOL SetBkColor(COLORREF crBk);

	// 刷新状态栏显示
	void UpdateDisplay();

protected:
	// 重绘函数
	afx_msg void OnPaint();
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);
	afx_msg void OnTimer(UINT_PTR nIDEvent);

	// 绘制单个面板
	void DrawPanel(CDC* pDC, int nIndex, const CString& strText, COLORREF textColor);

	// 绘制面板中的一部分文本（根据对齐方式）
	void DrawPanelSegment(CDC* pDC, int nIndex, const CString& strText, COLORREF textColor,
		UINT nFormat, int nPart = 0);

	// 创建状态栏面板
	void CreatePanels();

	// 更新日期时间
	void UpdateDateTime();

	// 重新计算面板宽度
	void RecalculatePanelWidths();

private:
	// 大盘指数数据
	std::vector<MarketIndexData> m_marketIndices;

	// 市场统计数据
	MarketStatData m_marketStats;

	// 状态栏面板矩形区域
	CArray<CRect, CRect&> m_panelRects;

	// 面板数量
	static const int PANEL_COUNT = 11;  // 增加一个日期时间面板

	// 状态栏字体
	CFont m_font;

	// 背景色
	COLORREF m_bgColor;

	// 边框色
	COLORREF m_borderColor;

	// 背景画刷
	CBrush m_bgBrush;

	// 时间更新定时器ID
	static const UINT_PTR TIMER_ID = 1001;

	// 当前日期时间
	CString m_strDateTime;

	DECLARE_MESSAGE_MAP()
};