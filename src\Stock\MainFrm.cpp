﻿// MainFrm.cpp: CMainFrame 类的实现
//

#include "pch.h"
#include "framework.h"
#include "Stock.h"
#include "Symbol\SymbolView.h"  // 替换为新的股票行情视图
#include "Symbol\SymbolGrid.h"  // 添加网格控件头文件
#include "Time\TimeView.h"      // 添加分时行情视图
#include "KLine\KLineView.h"     // K线视图
#include "MainFrm.h"
#include "StockDoc.h"  // 添加StockDoc头文件
#include "resource.h"  // 添加资源头文件
#include "..\Common\SQLiteRAII.h"  // 添加SQLite RAII包装类
#include "UIConstants.h"  // 添加UI常量定义
#include "..\Common\StringUtils.h"  // 添加字符串工具类

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CMainFrame

IMPLEMENT_DYNCREATE(CMainFrame, CFrameWnd)

BEGIN_MESSAGE_MAP(CMainFrame, CFrameWnd)
	ON_WM_CREATE()
	ON_WM_SIZE()
	ON_COMMAND(ID_VIEW_SHOW_INFO_AREA, &CMainFrame::OnViewShowInfoArea)
	ON_UPDATE_COMMAND_UI(ID_VIEW_SHOW_INFO_AREA, &CMainFrame::OnUpdateViewShowInfoArea)
	ON_COMMAND(ID_VIEW_SHOW_AUCTION_AREA, &CMainFrame::OnViewShowAuctionArea)
	ON_UPDATE_COMMAND_UI(ID_VIEW_SHOW_AUCTION_AREA, &CMainFrame::OnUpdateViewShowAuctionArea)
	ON_COMMAND(ID_VIEW_SHOW_VOLUME_AREA, &CMainFrame::OnViewShowVolumeArea)
	ON_UPDATE_COMMAND_UI(ID_VIEW_SHOW_VOLUME_AREA, &CMainFrame::OnUpdateViewShowVolumeArea)
	ON_COMMAND(ID_DOWN_STOCK_LIST, &CMainFrame::OnDownStockList)
	ON_COMMAND(ID_UPDATE_STOCK_LIST, &CMainFrame::OnUpdateStockList)
	ON_COMMAND(ID_APP_REFRESH, &CMainFrame::OnAppRefresh)
END_MESSAGE_MAP()

// CMainFrame 构造/析构

CMainFrame::CMainFrame() noexcept
{
	// TODO: 在此添加成员初始化代码
	m_nStockListViewID = 0;
	m_nTimeLineViewID = 1;
	m_nKLineViewID = 2;
	
	// 初始化视图状态为未完成
	m_bViewInitialized = false;
}

CMainFrame::~CMainFrame()
{
}
// TabPage点击回调函数
void CALLBACK OnTabPageClicked(int nIndex, LPARAM lParam)
{
	// lParam是CMainFrame指针
	CMainFrame* pMainFrame = (CMainFrame*)lParam;
	if (pMainFrame)
	{
		// 在状态栏显示当前选中的选项卡
		CString strStatus;
		strStatus.Format(_T("选中了第%d个选项卡"), nIndex + 1);
		
		// 根据选中的选项卡执行不同的操作
		switch (nIndex)
		{
		case 0: // 股票行情
			// 处理股票行情选项卡
			TRACE(_T("选中了股票行情选项卡\n"));
			pMainFrame->SwitchView(pMainFrame->m_nStockListViewID);
			break;
		case 1: // 分时行情
			// 处理分时行情选项卡
			TRACE(_T("选中了分时行情选项卡\n"));
			pMainFrame->SwitchView(pMainFrame->m_nTimeLineViewID);
			break;
		case 2: // 技术分析
			// 处理技术分析选项卡
			TRACE(_T("选中了技术分析选项卡\n"));
			pMainFrame->SwitchView(pMainFrame->m_nKLineViewID);
			break;
		}
	}
}
int CMainFrame::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CFrameWnd::OnCreate(lpCreateStruct) == -1)
		return -1;

	// 创建自定义状态栏
	if (!m_wndStatusBar.Create(this))
	{
		TRACE0("未能创建状态栏\n");
		return -1;      // 未能创建
	}
	

	return 0;
}


BOOL CMainFrame::PreCreateWindow(CREATESTRUCT& cs)
{
	if(!CFrameWnd::PreCreateWindow(cs))
		return FALSE;
	
	// 使用MFC框架默认的边框样式，不再使用自定义风格
	// 之前的自定义风格设置: 
	// cs.style = WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_THICKFRAME;
	// 恢复框架默认值，不再特别设置cs.style
	
	// 保留在任务栏显示的扩展样式
	cs.dwExStyle |= WS_EX_APPWINDOW;

	// 保留窗口类注册代码
	cs.lpszClass = AfxRegisterWndClass(0, 
		::LoadCursor(NULL, IDC_ARROW),
		(HBRUSH)::GetStockObject(BLACK_BRUSH),  // 使用黑色背景
		AfxGetApp()->LoadIcon(IDR_MAINFRAME));
	
	// 设置默认窗口大小为宽屏模式，更适合显示股票图表
	cs.cx = 1280;  // 更宽的显示区域
	cs.cy = 800;   // 适当的高度
	
	// 窗口居中显示
	cs.x = (GetSystemMetrics(SM_CXSCREEN) - cs.cx) / 2;
	cs.y = (GetSystemMetrics(SM_CYSCREEN) - cs.cy) / 2;
	return TRUE;
}

// 更新大盘指数数据
void CMainFrame::UpdateMarketIndex(const MarketIndexData& indexData)
{
	m_wndStatusBar.SetMarketIndex(indexData);
}

// 更新市场统计数据
void CMainFrame::UpdateMarketStats(const MarketStatData& statsData)
{
	m_wndStatusBar.SetMarketStats(statsData);
}

// CMainFrame 诊断

#ifdef _DEBUG
void CMainFrame::AssertValid() const
{
	CFrameWnd::AssertValid();
}

void CMainFrame::Dump(CDumpContext& dc) const
{
	CFrameWnd::Dump(dc);
}
#endif //_DEBUG


// CMainFrame 消息处理程序
// 处理窗口大小变化，重新调整分割窗口和选项卡的位置
void CMainFrame::OnSize(UINT nType, int cx, int cy)
{
	CFrameWnd::OnSize(nType, cx, cy);

	// 如果窗口被最小化，不必调整控件大小
	if (nType == SIZE_MINIMIZED)
		return;

	// 调整分隔窗口大小
	if (::IsWindow(m_wndSplitter.GetSafeHwnd()))
	{
		// 计算分隔窗口的大小
		CRect rcClient;
		GetClientRect(&rcClient);

		// 设置分隔窗口位置和大小
		//m_wndSplitter.MoveWindow(&rcClient);
	}
}

// 实现OnCreateClient方法，创建竖向分割的窗口
BOOL CMainFrame::OnCreateClient(LPCREATESTRUCT lpcs, CCreateContext* pContext)
{
	// 获取整个客户区大小
	CRect rect;
	GetClientRect(&rect);

	// 创建静态分隔窗口（左右分隔）
	if (!m_wndSplitter.CreateStatic(this, 1, 2, WS_CHILD | WS_VISIBLE))
	{
		TRACE0("未能创建分隔窗口\n");
		return FALSE;
	}

	// 选项卡控件的宽度
	const int TAB_WIDTH = 35;  // 选项卡宽度

	// 创建选项卡控件
	if (!m_wndTabPage.Create(CRect(0, 0, TAB_WIDTH, rect.Height()), &m_wndSplitter, m_wndSplitter.IdFromRowCol(0, 0)))
	{
		TRACE0("创建选项卡控件失败\n");
		return FALSE;
	}

	// 设置选项卡点击回调函数
	m_wndTabPage.SetTabClickCallback(OnTabPageClicked, (LPARAM)this);

	// 创建并保存视图ID
	m_nStockListViewID = m_wndSplitter.AddView(0, 1, RUNTIME_CLASS(CSymbolView), pContext);
	
	// 创建分时行情视图
	m_nTimeLineViewID = m_wndSplitter.AddView(0, 1, RUNTIME_CLASS(CTimeView), pContext);
	
	// 创建K线图视图
	m_nKLineViewID = m_wndSplitter.AddView(0, 1, RUNTIME_CLASS(CKLineView), pContext);

	// 添加新的选项卡
	m_wndTabPage.AddPage(_T("股票行情"));
	m_wndTabPage.AddPage(_T("分时行情"));
	m_wndTabPage.AddPage(_T("技术分析"));

	// 设置选项卡高度
	m_wndTabPage.SetTabHeight(UIConstants::DEFAULT_TAB_HEIGHT);

	// 设置最小窗格大小
	m_wndSplitter.SetColumnInfo(0, UIConstants::MIN_COLUMN_WIDTH, 0);

	// 重新计算窗格布局
	m_wndSplitter.RecalcLayout();
	m_wndSplitter.SetLocked(TRUE);
	
	// 默认显示股票列表视图
	m_wndSplitter.SwitchView(m_nStockListViewID);
	
	return TRUE;
}

// 添加SwitchView方法实现
void CMainFrame::SwitchView(int ViewID)
{
	m_wndSplitter.SwitchView(ViewID);
	
	// 同时更新选项卡状态
	if (ViewID == m_nStockListViewID)
	{
		m_wndTabPage.SetCurSel(0);  // 股票行情
	}
	else if (ViewID == m_nTimeLineViewID)
	{
		m_wndTabPage.SetCurSel(1);  // 分时行情
	}
	else if (ViewID == m_nKLineViewID)
	{
		m_wndTabPage.SetCurSel(2);  // 技术分析
	}
	
	// 获取新切换的视图，并设置焦点
	CWnd* pNewView = m_wndSplitter.GetView(ViewID);
	if (pNewView && ::IsWindow(pNewView->GetSafeHwnd()))
	{
		// 特殊处理CSymbolView，需要设置其中的网格控件焦点
		if (ViewID == m_nStockListViewID && pNewView->IsKindOf(RUNTIME_CLASS(CSymbolView)))
		{
			// 获取CSymbolView中的网格控件并设置焦点
			CSymbolView* pSymbolView = (CSymbolView*)pNewView;
			CSymbolGrid* pGrid = pSymbolView->GetSymbolGrid();
			if (pGrid && ::IsWindow(pGrid->GetSafeHwnd()))
			{
				pGrid->SetFocus();
				TRACE(_T("已将焦点设置到股票列表网格控件\n"));
				return;
			}
		}
		// 特殊处理CTimeView，需要设置其中的TimeLine控件焦点
		else if (ViewID == m_nTimeLineViewID && pNewView->IsKindOf(RUNTIME_CLASS(CTimeView)))
		{
			// 获取CTimeView中的TimeLine控件并设置焦点
			CTimeView* pTimeView = (CTimeView*)pNewView;
			CTimeLine* pTimeLine = pTimeView->GetTimeLineView();
			if (pTimeLine && ::IsWindow(pTimeLine->GetSafeHwnd()))
			{
				pTimeLine->SetFocus();
				TRACE(_T("已将焦点设置到分时图控件\n"));
				return;
			}
		}
		// 特殊处理CKLineView，需要设置其中的KLineVolume控件焦点
		else if (ViewID == m_nKLineViewID && pNewView->IsKindOf(RUNTIME_CLASS(CKLineView)))
		{
			// 获取CKLineView并尝试设置子视图焦点
			CKLineView* pKLineView = (CKLineView*)pNewView;
			if (pKLineView->m_nVolumeViewID > 0)
			{
				CWnd* pVolumeView = pKLineView->m_wndSplitter2.GetView(pKLineView->m_nVolumeViewID);
				if (pVolumeView && pVolumeView->IsKindOf(RUNTIME_CLASS(CKLineVolume)) && ::IsWindow(pVolumeView->GetSafeHwnd()))
				{
					pVolumeView->SetFocus();
					TRACE(_T("已将焦点设置到K线成交量视图\n"));
					return;
				}
			}
		}
		
		// 其他视图直接设置焦点
		pNewView->SetFocus();
		TRACE(_T("已将焦点设置到新视图\n"));
	}
}

// 添加视图菜单消息处理函数实现
void CMainFrame::OnUpdateViewShowInfoArea(CCmdUI* pCmdUI)
{
	// 查找当前活动视图
	CView* pActiveView = GetActiveView();
	if (pActiveView && pActiveView->IsKindOf(RUNTIME_CLASS(CTimeView)))
	{
		CTimeView* pTimeView = (CTimeView*)pActiveView;
		CTimeLine* pTimeLine = pTimeView->GetTimeLineView();
		if (pTimeLine)
		{
			// 根据当前状态更新菜单项
			//pCmdUI->SetCheck(pTimeLine->IsInfoAreaVisible());
			//pCmdUI->Enable(TRUE);
			return;
		}
	}
	
	// 如果不是分时视图，禁用菜单项
	pCmdUI->Enable(FALSE);
}

void CMainFrame::OnViewShowInfoArea()
{
	// 查找当前活动视图
	CView* pActiveView = GetActiveView();
	if (pActiveView && pActiveView->IsKindOf(RUNTIME_CLASS(CTimeView)))
	{
		CTimeView* pTimeView = (CTimeView*)pActiveView;
		CTimeLine* pTimeLine = pTimeView->GetTimeLineView();
		if (pTimeLine)
		{
			// 切换个股资讯区域的显示状态
			//pTimeLine->ToggleInfoArea();
		}
	}
}

void CMainFrame::OnUpdateViewShowAuctionArea(CCmdUI* pCmdUI)
{
	// 查找当前活动视图
	CView* pActiveView = GetActiveView();
	if (pActiveView && pActiveView->IsKindOf(RUNTIME_CLASS(CTimeView)))
	{
		CTimeView* pTimeView = (CTimeView*)pActiveView;
		CTimeLine* pTimeLine = pTimeView->GetTimeLineView();
		if (pTimeLine)
		{
			// 根据当前状态更新菜单项
			//pCmdUI->SetCheck(pTimeLine->IsAuctionAreaVisible());
			//pCmdUI->Enable(TRUE);
			return;
		}
	}
	
	// 如果不是分时视图，禁用菜单项
	pCmdUI->Enable(FALSE);
}

void CMainFrame::OnViewShowAuctionArea()
{
	// 查找当前活动视图
	CView* pActiveView = GetActiveView();
	if (pActiveView && pActiveView->IsKindOf(RUNTIME_CLASS(CTimeView)))
	{
		CTimeView* pTimeView = (CTimeView*)pActiveView;
		CTimeLine* pTimeLine = pTimeView->GetTimeLineView();
		if (pTimeLine)
		{
			// 切换集合竞价区域的显示状态
			//pTimeLine->ToggleAuctionArea();
		}
	}
}

void CMainFrame::OnUpdateViewShowVolumeArea(CCmdUI* pCmdUI)
{
	// 查找当前活动视图
	CView* pActiveView = GetActiveView();
	if (pActiveView && pActiveView->IsKindOf(RUNTIME_CLASS(CTimeView)))
	{
		CTimeView* pTimeView = (CTimeView*)pActiveView;
		CTimeLine* pTimeLine = pTimeView->GetTimeLineView();
		if (pTimeLine)
		{
			// 根据当前状态更新菜单项
			//pCmdUI->SetCheck(pTimeLine->IsVolumeAreaVisible());
			//pCmdUI->Enable(TRUE);
			return;
		}
	}
	
	// 如果不是分时视图，禁用菜单项
	pCmdUI->Enable(FALSE);
}

void CMainFrame::OnViewShowVolumeArea()
{
	// 查找当前活动视图
	CView* pActiveView = GetActiveView();
	if (pActiveView && pActiveView->IsKindOf(RUNTIME_CLASS(CTimeView)))
	{
		CTimeView* pTimeView = (CTimeView*)pActiveView;
		CTimeLine* pTimeLine = pTimeView->GetTimeLineView();
		if (pTimeLine)
		{
			// 切换成交量区域的显示状态
			//pTimeLine->ToggleVolumeArea();
		}
	}
}

#include "LoadingDlg.h"
// 下载股票列表
void CMainFrame::OnDownStockList()
{
	// 设置等待光标
	CWaitCursor waitCursor;
	
	// 创建进度对话框
	CLoadingDlg* pLoadingDlg = new CLoadingDlg();
	if (!pLoadingDlg->Create(this))
	{
		delete pLoadingDlg;
		pLoadingDlg = NULL;
		TRACE(_T("进度对话框创建失败，将无法显示下载进度\n"));
	}
	else
	{
		pLoadingDlg->SetStatusText(_T("正在连接服务器，准备下载股票列表..."));
		pLoadingDlg->SetProgress(5);
		pLoadingDlg->ShowWindow(SW_SHOW);
		pLoadingDlg->UpdateWindow();
	}
	
	try
	{
		// 初始化HTTP连接
		CWinHttp http;
		CString strHtml;
		const char* url = "https://apphq.longhuvip.com/w1/api/index.php?Order=9&st=6000&a=KanPanNew&c=YiDongKanPan&apiv=w29&Type=1";
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("正在连接龙虎网API..."));
			pLoadingDlg->SetProgress(10);
		}
		
		// 设置HTTP头
		http.AddHeader("Name", "KeHanNan");
		http.AddHeader("Address", "XinNing");
		http.AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36");

		// 先尝试连接到服务器
		if (!http.ConnectHttpServer("https://apphq.longhuvip.com"))
		{
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("连接服务器失败，请检查网络连接！"), MB_ICONERROR);
			return;
		}
		
		if (pLoadingDlg)
		{
			pLoadingDlg->SetStatusText(_T("已连接龙虎网API，正在发送请求..."));
			pLoadingDlg->SetProgress(15);
		}
		
		// 连接成功后发送HTTP请求并获取响应
		if (!http.Request(url, HttpGet, strHtml))
		{
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("发送请求失败，请稍后重试！"), MB_ICONERROR);
			return;
		}
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("正在解析JSON数据..."));
			pLoadingDlg->SetProgress(30);
		}
		// 解析JSON
		JSONValue* jsonValue = JSON::Parse(strHtml.GetBuffer(0));
		
		
		if (!jsonValue)
		{
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("解析JSON数据失败！"), MB_ICONERROR);
			return;
		}
		
		// 检查JSON是否为对象
		if (!jsonValue->IsObject())
		{
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("JSON数据格式不正确！"), MB_ICONERROR);
			return;
		}
		
		// 获取根JSON对象
		JSONObject jsonObj = jsonValue->AsObject();
		
		// 提取日期信息
		CString strDate = _T("未知日期");
		if (jsonObj.find(L"Date") != jsonObj.end() && jsonObj[L"Date"]->IsString())
		{
			std::wstring dateStr = jsonObj[L"Date"]->AsString();
			strDate = dateStr.c_str();
		}
		
		// 获取股票列表数组
		if (jsonObj.find(L"List") == jsonObj.end() || !jsonObj[L"List"]->IsArray())
		{
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("未找到股票列表数据！"), MB_ICONERROR);
			return;
		}
		
		JSONArray stockList = jsonObj[L"List"]->AsArray();
		int totalStocks = stockList.size();
		
		if (totalStocks == 0)
		{
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("股票列表为空！"), MB_ICONWARNING);
			return;
		}
		
		if (pLoadingDlg) 
		{
			CString strStatus;
			strStatus.Format(_T("准备保存数据到数据库... 日期: %s, 共 %d 支股票"), strDate, totalStocks);
			pLoadingDlg->SetStatusText(strStatus);
			pLoadingDlg->SetProgress(40);
		}
		
		// 准备数据库操作
		sqlite3* db = NULL;
		char* errMsg = NULL;
		

		TCHAR szPath[MAX_PATH];
		GetModuleFileName(NULL, szPath, MAX_PATH);
		CString strAppPath(szPath);
		int nIndex = strAppPath.ReverseFind('\\');
		if (nIndex > 0)
			strAppPath = strAppPath.Left(nIndex + 1);
		
		CString strDBPath = strAppPath + _T("Stock.db");
		
		// 打开或创建数据库
		if (sqlite3_open(CStringA(strDBPath), &db) != SQLITE_OK)
		{
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("无法打开或创建数据库！"), MB_ICONERROR);
			return;
		}
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("正在创建数据表..."));
			pLoadingDlg->SetProgress(50);
		}
		
		// 创建股票表，如果不存在的话
		const char* createTableSQL = 
			"CREATE TABLE IF NOT EXISTS X00 ("
			"ID INTEGER PRIMARY KEY AUTOINCREMENT,"  // 添加自增主键ID
			"CODE TEXT UNIQUE,"       // 股票代码变为唯一索引
			"NAME TEXT,"              // 股票名称
			"PLATE TEXT,"            // 所属板块
			"INDUSTRY TEXT,"          // 行业
			"THEME TEXT,"             // 题材
			"STYLE TEXT,"             // 风格
			"CIRCULATING_VALUE REAL," // 流通值(亿元)
			"ACTUAL_CIRCULATING REAL,"// 实际流通值(亿元)
			"UPDATE_DATE INTEGER"     // 更新日期，使用整数存储时间戳
			")";
		
		if (sqlite3_exec(db, createTableSQL, NULL, NULL, &errMsg) != SQLITE_OK)
		{
			std::string errorMsg = "创建数据表失败: ";
			errorMsg += errMsg;
			sqlite3_free(errMsg);
			sqlite3_close(db);
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(CString(errorMsg.c_str()), MB_ICONERROR);
			return;
		}
		
		// 开始事务以提高插入性能
		sqlite3_exec(db, "BEGIN TRANSACTION", NULL, NULL, NULL);
		
		// 准备插入或更新语句
		const char* insertSQL = 
			"INSERT OR REPLACE INTO X00 (CODE, NAME, PLATE, INDUSTRY, THEME, STYLE, CIRCULATING_VALUE, ACTUAL_CIRCULATING, UPDATE_DATE) "
			"VALUES (?, ?, ?, '', '', '', 0, 0, strftime('%s','now'))";
		
		sqlite3_stmt* stmt = NULL;
		if (sqlite3_prepare_v2(db, insertSQL, -1, &stmt, NULL) != SQLITE_OK)
		{
			sqlite3_exec(db, "ROLLBACK", NULL, NULL, NULL);
			sqlite3_close(db);
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("准备SQL语句失败！"), MB_ICONERROR);
			return;
		}
		
		// 解析股票数据并插入数据库
		int successCount = 0;
		for (int i = 0; i < totalStocks; i++)
		{
			JSONObject stock = stockList[i]->AsObject();
			
			// 获取股票代码、名称和板块
			std::wstring stockCode = L"";
			std::wstring stockName = L"";
			std::wstring stockPlate = L"";
			
			if (stock.find(L"stock_code") != stock.end() && stock[L"stock_code"]->IsString())
				stockCode = stock[L"stock_code"]->AsString();
			
			if (stock.find(L"stock_name") != stock.end() && stock[L"stock_name"]->IsString())
				stockName = stock[L"stock_name"]->AsString();
			
			if (stock.find(L"plate") != stock.end() && stock[L"plate"]->IsString())
				stockPlate = stock[L"plate"]->AsString();
			
			// 确保有股票代码
			if (stockCode.empty())
				continue;
			
			// 直接使用宽字符串转换为ANSI，不进行额外的编码转换
			// API返回的数据是ANSI编码，SimpleJSON解析后得到的wstring，
			// 使用字符串工具类进行转换（带缓存优化）
			std::string codeANSI = StringUtils::WideToANSI(stockCode);
			std::string nameANSI = StringUtils::WideToANSI(stockName);
			std::string plateANSI = StringUtils::WideToANSI(stockPlate);
			
			// 绑定参数
			sqlite3_bind_text(stmt, 1, codeANSI.c_str(), -1, SQLITE_TRANSIENT);
			sqlite3_bind_text(stmt, 2, nameANSI.c_str(), -1, SQLITE_TRANSIENT);
			sqlite3_bind_text(stmt, 3, plateANSI.c_str(), -1, SQLITE_TRANSIENT);

			// 执行插入
			if (sqlite3_step(stmt) == SQLITE_DONE)
				successCount++;

			// 重置语句以重用
			sqlite3_reset(stmt);
			sqlite3_clear_bindings(stmt);
			
			// 更新进度
			if (pLoadingDlg && (i % 50 == 0 || i == totalStocks - 1))
			{
				int progress = 50 + (i * 45 / totalStocks);
				pLoadingDlg->SetProgress(progress);
				CString strStatus;
				strStatus.Format(_T("正在保存股票数据... %d/%d"), i + 1, totalStocks);
				pLoadingDlg->SetStatusText(strStatus);
				
				// 处理消息循环，使UI响应
				MSG msg;
				while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE))
				{
					TranslateMessage(&msg);
					DispatchMessage(&msg);
				}
			}
		}
		
		// 完成事务
		sqlite3_finalize(stmt);
		sqlite3_exec(db, "COMMIT", NULL, NULL, NULL);
		sqlite3_close(db);
		
		// 释放JSON资源
		delete jsonValue;
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("正在重新加载股票列表..."));
			pLoadingDlg->SetProgress(95);
		}
		
		// 重新加载股票列表
		CStockDoc* pDoc = (CStockDoc*)GetActiveDocument();
		if (pDoc)
		{
			// 重新初始化股票列表
			pDoc->InitStockList();
			
			// 通知视图更新
			pDoc->UpdateAllViews(NULL);
			
			// 使网格控件缓存失效，强制重新加载数据
			CSymbolGrid* pGrid = GetSymbolGrid();
			if (pGrid)
			{
				pGrid->InvalidateCache();
				pGrid->RedrawAll();
			}
		}
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("股票列表加载完成！"));
			pLoadingDlg->SetProgress(100);
			
			// 延时关闭进度对话框，让用户能看到完成消息
			Sleep(1000);
			delete pLoadingDlg;
			pLoadingDlg = NULL;
		}
		
		// 显示成功消息
		CString strSuccess;
		strSuccess.Format(_T("成功下载了 %d 支股票的数据！\n数据日期: %s"), stockList.size(), strDate);
		AfxMessageBox(strSuccess, MB_ICONINFORMATION);
	}
	catch (...)
	{
		if (pLoadingDlg)
		{
			delete pLoadingDlg;
			pLoadingDlg = NULL;
		}
		AfxMessageBox(_T("下载股票列表时发生异常！"), MB_ICONERROR);
	}
}


// 更新股票列表
void CMainFrame::OnUpdateStockList()
{
	// 设置等待光标
	CWaitCursor waitCursor;
	
	// 创建进度对话框
	CLoadingDlg* pLoadingDlg = new CLoadingDlg();
	if (!pLoadingDlg->Create(this))
	{
		delete pLoadingDlg;
		pLoadingDlg = NULL;
		TRACE(_T("进度对话框创建失败，将无法显示更新进度\n"));
	}
	else
	{
		pLoadingDlg->SetStatusText(_T("正在连接服务器，准备更新股票数据..."));
		pLoadingDlg->SetProgress(5);
		pLoadingDlg->ShowWindow(SW_SHOW);
		pLoadingDlg->UpdateWindow();
	}
	
	try
	{
		// 初始化HTTP连接
		CWinHttp http;
		CString strHtml;
		const char* url = "https://apphq.longhuvip.com/w1/api/index.php?Order=9&st=6000&a=KanPanNew&c=YiDongKanPan&apiv=w29&Type=1";
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("正在连接龙虎网API..."));
			pLoadingDlg->SetProgress(10);
		}
		
		// 设置HTTP头
		http.AddHeader("Name", "KeHanNan");
		http.AddHeader("Address", "XinNing");
		http.AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36");

		// 先尝试连接到服务器
		if (!http.ConnectHttpServer("https://apphq.longhuvip.com"))
		{
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("连接服务器失败，请检查网络连接！"), MB_ICONERROR);
			return;
		}
		
		if (pLoadingDlg)
		{
			pLoadingDlg->SetStatusText(_T("已连接龙虎网API，正在发送请求..."));
			pLoadingDlg->SetProgress(15);
		}
		
		// 连接成功后发送HTTP请求并获取响应
		if (!http.Request(url, HttpGet, strHtml))
		{
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("发送请求失败，请稍后重试！"), MB_ICONERROR);
			return;
		}
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("正在解析JSON数据..."));
			pLoadingDlg->SetProgress(30);
		}
		// 解析JSON
		JSONValue* jsonValue = JSON::Parse(strHtml.GetBuffer(0));
		
		
		if (!jsonValue)
		{
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("解析JSON数据失败！"), MB_ICONERROR);
			return;
		}
		
		// 检查JSON是否为对象
		if (!jsonValue->IsObject())
		{
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("JSON数据格式不正确！"), MB_ICONERROR);
			return;
		}
		
		// 获取根JSON对象
		JSONObject jsonObj = jsonValue->AsObject();
		
		// 提取日期信息
		CString strDate = _T("未知日期");
		if (jsonObj.find(L"Date") != jsonObj.end() && jsonObj[L"Date"]->IsString())
		{
			std::wstring dateStr = jsonObj[L"Date"]->AsString();
			strDate = dateStr.c_str();
		}
		
		// 获取股票列表数组
		if (jsonObj.find(L"List") == jsonObj.end() || !jsonObj[L"List"]->IsArray())
		{
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("未找到股票列表数据！"), MB_ICONERROR);
			return;
		}
		
		JSONArray stockList = jsonObj[L"List"]->AsArray();
		int totalStocks = stockList.size();
		
		if (totalStocks == 0)
		{
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("股票列表为空！"), MB_ICONWARNING);
			return;
		}
		
		if (pLoadingDlg) 
		{
			CString strStatus;
			strStatus.Format(_T("准备更新数据到数据库... 日期: %s, 共 %d 支股票"), strDate, totalStocks);
			pLoadingDlg->SetStatusText(strStatus);
			pLoadingDlg->SetProgress(40);
		}
		
		// 准备数据库操作
		sqlite3* db = NULL;
		char* errMsg = NULL;
		

		TCHAR szPath[MAX_PATH];
		GetModuleFileName(NULL, szPath, MAX_PATH);
		CString strAppPath(szPath);
		int nIndex = strAppPath.ReverseFind('\\');
		if (nIndex > 0)
			strAppPath = strAppPath.Left(nIndex + 1);
		
		CString strDBPath = strAppPath + _T("Stock.db");
		
		// 打开数据库
		if (sqlite3_open(CStringA(strDBPath), &db) != SQLITE_OK)
		{
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("无法打开数据库！"), MB_ICONERROR);
			return;
		}
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("正在检查数据表..."));
			pLoadingDlg->SetProgress(50);
		}
		
		// 开始事务以提高更新性能
		sqlite3_exec(db, "BEGIN TRANSACTION", NULL, NULL, NULL);
		
		// 准备更新语句 - 这里使用INSERT OR REPLACE而不是重建表
		const char* updateSQL = 
			"INSERT OR REPLACE INTO X00 (CODE, NAME, PLATE, INDUSTRY, THEME, STYLE, CIRCULATING_VALUE, ACTUAL_CIRCULATING, UPDATE_DATE) "
			"VALUES (?, ?, ?, "
			"(SELECT INDUSTRY FROM X00 WHERE CODE = ? LIMIT 1), " // 保留原有行业信息
			"(SELECT THEME FROM X00 WHERE CODE = ? LIMIT 1), "    // 保留原有题材信息
			"(SELECT STYLE FROM X00 WHERE CODE = ? LIMIT 1), "    // 保留原有风格信息
			"(SELECT CIRCULATING_VALUE FROM X00 WHERE CODE = ? LIMIT 1), " // 保留原有流通值
			"(SELECT ACTUAL_CIRCULATING FROM X00 WHERE CODE = ? LIMIT 1), " // 保留原有实际流通值
			"strftime('%s','now'))"; // 更新时间戳
		
		sqlite3_stmt* stmt = NULL;
		if (sqlite3_prepare_v2(db, updateSQL, -1, &stmt, NULL) != SQLITE_OK)
		{
			sqlite3_exec(db, "ROLLBACK", NULL, NULL, NULL);
			sqlite3_close(db);
			delete jsonValue;
			if (pLoadingDlg)
			{
				delete pLoadingDlg;
				pLoadingDlg = NULL;
			}
			AfxMessageBox(_T("准备SQL语句失败！"), MB_ICONERROR);
			return;
		}
		
		// 解析股票数据并更新数据库
		int successCount = 0;
		for (int i = 0; i < totalStocks; i++)
		{
			JSONObject stock = stockList[i]->AsObject();
			
			// 获取股票代码、名称和板块
			std::wstring stockCode = L"";
			std::wstring stockName = L"";
			std::wstring stockPlate = L"";
			
			if (stock.find(L"stock_code") != stock.end() && stock[L"stock_code"]->IsString())
				stockCode = stock[L"stock_code"]->AsString();
			
			if (stock.find(L"stock_name") != stock.end() && stock[L"stock_name"]->IsString())
				stockName = stock[L"stock_name"]->AsString();
			
			if (stock.find(L"plate") != stock.end() && stock[L"plate"]->IsString())
				stockPlate = stock[L"plate"]->AsString();
			
			// 确保有股票代码
			if (stockCode.empty())
				continue;

			// 使用字符串工具类进行转换（带缓存优化）
			std::string codeANSI = StringUtils::WideToANSI(stockCode);
			std::string nameANSI = StringUtils::WideToANSI(stockName);
			std::string plateANSI = StringUtils::WideToANSI(stockPlate);
			
			// 绑定参数 - 传入多次股票代码用于子查询
			sqlite3_bind_text(stmt, 1, codeANSI.c_str(), -1, SQLITE_TRANSIENT); // 代码
			sqlite3_bind_text(stmt, 2, nameANSI.c_str(), -1, SQLITE_TRANSIENT); // 名称
			sqlite3_bind_text(stmt, 3, plateANSI.c_str(), -1, SQLITE_TRANSIENT); // 板块
			sqlite3_bind_text(stmt, 4, codeANSI.c_str(), -1, SQLITE_TRANSIENT); // 用于行业子查询
			sqlite3_bind_text(stmt, 5, codeANSI.c_str(), -1, SQLITE_TRANSIENT); // 用于题材子查询
			sqlite3_bind_text(stmt, 6, codeANSI.c_str(), -1, SQLITE_TRANSIENT); // 用于风格子查询
			sqlite3_bind_text(stmt, 7, codeANSI.c_str(), -1, SQLITE_TRANSIENT); // 用于流通值子查询
			sqlite3_bind_text(stmt, 8, codeANSI.c_str(), -1, SQLITE_TRANSIENT); // 用于实际流通值子查询

			// 执行更新
			if (sqlite3_step(stmt) == SQLITE_DONE)
				successCount++;

			// 重置语句以重用
			sqlite3_reset(stmt);
			sqlite3_clear_bindings(stmt);
			
			// 更新进度
			if (pLoadingDlg && (i % 50 == 0 || i == totalStocks - 1))
			{
				int progress = 50 + (i * 45 / totalStocks);
				pLoadingDlg->SetProgress(progress);
				CString strStatus;
				strStatus.Format(_T("正在更新股票数据... %d/%d"), i + 1, totalStocks);
				pLoadingDlg->SetStatusText(strStatus);
				
				// 处理消息循环，使UI响应
				MSG msg;
				while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE))
				{
					TranslateMessage(&msg);
					DispatchMessage(&msg);
				}
			}
		}
		
		// 完成事务
		sqlite3_finalize(stmt);
		sqlite3_exec(db, "COMMIT", NULL, NULL, NULL);
		sqlite3_close(db);
		
		// 释放JSON资源
		delete jsonValue;
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("正在重新加载股票列表..."));
			pLoadingDlg->SetProgress(95);
		}
		
		// 重新加载股票列表
		CStockDoc* pDoc = (CStockDoc*)GetActiveDocument();
		if (pDoc)
		{
			// 重新初始化股票列表
			pDoc->InitStockList();
			
			// 通知视图更新
			pDoc->UpdateAllViews(NULL);
			
			// 使网格控件缓存失效，强制重新加载数据
			CSymbolGrid* pGrid = GetSymbolGrid();
			if (pGrid)
			{
				pGrid->InvalidateCache();
				pGrid->RedrawAll();
			}
		}
		
		if (pLoadingDlg) 
		{
			pLoadingDlg->SetStatusText(_T("股票列表更新完成！"));
			pLoadingDlg->SetProgress(100);
			
			// 延时关闭进度对话框，让用户能看到完成消息
			Sleep(1000);
			delete pLoadingDlg;
			pLoadingDlg = NULL;
		}
		
		// 显示成功消息
		CString strSuccess;
		strSuccess.Format(_T("成功更新了 %d 支股票的数据！\n数据日期: %s"), successCount, strDate);
		AfxMessageBox(strSuccess, MB_ICONINFORMATION);
	}
	catch (...)
	{
		if (pLoadingDlg)
		{
			delete pLoadingDlg;
			pLoadingDlg = NULL;
		}
		AfxMessageBox(_T("更新股票列表时发生异常！"), MB_ICONERROR);
	}
}

// 获取网格控件
CSymbolGrid* CMainFrame::GetSymbolGrid()
{
	// 查找股票列表视图
	POSITION pos = GetActiveDocument()->GetFirstViewPosition();
	while (pos != NULL)
	{
		CView* pView = GetActiveDocument()->GetNextView(pos);
		if (pView && pView->IsKindOf(RUNTIME_CLASS(CSymbolView)))
		{
			CSymbolView* pSymbolView = (CSymbolView*)pView;
			return pSymbolView->GetSymbolGrid();
		}
	}
	
	// 如果没有找到，返回NULL
	return NULL;
}

// 处理ID_APP_REFRESH命令，通知文档更新所有视图
void CMainFrame::OnAppRefresh()
{
    // 获取活动文档
    CStockDoc* pDoc = (CStockDoc*)GetActiveDocument();
    if (pDoc)
    {
        // 直接调用UpdateAllViews而不是使用消息机制
        pDoc->UpdateAllViews(NULL);
    }
}


