﻿#pragma once
#include <windows.h>

// UI相关常量定义，避免魔法数字

namespace UIConstants {
    // 选项卡相关常量
    const int DEFAULT_TAB_HEIGHT = 120;
    const int DEFAULT_TAB_WIDTH = 200;
    
    // 字体相关常量
    const int DEFAULT_FONT_SIZE = -18;
    const int BOLD_FONT_SIZE = -18;
    const TCHAR* const DEFAULT_FONT_NAME = _T("微软雅黑");
    
    // 网格相关常量
    const int MIN_COLUMN_WIDTH = 35;
    const int DEFAULT_BUFFER_SIZE = 4096;
    
    // 颜色常量
    const COLORREF COLOR_BLACK = RGB(0, 0, 0);
    const COLORREF COLOR_WHITE = RGB(255, 255, 255);
    const COLORREF COLOR_RED_UP = RGB(255, 40, 40);    // 上涨红色
    const COLORREF COLOR_GREEN_DOWN = RGB(40, 255, 40); // 下跌绿色
    const COLORREF COLOR_YELLOW = RGB(255, 255, 0);     // 均价线黄色
    
    // 定时器相关常量
    const UINT TIMER_UPDATE_DELAY = 100;     // 100毫秒
    const UINT TIMER_INIT_DELAY = 200;       // 200毫秒
    const UINT TIMER_REDRAW_ID = 1001;
    const UINT TIMER_INIT_ID = 1002;
    
    // 网络请求相关常量
    const int HTTP_REQUEST_TIMEOUT = 30000;  // 30秒超时
    const int BATCH_REQUEST_DELAY = 100;     // 批量请求间隔100毫秒
    const int DATA_UPDATE_INTERVAL = 3000;   // 数据更新间隔3秒
    
    // 分割窗口相关常量
    const int INFO_AREA_WIDTH = 300;         // 信息区域宽度
    const int MIN_LEFT_WIDTH = 400;          // 左侧最小宽度
    
    // 数据库相关常量
    const int MAX_STOCK_CODE_LENGTH = 10;
    const int MAX_STOCK_NAME_LENGTH = 50;
    const int MAX_PLATE_NAME_LENGTH = 100;
}

// 注意：消息常量已在 NetData.h 中定义
// #define WM_USER_DATA_UPDATED (WM_USER + 100)
// #define WM_UPDATE_STOCK_DATA (WM_USER + 101)
// 如需要其他消息常量，请在此添加

// 选项卡索引常量
namespace TabConstants {
    const int TAB_INDEX_ALL = 0;      // 全部股票
    const int TAB_INDEX_SH_A = 1;     // 上证A股
    const int TAB_INDEX_SZ_A = 2;     // 深证A股
    const int TAB_INDEX_GEM = 3;      // 创业板
    const int TAB_INDEX_STAR = 4;     // 科创板
    const int TAB_INDEX_BJ_A = 5;     // 北证A股
}

// 网络URL常量
namespace URLConstants {
    extern const char* SINA_REALTIME_URL;
    extern const char* TENCENT_TIMELINE_URL;
    extern const char* TONGHUASHUN_MARKET_URL;
    extern const char* LONGHUVIP_FUND_URL;
}

// 错误码常量
namespace ErrorConstants {
    const int ERROR_NETWORK_FAILED = -1;
    const int ERROR_PARSE_FAILED = -2;
    const int ERROR_DATABASE_FAILED = -3;
    const int ERROR_MEMORY_ALLOCATION = -5;
}
