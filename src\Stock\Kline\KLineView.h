﻿// KLineView.h: CKLineView 类的接口
//

#pragma once
#include "..\StockSplitter.h"
#include "KLineVolume.h"
#include "KLineInfo.h"
#include "KLineIndicator.h"
#include "KLineTime.h"
#include "KLineSignal.h"

class CStockDoc;

// CKLineView - K线图主视图，包含三个子视图：K线图、成交量图和技术指标图
class CKLineView : public CView
{
protected: // 仅从序列化创建
	CKLineView() noexcept;
	DECLARE_DYNCREATE(CKLineView)

// 特性
public:
	CStockDoc* GetDocument() const;

// 操作
public:
	// 设置股票代码
	void SetStockCode(const CString& strCode);
	
	// 更新所有子视图数据
	void UpdateChildViews(const CString& strCode);

// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnInitialUpdate();
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint);

// 实现
public:
	virtual ~CKLineView();

	// 分割窗口
	CStockSplitter m_wndSplitter1;  // 主分割窗口（左右分割）
	CStockSplitter m_wndSplitter2;  // 左侧子分割窗口（上下分割）

	// 子视图ID
	int m_nVolumeViewID;    // K线和成交量视图ID
	int m_nInfoViewID;      // 股票信息视图ID
	int m_nIndicatorViewID; // 技术指标视图ID
	int m_nKLineSignalID;	// 技术信号视图ID
	int m_nKLineTimeID;		// 历史分时视图ID

protected:
	// 初始化分割窗口
	BOOL CreateSplitterWindow();

// 生成的消息映射函数
protected:
	DECLARE_MESSAGE_MAP()
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
	afx_msg void OnTimer(UINT_PTR nIDEvent);
};

