﻿// TimeLineView.cpp: CTimeLine 类的实现
//

#include "pch.h"
#include "..\framework.h"
#include "..\Stock.h"
#include "..\StockDoc.h"
#include "TimeLine.h"
#include "..\MainFrm.h"  // 添加MainFrm.h的包含声明
#include "..\UIConstants.h"  // 添加UI常量定义
#include <vector>
#include <ctime>
#include <random>
#include <functional> // 添加functional头文件，用于std::bind和std::placeholders
#include <cmath>      // 添加数学函数头文件
#include <algorithm>  // 添加算法函数头文件
#include <tchar.h>    // 添加TCHAR函数头文件
#include <wininet.h>  // 添加网络状态检查头文件


// 布局常量
const int TOP_INFO_HEIGHT = 35;        // 顶部信息区高度
const int BOTTOM_TIME_HEIGHT = 35;     // 底部时间区高度
const int LEFT_PRICE_WIDTH = 80;       // 左侧价格区宽度
const int RIGHT_PERCENT_WIDTH = 80;    // 右侧百分比区宽度
const int DIVIDER_LINE_WIDTH = 3;      // 中间分割线宽度
const int AUCTION_DIVIDER_WIDTH = 3;   // 集合竞价区分割线宽度
const int VOLUME_DIVIDER_HEIGHT = 3;   // 价格区与成交量区分割线高度

// 定时器常量
const UINT_PTR TIMER_REALTIME_UPDATE = 1001;  // 实时数据更新定时器
const UINT_PTR TIMER_REFRESH_DISPLAY = 1002;  // 界面刷新定时器


#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CTimeLine

IMPLEMENT_DYNCREATE(CTimeLine, CView)

BEGIN_MESSAGE_MAP(CTimeLine, CView)
    ON_WM_SIZE()
    ON_WM_ERASEBKGND()
    ON_WM_CREATE()
    ON_WM_KEYDOWN()
    ON_WM_DESTROY()
    ON_WM_TIMER()       // 添加定时器消息
    ON_WM_MOUSEWHEEL()  // 添加鼠标滚轮消息
    ON_WM_LBUTTONDBLCLK() // 添加鼠标左键双击消息
    ON_WM_MOUSEMOVE()   // 添加鼠标移动消息
    ON_WM_LBUTTONDOWN() // 添加鼠标左键按下消息
    ON_WM_LBUTTONUP()   // 添加鼠标左键抬起消息
END_MESSAGE_MAP()

// CTimeLine 构造/析构
// 初始化分时数据管理器
void CTimeLine::InitTimeData()
{
	// 如果有股票代码，尝试立即获取数据
	if (!m_strCode.IsEmpty())
	{
		// 获取文档
		CStockDoc* pDoc = GetDocument();
		if (pDoc)
		{
			// 尝试更新该股票的实时数据
			//pDoc->UpdateStockData(std::string(m_strCode));

			// 通知TimeInfo视图进行初始更新
			// 获取当前时间作为初始显示
			CTime currentTime = CTime::GetCurrentTime();

			// 获取当前股票数据
			int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
			if (stockIndex >= 0)
			{
				const StockData* pStockData = pDoc->GetStock(stockIndex);
				if (pStockData && !pStockData->_vecTimeLine.empty())
				{
					// 使用最后一个有效数据点
					const TLINE_DATA& lastData = pStockData->_vecTimeLine.back();

					// 更新顶部信息
					m_topInfoData.time = lastData._Time;
					m_topInfoData.date = m_strCurrentDate;
					m_topInfoData.price = lastData._Price;
					m_topInfoData.preClose = m_chartData.preClose;
					m_topInfoData.change = lastData._Price - m_chartData.preClose;
					m_topInfoData.changePercent = (lastData._Price / m_chartData.preClose - 1) * 100;
					m_topInfoData.volume = lastData._Volume;
					m_topInfoData.amount = lastData._Amount;
					m_topInfoData.isValid = TRUE;
				}
			}
		}
	}
}

CTimeLine::CTimeLine() noexcept
{
	// 初始化股票代码
	m_strCode = _T("");

    // 初始化区域控制参数
    m_bShowInfoArea = TRUE;      // 默认显示个股资讯区
    m_bShowAuctionArea = TRUE;   // 默认显示集合竞价区
    m_nInfoAreaHeight = 200;     // 默认个股资讯区高度
    
    // 初始化集合竞价数据
    ZeroMemory(&m_AucEx, sizeof(AUCEX));
    m_vecAucTime.clear();
    m_MinDate = 0;
    m_LastCode = _T("");

    // 初始化布局参数
    m_fAuctionAreaRatio = 0.15f;  // 集合竞价区域占主图表宽度的15%
    m_fVolumeAreaRatio = 0.25f;  // 成交量区域占主图表高度的25%
    
    // 初始化状态标志
    m_bNeedRecalcData = true;
    m_bShowLimitPriceCoordinates = FALSE;
    m_bShowCrossCursor = FALSE;
    m_bCursorDataValid = FALSE;
    
    // 初始化光标位置
    m_ptCrossCursor.x = 0;
    m_ptCrossCursor.y = 0;
    
    // 初始化当前日期
    CTime currentTime = CTime::GetCurrentTime();
    m_strCurrentDate = currentTime.Format(_T("%Y-%m-%d"));
    
    // 初始化最大成交量
    m_maxVolume = 0.0;
    
    // 初始化定时器状态
    m_bLastTradingTimeStatus = IsTradingTime();
}

CTimeLine::~CTimeLine()
{

}

BOOL CTimeLine::PreCreateWindow(CREATESTRUCT& cs)
{
	// 修改窗口类或样式实现无边框
	cs.style &= ~(WS_BORDER | WS_DLGFRAME | WS_THICKFRAME | WS_CAPTION);
	cs.dwExStyle &= ~(WS_EX_DLGMODALFRAME | WS_EX_CLIENTEDGE | WS_EX_WINDOWEDGE);
	
	// 添加WS_EX_COMPOSITED扩展样式，启用窗口合成，减少闪烁
	cs.dwExStyle |= WS_EX_COMPOSITED;

	return CView::PreCreateWindow(cs);
}

// 拦截擦除背景消息，返回TRUE表示已处理（不执行默认的擦除操作）
BOOL CTimeLine::OnEraseBkgnd(CDC* pDC)
{
	// 使用双缓冲绘图后，这里不需要擦除背景
	// 直接返回TRUE表示背景已被擦除，实际擦除在OnDraw中处理
	return TRUE;
}

// CTimeLine 诊断
CStockDoc* CTimeLine::GetDocument() const // 非调试版本是内联的
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 初始化视图
void CTimeLine::OnInitialUpdate()
{
    // 调用基类方法
    CView::OnInitialUpdate();
    
    // 初始化数据标志
    m_bNeedRecalcData = true;
    m_chartData.dataValid = false;
    
    // 窗口已创建，现在可以安全地加载数据
    CStockDoc* pDoc = GetDocument();
    if (!pDoc)
        return;
    
    try {
        // 获取当前股票代码
        std::string stockCode = pDoc->GetCurrentStock();
        if (!stockCode.empty() && m_strCode == stockCode.c_str())
        {
            // 获取当天日期
            CTime now = CTime::GetCurrentTime();
            CString strDate;
            strDate.Format(_T("%04d-%02d-%02d"), now.GetYear(), now.GetMonth(), now.GetDay());
            
            // 加载最新数据
            TRACE(_T("OnInitialUpdate: 加载股票 %hs 的数据\n"), stockCode.c_str());
            LoadTimeData(stockCode, std::string(strDate));
        }
    }
    catch (const std::exception& e) {
        TRACE(_T("OnInitialUpdate异常: %hs\n"), e.what());
    }
    
    // 启动定时器进行实时数据更新
    // 根据是否为交易时段来确定更新频率
    if (IsTradingTime())
    {
        // 交易时段：更频繁的更新
        SetTimer(TIMER_REALTIME_UPDATE, 2000, NULL);  // 每2秒检查一次数据更新
        SetTimer(TIMER_REFRESH_DISPLAY, 500, NULL);   // 每0.5秒刷新一次界面
    }
    else
    {
        // 非交易时段：较慢的更新频率
        SetTimer(TIMER_REALTIME_UPDATE, 10000, NULL); // 每10秒检查一次数据更新
        SetTimer(TIMER_REFRESH_DISPLAY, 5000, NULL);  // 每5秒刷新一次界面
    }
}

// 加载分时数据
BOOL CTimeLine::LoadTimeData(const std::string& stockCode, const std::string& date)
{
	// 参数验证
	if (stockCode.empty() || date.empty())
	{
		TRACE(_T("LoadTimeData: 参数无效 - 股票代码或日期为空\n"));
		return FALSE;
	}

	CString cstrStockCode(stockCode.c_str());
	CString cstrDate(date.c_str());

	TRACE(_T("加载分时数据 - 股票代码: %s, 日期: %s\n"), cstrStockCode, cstrDate);

	// 获取文档
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
	{
		TRACE(_T("LoadTimeData: 无法获取文档指针\n"));
		return FALSE;
	}

	// 设置日期格式
	m_strCurrentDate = cstrDate;

	try {
		// 获取股票索引
		int stockIndex = pDoc->GetStockIndex(stockCode);
		if (stockIndex < 0)
		{
			TRACE(_T("找不到股票: %s\n"), cstrStockCode);
			return FALSE;
		}

		// 获取股票数据
		const StockData* pStockData = pDoc->GetStock(stockIndex);
		if (!pStockData)
		{
			TRACE(_T("无法获取股票数据: %s\n"), cstrStockCode);
			return FALSE;
		}

		// 检查分时数据是否有效
		bool needsUpdate = pStockData->_vecTimeLine.empty();

		if (needsUpdate && ::IsWindow(m_hWnd))
		{
			TRACE(_T("股票分时数据为空，尝试从网络获取: %s\n"), cstrStockCode);

			// 网络状态检查
			if (!IsNetworkAvailable())
			{
				TRACE(_T("网络不可用，尝试加载缓存数据\n"));
				return LoadCachedData(stockCode, date);
			}

			// 重试机制 - 最多重试3次
			const int MAX_RETRY = 3;
			bool updateSuccess = false;
			
			for (int retry = 0; retry < MAX_RETRY; retry++)
			{
				TRACE(_T("尝试下载数据，第%d次\n"), retry + 1);
				
				updateSuccess = pDoc->DownloadStockTimelineData(stockCode);
				
				if (updateSuccess)
				{
					TRACE(_T("数据下载成功\n"));
					break;
				}
				
				// 递增延迟重试
				if (retry < MAX_RETRY - 1)
				{
					DWORD delay = 1000 * (retry + 1); // 1秒、2秒、3秒
					TRACE(_T("下载失败，%d毫秒后重试\n"), delay);
					Sleep(delay);
				}
			}

			if (!updateSuccess)
			{
				TRACE(_T("网络数据下载失败，尝试加载缓存数据\n"));
				return LoadCachedData(stockCode, date);
			}

			// 重新获取数据指针，验证数据有效性
			pStockData = pDoc->GetStock(stockIndex);
			if (!pStockData)
			{
				TRACE(_T("更新后无法获取股票数据指针\n"));
				return FALSE;
			}
			
			if (pStockData->_vecTimeLine.empty())
			{
				TRACE(_T("更新后分时数据仍为空，尝试加载缓存数据\n"));
				return LoadCachedData(stockCode, date);
			}
			
			// 数据有效性验证
			bool dataValid = true;
			for (const auto& timeData : pStockData->_vecTimeLine)
			{
				if (timeData._Price <= 0 || timeData._Time < 0)
				{
					dataValid = false;
					break;
				}
			}
			
			if (!dataValid)
			{
				TRACE(_T("下载的数据无效，尝试加载缓存数据\n"));
				return LoadCachedData(stockCode, date);
			}
		}
		else if (needsUpdate)
		{
			// 窗口尚未创建，暂不更新数据
			TRACE(_T("窗口尚未创建，暂不更新分时数据\n"));
			return TRUE; // 返回成功，稍后会有其他机会更新数据
		}

		// 标记需要重新计算绘图数据
		m_bNeedRecalcData = true;
		m_chartData.dataValid = false;

		TRACE(_T("成功加载分时数据 - 数据点数: %d\n"), (int)pStockData->_vecTimeLine.size());
		return TRUE;
	}
	catch (const std::exception& e) {
		TRACE(_T("LoadTimeData异常: %hs\n"), e.what());
		// 异常情况下尝试加载缓存数据
		return LoadCachedData(stockCode, date);
	}
	catch (...)
	{
		TRACE(_T("LoadTimeData发生未知异常\n"));
		return LoadCachedData(stockCode, date);
	}
}

// 创建窗口事件
int CTimeLine::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CView::OnCreate(lpCreateStruct) == -1)
		return -1;

	return 0;
}

// 更新视图布局
void CTimeLine::UpdateLayout()
{
    // 获取客户区尺寸
    GetClientRect(&m_rcClient);
    
    // 设置个股资讯区
    if (m_bShowInfoArea)
    {
        // 计算分时行情区和个股资讯区
        m_rcTimelineArea = m_rcClient;
        m_rcTimelineArea.bottom = m_rcClient.bottom - m_nInfoAreaHeight;
        
        m_rcInfoArea = m_rcClient;
        m_rcInfoArea.top = m_rcTimelineArea.bottom + 1; // 考虑分割线宽度
    }
    else
    {
        // 如果不显示个股资讯区，分时行情区占满整个客户区
        m_rcTimelineArea = m_rcClient;
        m_rcInfoArea.SetRectEmpty();
    }
    
    // 计算分时行情区子区域
    // 主图表区域
    m_rcMainChart = m_rcTimelineArea;
    m_rcMainChart.top += TOP_INFO_HEIGHT + 1; // 考虑分割线宽度
    m_rcMainChart.bottom -= BOTTOM_TIME_HEIGHT + 1; // 考虑分割线宽度
    m_rcMainChart.left += LEFT_PRICE_WIDTH + 1; // 考虑分割线宽度
    m_rcMainChart.right -= RIGHT_PERCENT_WIDTH + 1; // 考虑分割线宽度
    
    // 确保个股资讯区的左右边界与分时区的左右边界对齐
    if (m_bShowInfoArea && !m_rcInfoArea.IsRectEmpty())
    {
        m_rcInfoArea.left = m_rcTimelineArea.left;
        m_rcInfoArea.right = m_rcTimelineArea.right;
    }
    
    // 计算集合竞价区域和价格区域
    if (m_bShowAuctionArea)
    {
        // 使用双精度计算集合竞价区域宽度，避免精度损失
        double mainChartWidth = static_cast<double>(m_rcMainChart.Width());
        double auctionWidth = mainChartWidth * m_fAuctionAreaRatio;
        
        // 集合竞价区域位于主图表左侧，包括价格区和成交量区
        m_rcAuctionArea = m_rcMainChart;
        m_rcAuctionArea.right = m_rcAuctionArea.left + static_cast<int>(auctionWidth + 0.5); // 四舍五入
        
        // 价格区域占据主图表剩余部分，考虑集合竞价分割线宽度
        m_rcPriceArea = m_rcMainChart;
        m_rcPriceArea.left = m_rcAuctionArea.right + AUCTION_DIVIDER_WIDTH;
    }
    else
    {
        // 如果不显示集合竞价区，价格区域占据整个主图表宽度
        m_rcPriceArea = m_rcMainChart;
        m_rcAuctionArea.SetRectEmpty();
    }
    
    // 计算主图表子区域 - 价格区域和成交量区域
    
    // 使用双精度计算，确保高度分配的精确性
    double mainChartHeight = static_cast<double>(m_rcMainChart.Height());
    
    // 成交量区域与价格区域之间的分割线居中放置
    int dividerHeight = VOLUME_DIVIDER_HEIGHT;
    
    // 计算可用空间，排除分割线占用的空间
    double availableHeight = mainChartHeight - dividerHeight;
    
    // 计算成交量区域和价格区域的理想高度
    double volumeHeightRatio = m_fVolumeAreaRatio;
    double volumeHeightDouble = availableHeight * volumeHeightRatio;
    double priceHeightDouble = availableHeight * (1.0 - volumeHeightRatio);
    
    // 使用四舍五入确保整数转换的精确性
    int volumeHeight = static_cast<int>(volumeHeightDouble + 0.5);
    int priceHeight = static_cast<int>(priceHeightDouble + 0.5);
    
    // 确保两个区域高度之和等于可用高度
    if (priceHeight + volumeHeight != static_cast<int>(availableHeight)) {
        // 优先保证价格区域准确，调整成交量区域高度
        volumeHeight = static_cast<int>(availableHeight) - priceHeight;
    }
    
    // 计算价格区域和成交量区域的位置
    m_rcPriceArea.bottom = m_rcMainChart.top + priceHeight;
    
    // 成交量区域的顶部位置是价格区域底部加上分割线高度
    m_rcVolumeArea = m_rcMainChart;
    m_rcVolumeArea.top = m_rcPriceArea.bottom + dividerHeight;
    
    // 如果显示了集合竞价区，需要调整集合竞价区的价格部分和成交量部分
    if (m_bShowAuctionArea)
    {
        // 定义竞价区价格和成交量部分的分割矩形
        CRect rcAuctionPrice = m_rcAuctionArea;
        rcAuctionPrice.bottom = m_rcPriceArea.bottom;
        
        CRect rcAuctionVolume = m_rcAuctionArea;
        rcAuctionVolume.top = m_rcVolumeArea.top;
        
        // 更新成交量区域左边界，不包含集合竞价区，考虑分割线宽度
        m_rcVolumeArea.left = m_rcPriceArea.left;
    }
    
    // 顶部信息区域
    m_rcTopInfo = m_rcTimelineArea;
    m_rcTopInfo.bottom = m_rcTimelineArea.top + TOP_INFO_HEIGHT;
    
    // 底部时间坐标区域
    m_rcBottomTime = m_rcTimelineArea;
    m_rcBottomTime.top = m_rcTimelineArea.bottom - BOTTOM_TIME_HEIGHT;
    
    // 左侧价格坐标区域
    m_rcLeftPrice = m_rcTimelineArea;
    m_rcLeftPrice.right = m_rcTimelineArea.left + LEFT_PRICE_WIDTH;
    m_rcLeftPrice.top = m_rcMainChart.top;
    m_rcLeftPrice.bottom = m_rcMainChart.bottom;
    
    // 右侧百分比坐标区域
    m_rcRightPercent = m_rcTimelineArea;
    m_rcRightPercent.left = m_rcTimelineArea.right - RIGHT_PERCENT_WIDTH;
    m_rcRightPercent.top = m_rcMainChart.top;
    m_rcRightPercent.bottom = m_rcMainChart.bottom;
}


// CTimeLine 绘图
void CTimeLine::OnDraw(CDC* pDC)
{
    // 获取窗口尺寸
    CRect rcClient;
    GetClientRect(rcClient);

    // 如果窗口太小，不需要绘制
    if (rcClient.Width() < 10 || rcClient.Height() < 10)
        return;
    
    // 确保数据已准备好，在绘制前调用数据准备方法
    if (m_bNeedRecalcData || !m_chartData.dataValid)
    {
        PrepareChartData();
    }
    
    // 使用RAII模式确保资源安全管理
    CDC memDC;
    CBitmap memBitmap;
    CBitmap* pOldBitmap = nullptr;
    
    try {
        // 创建内存设备上下文和兼容位图用于双缓冲绘图
        if (!memDC.CreateCompatibleDC(pDC))
        {
            TRACE(_T("OnDraw: 无法创建内存设备上下文\n"));
            return;
        }
        
        if (!memBitmap.CreateCompatibleBitmap(pDC, rcClient.Width(), rcClient.Height()))
        {
            TRACE(_T("OnDraw: 无法创建兼容位图\n"));
            memDC.DeleteDC();
            return;
        }
        
        pOldBitmap = memDC.SelectObject(&memBitmap);
        if (!pOldBitmap)
        {
            TRACE(_T("OnDraw: 无法选择位图对象\n"));
            memBitmap.DeleteObject();
            memDC.DeleteDC();
            return;
        }

        // 使用SetBkMode优化文本绘制，避免重复设置
        memDC.SetBkMode(TRANSPARENT);

        // 保存原始映射模式
        int oldMapMode = memDC.SetMapMode(MM_TEXT);

        // 填充背景 - 一次性填充整个背景
        memDC.FillSolidRect(rcClient, RGB(0, 0, 0));
            
        // 使用区域剪裁(ClipRect)优化绘图 - 只绘制可见区域
        if (!m_rcTimelineArea.IsRectEmpty())
        {
            memDC.IntersectClipRect(m_rcTimelineArea);
            
            // 分区域绘制 - 减少不必要的重绘，使用异常保护
            try {
                DrawTimelineArea(&memDC);
            }
            catch (const std::exception& e) {
                TRACE(_T("DrawTimelineArea异常: %hs\n"), e.what());
            }
            catch (...) {
                TRACE(_T("DrawTimelineArea发生未知异常\n"));
            }
        }
        
        // 重设剪裁区域用于绘制个股资讯区（如果可见）
        memDC.SelectClipRgn(NULL);
        if (m_bShowInfoArea && !m_rcInfoArea.IsRectEmpty())
        {
            memDC.IntersectClipRect(m_rcInfoArea);
            try {
                DrawInfoArea(&memDC);
            }
            catch (const std::exception& e) {
                TRACE(_T("DrawInfoArea异常: %hs\n"), e.what());
            }
            catch (...) {
                TRACE(_T("DrawInfoArea发生未知异常\n"));
            }
        }
            
        // 绘制十字光标 - 在所有图形元素绘制完成后绘制
        if (m_bShowCrossCursor)
        {
            memDC.SelectClipRgn(NULL);
            try {
                DrawCrossCursor(&memDC);
            }
            catch (const std::exception& e) {
                TRACE(_T("DrawCrossCursor异常: %hs\n"), e.what());
            }
            catch (...) {
                TRACE(_T("DrawCrossCursor发生未知异常\n"));
            }
        }
        
        // 恢复映射模式和剪裁区域
        memDC.SelectClipRgn(NULL);
        memDC.SetMapMode(oldMapMode);

        // 将内存设备上下文的内容绘制到屏幕
        pDC->BitBlt(0, 0, rcClient.Width(), rcClient.Height(), &memDC, 0, 0, SRCCOPY);
    }
    catch (const std::exception& e) {
        TRACE(_T("OnDraw异常: %hs\n"), e.what());
    }
    catch (...) {
        TRACE(_T("OnDraw发生未知异常\n"));
    }

    // 确保资源总是被正确释放（RAII清理）
    if (pOldBitmap)
    {
        memDC.SelectObject(pOldBitmap);
    }
    
    if (memBitmap.GetSafeHandle())
    {
        memBitmap.DeleteObject();
    }
    
    if (memDC.GetSafeHdc())
    {
        memDC.DeleteDC();
    }
}

// 绘制分时行情区 - 优化绘制顺序
void CTimeLine::DrawTimelineArea(CDC* pDC)
{
    // 使用更高效的绘制顺序，从底向上绘制各层
    
    // 1. 绘制集合竞价区域的背景 - 这样后续的网格线绘制不会被覆盖
    if (m_bShowAuctionArea)
    {
        // 只绘制背景部分，水平分割线由DrawMainChartBackground绘制
        DrawAuctionAreaBackground(pDC);
    }
    
    // 2. 再绘制主图表背景和网格线 - 包括集合竞价区的水平分割线
   DrawMainChartBackground(pDC);
    
    // 3. 绘制集合竞价区域的边界线和垂直分割线
    if (m_bShowAuctionArea)
    {
        DrawAuctionAreaBorders(pDC);
    }
    
    // 4. 绘制主图数据
    DrawMainChartData(pDC);
   
    // 6. 绘制坐标系和边框 - 这些是顶层UI元素
    DrawTopInfo(pDC);
    DrawBottomTime(pDC);
    DrawLeftPrice(pDC);
    DrawRightPercent(pDC);
    
    // 7. 绘制边界线 - 确保边界线覆盖在其他元素之上
    DrawBorders(pDC);
}

// **：绘制集合竞价区域背景
void CTimeLine::DrawAuctionAreaBackground(CDC* pDC)
{
    if (m_rcAuctionArea.IsRectEmpty())
        return;
    
    // 只填充集合竞价区域背景
    CBrush brushBg(RGB(30, 30, 30)); // 深蓝黑色背景，与主图表区分
    pDC->FillRect(&m_rcAuctionArea, &brushBg);
}

// **：绘制集合竞价区域边界线和垂直分割线
void CTimeLine::DrawAuctionAreaBorders(CDC* pDC)
{
    if (m_rcAuctionArea.IsRectEmpty())
        return;
    
    // 绘制集合竞价区域边框
    CPen penBorder(PS_SOLID, 3, RGB(128, 0, 0));
    CPen* pOldPen = pDC->SelectObject(&penBorder);
    
    // 绘制竞价区和价格区的分隔线（右侧垂直边界）
    pDC->MoveTo(m_rcAuctionArea.right, m_rcAuctionArea.top);
    pDC->LineTo(m_rcAuctionArea.right, m_rcAuctionArea.bottom); 

    
    // 绘制区域分隔线
    CPen penDivider(PS_SOLID, 1, RGB(128, 0, 0));
    pDC->SelectObject(&penDivider);

    // 绘制集合竞价区中间垂直分割线
    int middleX = m_rcAuctionArea.left + m_rcAuctionArea.Width() / 2;
    
    // 集合竞价区价格部分绘制垂直中线
    pDC->MoveTo(middleX, m_rcAuctionArea.top);
    pDC->LineTo(middleX, m_rcVolumeArea.top);
    
    // 集合竞价区成交量部分绘制垂直中线
    pDC->MoveTo(middleX, m_rcVolumeArea.top);
    pDC->LineTo(middleX, m_rcAuctionArea.bottom);
    
    pDC->SelectObject(pOldPen);
}

// **：绘制主图表背景和网格线
void CTimeLine::DrawMainChartBackground(CDC* pDC)
{
     // 绘制网格线
    CPen penGrid(PS_DOT, 1, RGB(128, 0, 0));
    CPen penHorizontal(PS_SOLID, 1, RGB(128, 0, 0));
    // 修改零轴线颜色为亮红色
    CPen penThickHorizontal(PS_SOLID, 3, RGB(128, 0, 0));
    CPen* pOldPen = pDC->SelectObject(&penHorizontal);
    
    // 计算价格区域网格线间距 - 使用精确的浮点计算
    double priceAreaHeight = static_cast<double>(m_rcPriceArea.Height());
    double priceYStep = priceAreaHeight / 16.0; // 16等分，画15条线
    
    // 绘制价格区域的水平网格线
    for (int i = 1; i <= 15; i++) {
        // 使用四舍五入确保位置精确
        int y = m_rcPriceArea.top + static_cast<int>(i * priceYStep + 0.5);
        
        // 确保不超出价格区域边界
        if (y >= m_rcPriceArea.bottom) {
            y = m_rcPriceArea.bottom - 1;
        }
        
        // 第8根线使用加粗实线（零轴线）
        if (i == 8) {
            pDC->SelectObject(&penThickHorizontal);
        } else {
            pDC->SelectObject(&penHorizontal);
        }
        
        // 水平网格线从主图表左边界开始，横跨整个主图表区域（包括集合竞价区）
        pDC->MoveTo(m_rcMainChart.left, y);
        pDC->LineTo(m_rcMainChart.right, y);
    }
    
    // 计算成交量区域水平网格线的精确位置
    double volumeAreaHeight = static_cast<double>(m_rcVolumeArea.Height());
    double volumeYStep = volumeAreaHeight / 5.0; // 5等分，画4条线
    
    // 绘制成交量区域的水平网格线
    for (int i = 1; i <= 4; i++) {
        // 使用四舍五入确保位置精确
        int y = m_rcVolumeArea.top + static_cast<int>(i * volumeYStep + 0.5);
        
        // 确保不超出成交量区域边界
        if (y >= m_rcVolumeArea.bottom) {
            y = m_rcVolumeArea.bottom - 1;
        }
        
        pDC->SelectObject(&penHorizontal);
        pDC->MoveTo(m_rcMainChart.left, y);
        pDC->LineTo(m_rcMainChart.right, y);
    }
    
    // 在价格区域绘制垂直网格线
    if (!m_rcPriceArea.IsRectEmpty())
    {
        // 使用双精度计算垂直网格线间距
        double priceAreaWidth = static_cast<double>(m_rcPriceArea.Width());
        double xStep = priceAreaWidth / 8.0;
        
        // 创建实线画笔(用于第2、4、6根垂直线)
        CPen penSolid(PS_SOLID, 1, RGB(128, 0, 0));
        // 创建加粗实线画笔(用于第4根垂直线)，与水平零轴线颜色一致
        CPen penThickSolid(PS_SOLID, 3, RGB(128, 0, 0));
        
        for (int i = 1; i < 8; i++)
        {
            // 使用四舍五入确保位置精确
            int x = m_rcPriceArea.left + static_cast<int>(i * xStep + 0.5);
            
            // 确保不超出价格区域边界
            if (x >= m_rcPriceArea.right) {
                x = m_rcPriceArea.right - 1;
            }
            
            // 根据线的位置选择不同的画笔
            if (i == 2 || i == 6) {
                // 第2、6根线用实线
                pDC->SelectObject(&penSolid);
            } else if (i == 4) {
                // 第4根线用加粗实线，颜色为亮红色
                pDC->SelectObject(&penThickSolid);
            } else {
                // 其他线用虚线
                pDC->SelectObject(&penGrid);
            }
            
            // 价格区域绘制垂直网格线
            pDC->MoveTo(x, m_rcPriceArea.top);
            pDC->LineTo(x, m_rcPriceArea.bottom);
            
            // 成交量区域绘制垂直网格线
            pDC->MoveTo(x, m_rcVolumeArea.top);
            pDC->LineTo(x, m_rcVolumeArea.bottom);
        }
    }
    
    // 如果显示集合竞价区，绘制竞价区水平网格线
    if (m_bShowAuctionArea && !m_rcAuctionArea.IsRectEmpty())
    {
        // 获取集合竞价区中间位置
        int middleX = m_rcAuctionArea.left + m_rcAuctionArea.Width() / 2;
        
        // 价格区域部分 - 使用与价格区域相同的计算方法
        for (int i = 1; i <= 15; i++) {
            // 使用四舍五入确保位置精确
            int y = m_rcPriceArea.top + static_cast<int>(i * priceYStep + 0.5);
            
            // 确保不超出价格区域边界
            if (y >= m_rcPriceArea.bottom) {
                y = m_rcPriceArea.bottom - 1;
            }
            
            // 第8根线使用加粗实线（零轴），确保与主图表区域零轴颜色一致
            if (i == 8) {
                pDC->SelectObject(&penThickHorizontal);
            } else {
                pDC->SelectObject(&penHorizontal);
            }
            
            // 仅绘制集合竞价区域范围内的水平线 - 分左右两半绘制以确保和垂直分割线交叉正确
            pDC->MoveTo(m_rcAuctionArea.left, y);
            pDC->LineTo(middleX, y);  // 左半部分
            pDC->MoveTo(middleX, y);
            pDC->LineTo(m_rcAuctionArea.right, y);  // 右半部分
        }
        
        // 成交量区域部分 - 使用与成交量区域相同的计算方法
        for (int i = 1; i <= 4; i++) {
            // 使用四舍五入确保位置精确
            int y = m_rcVolumeArea.top + static_cast<int>(i * volumeYStep + 0.5);
            
            // 确保不超出成交量区域边界
            if (y >= m_rcVolumeArea.bottom) {
                y = m_rcVolumeArea.bottom - 1;
            }
            
            pDC->SelectObject(&penHorizontal);
            
            // 仅绘制集合竞价区域范围内的水平线 - 分左右两半绘制以确保和垂直分割线交叉正确
            pDC->MoveTo(m_rcAuctionArea.left, y);
            pDC->LineTo(middleX, y);  // 左半部分
            pDC->MoveTo(middleX, y);
            pDC->LineTo(m_rcAuctionArea.right, y);  // 右半部分
        }
    }

    pDC->SelectObject(pOldPen);
}


// 新增：绘制所有边界线
void CTimeLine::DrawBorders(CDC* pDC)
{
	// 绘制深红色边框线
	CPen penBorder(PS_SOLID, 3, RGB(128, 0, 0)); // 深红色
	CPen* pOldPen = pDC->SelectObject(&penBorder);

	// 绘制主图表矩形边框
	pDC->MoveTo(m_rcMainChart.left, m_rcMainChart.top);
	pDC->LineTo(m_rcMainChart.right, m_rcMainChart.top);
	pDC->LineTo(m_rcMainChart.right, m_rcMainChart.bottom);
	pDC->LineTo(m_rcMainChart.left, m_rcMainChart.bottom);
	pDC->LineTo(m_rcMainChart.left, m_rcMainChart.top);

	// 绘制成交量区分割线 - 使用与updateLayout方法中计算一致的位置
	CPen penVolumeDivider(PS_SOLID, VOLUME_DIVIDER_HEIGHT, RGB(128, 0, 0));
	pDC->SelectObject(&penVolumeDivider);

	// 成交量分隔线需要绘制在价格区底部和成交量区顶部之间
	int dividerY = (m_rcPriceArea.bottom + m_rcVolumeArea.top) / 2;
	pDC->MoveTo(m_rcMainChart.left, dividerY);
	pDC->LineTo(m_rcMainChart.right, dividerY);

	// 绘制个股资讯区边框 - 仅当个股资讯区可见时绘制
	if (m_bShowInfoArea && !m_rcInfoArea.IsRectEmpty())
	{
		// 使用深红色边框线
		pDC->SelectObject(&penBorder);
		
		// 绘制个股资讯区矩形边框
		pDC->MoveTo(m_rcInfoArea.left, m_rcInfoArea.top);
		pDC->LineTo(m_rcInfoArea.right, m_rcInfoArea.top);
		pDC->LineTo(m_rcInfoArea.right, m_rcInfoArea.bottom);
		pDC->LineTo(m_rcInfoArea.left, m_rcInfoArea.bottom);
		pDC->LineTo(m_rcInfoArea.left, m_rcInfoArea.top);
	}

	pDC->SelectObject(pOldPen);
}

// 新增：绘制主图数据
void CTimeLine::DrawMainChartData(CDC* pDC)
{
    // 确保数据已准备好
    if (!m_chartData.dataValid) {
        // 如果数据无效，尝试重新准备数据
        PrepareChartData();
        
        // 如果数据仍然无效，显示无数据提示并返回
        if (!m_chartData.dataValid) {
            // 绘制无数据提示
            CFont font;
            font.CreateFont(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
                            ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
                            DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
            
            CFont* pOldFont = pDC->SelectObject(&font);
            pDC->SetTextColor(RGB(200, 200, 200));
            
            CString strNoData = _T("暂无分时数据");
            CSize textSize = pDC->GetTextExtent(strNoData);
            
            // 在主图表区域中央显示
            int x = m_rcMainChart.left + (m_rcMainChart.Width() - textSize.cx) / 2;
            int y = m_rcMainChart.top + (m_rcMainChart.Height() - textSize.cy) / 2;
            
            pDC->TextOut(x, y, strNoData);
            pDC->SelectObject(pOldFont);
            return;
        }
    }
    
    // 先绘制均价线，让价格线在上层
    if (!m_chartData.avgPricePoints.empty()) {
        // 创建均价线的画笔 - 使用更明显的色彩和线型
        CPen penAvg(PS_SOLID, 1, UIConstants::COLOR_YELLOW); // 黄色均价线，更易于区分
        CPen* pOldPen = pDC->SelectObject(&penAvg);
        
        // 绘制均价线
        pDC->Polyline(m_chartData.avgPricePoints.data(), (int)m_chartData.avgPricePoints.size());
        
        // 恢复原画笔
        pDC->SelectObject(pOldPen);
    }
    
    // 绘制价格线
    if (!m_chartData.pricePoints.empty()) {
        // 创建价格线的画笔
        CPen penPrice(PS_SOLID, 1, UIConstants::COLOR_WHITE); // 白色价格线
        CPen* pOldPen = pDC->SelectObject(&penPrice);
        
        // 绘制价格线
        pDC->Polyline(m_chartData.pricePoints.data(), (int)m_chartData.pricePoints.size());
        
        // 恢复原画笔
        pDC->SelectObject(pOldPen);
    }
    
    // 绘制成交量直线
    for (size_t i = 0; i < m_chartData.volumeLines.size(); i++) {
        // 创建成交量线的画笔
        CPen pen(PS_SOLID, 1, m_chartData.volumeColors[i]);
        CPen* pOldPen = pDC->SelectObject(&pen);
        
        // 绘制垂直线
        const auto& line = m_chartData.volumeLines[i];
        pDC->MoveTo(line.first);
        pDC->LineTo(line.second);
        
        // 恢复原画笔
        pDC->SelectObject(pOldPen);
    }
    
    // 绘制集合竞价区数据
    if (m_bShowAuctionArea && !m_chartData.auctionPricePoints.empty()) {
        // 绘制价格折线
        CPen penPrice(PS_SOLID, 2, RGB(255, 255, 255)); // 白色价格线
        CPen* pOldPen = pDC->SelectObject(&penPrice);
        
        // 绘制集合竞价价格线
        pDC->Polyline(m_chartData.auctionPricePoints.data(), (int)m_chartData.auctionPricePoints.size());
        
        // 绘制集合竞价成交量直线
        CPen penVolume(PS_SOLID, 1, RGB(0, 200, 200)); // 蓝绿色成交量线
        pDC->SelectObject(&penVolume);
        
        // 绘制每条成交量直线
        for (const auto& line : m_chartData.auctionVolumeLines) {
            pDC->MoveTo(line.first);
            pDC->LineTo(line.second);
        }
        
        // 恢复原画笔
        pDC->SelectObject(pOldPen);
    }
    
    // 绘制集合竞价区域数据（使用CSV文件数据）
    if (m_bShowAuctionArea) {
        DrawAucZone(pDC);
    }
    
    // 更新顶部信息区显示的数据
    if (!m_strCode.IsEmpty())
    {
        // 获取文档
        CStockDoc* pDoc = GetDocument();
        if (pDoc)
        {
            // 获取股票索引
            int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
            if (stockIndex >= 0)
            {
                // 获取股票数据
                const StockData* pStockData = pDoc->GetStock(stockIndex);
                if (pStockData && !pStockData->_vecTimeLine.empty())
                {
                    // 使用最后一个有效数据点
                    const TLINE_DATA& lastData = pStockData->_vecTimeLine.back();
                    
                    // 更新顶部信息
                    m_topInfoData.time = lastData._Time;
                    m_topInfoData.date = m_strCurrentDate;
                    m_topInfoData.price = lastData._Price;
                    m_topInfoData.preClose = m_chartData.preClose;
                    m_topInfoData.change = lastData._Price - m_chartData.preClose;
                    m_topInfoData.changePercent = (lastData._Price / m_chartData.preClose - 1) * 100;
                    m_topInfoData.volume = lastData._Volume;
                    m_topInfoData.amount = lastData._Amount;
                    m_topInfoData.isValid = TRUE;
                }
            }
        }
    }
}


// 绘制个股资讯区
void CTimeLine::DrawInfoArea(CDC* pDC)
{
    // 绘制背景
    pDC->FillSolidRect(&m_rcInfoArea, RGB(0, 0, 0));
    
    // 绘制标题
    CFont titleFont;
    titleFont.CreateFont(18, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
                         ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
                         DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
    
    CFont* pOldFont = pDC->SelectObject(&titleFont);
    pDC->SetTextColor(RGB(220, 220, 220));
    pDC->SetBkMode(TRANSPARENT);
    
    CString strTitle = _T("个股资讯");
    pDC->TextOut(m_rcInfoArea.left + 10, m_rcInfoArea.top + 5, strTitle);
    
    pDC->SelectObject(pOldFont);
    
    // 绘制个股资讯区边框 - 确保与分时区边框垂直对齐
    CPen penBorder(PS_SOLID, 3, RGB(128, 0, 0)); // 深红色
    CPen* pOldPen = pDC->SelectObject(&penBorder);
    
    // 绘制个股资讯区矩形边框
    pDC->MoveTo(m_rcInfoArea.left, m_rcInfoArea.top);
    pDC->LineTo(m_rcInfoArea.right, m_rcInfoArea.top);
    pDC->LineTo(m_rcInfoArea.right, m_rcInfoArea.bottom);
    pDC->LineTo(m_rcInfoArea.left, m_rcInfoArea.bottom);
    pDC->LineTo(m_rcInfoArea.left, m_rcInfoArea.top);
    
    pDC->SelectObject(pOldPen);
}

// 绘制顶部信息
void CTimeLine::DrawTopInfo(CDC* pDC)
{
    // 绘制背景
    pDC->FillSolidRect(&m_rcTopInfo, RGB(0, 0, 0));
    
    // 绘制信息文本
    CFont infoFont;
    infoFont.CreateFont(22, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
                        ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
                        DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
    
    CFont* pOldFont = pDC->SelectObject(&infoFont);
    
    // 定义颜色
    COLORREF textColor = RGB(255, 255, 255); // 白色文本
    COLORREF upColor = RGB(255, 50, 50);     // 上涨红色
    COLORREF downColor = RGB(0, 230, 0);     // 下跌绿色
    
    // 如果有有效的鼠标位置数据，显示对应的信息
        if (m_topInfoData.isValid)
        {
        // 准备数据格式化
        CString stockStr, dateStr, timeStr, priceStr, percentStr, volumeStr, amountStr;
            
            // 股票代码
        stockStr = m_strCode;
            
        // 日期格式化
        dateStr = m_topInfoData.date;
        
        // 时间格式化
            int hour = m_topInfoData.time / 100;
            int minute = m_topInfoData.time % 100;
            timeStr.Format(_T("%02d:%02d"), hour, minute);
            
        // 价格格式化
            priceStr.Format(_T("%.2f"), m_topInfoData.price);
            
        // 涨跌幅格式化
        if (m_topInfoData.changePercent > 0)
            percentStr.Format(_T("+%.2f%%"), m_topInfoData.changePercent);
        else if (m_topInfoData.changePercent < 0)
            percentStr.Format(_T("%.2f%%"), m_topInfoData.changePercent);
        else
            percentStr = _T("0.00%");
        
        // 成交量格式化（使用K、M等后缀表示单位）
        if (m_topInfoData.volume >= 100000000) // 亿
            volumeStr.Format(_T("%.2f亿"), m_topInfoData.volume / 100000000.0);
        else if (m_topInfoData.volume >= 10000) // 万
                volumeStr.Format(_T("%.2f万"), m_topInfoData.volume / 10000.0);
            else
                volumeStr.Format(_T("%.0f"), m_topInfoData.volume);
        
        // 成交额格式化（使用K、M等后缀表示单位）
        if (m_topInfoData.amount >= 100000000) // 亿
                amountStr.Format(_T("%.2f亿"), m_topInfoData.amount / 100000000.0);
        else if (m_topInfoData.amount >= 10000) // 万
                amountStr.Format(_T("%.2f万"), m_topInfoData.amount / 10000.0);
            else
                amountStr.Format(_T("%.0f"), m_topInfoData.amount);
        
        // 计算每个项目占据的宽度（等宽分布）
        int itemWidth = m_rcMainChart.Width() / 8;
        
        // 1. 绘制股票代码 - 靠左对齐
        pDC->SetTextColor(textColor);
            CRect rcCode(m_rcMainChart.left, m_rcTopInfo.top + 8, 
                         m_rcMainChart.left + itemWidth, m_rcTopInfo.bottom);
        pDC->DrawText(stockStr, rcCode, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
            
        // 2. 绘制日期
            pDC->SetTextColor(RGB(255, 255, 0)); // 日期为黄色
            CRect rcDate(m_rcMainChart.left + itemWidth, m_rcTopInfo.top + 8, 
                         m_rcMainChart.left + itemWidth * 2, m_rcTopInfo.bottom);
            pDC->DrawText(dateStr, rcDate, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
            
        // 3. 绘制时间 - 日期旁边
            pDC->SetTextColor(RGB(0, 191, 255)); // 时间为蓝色
            CRect rcTime(m_rcMainChart.left + itemWidth * 2, m_rcTopInfo.top + 8, m_rcMainChart.left + itemWidth * 3, m_rcTopInfo.bottom);
            pDC->DrawText(timeStr, rcTime, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
            
        // 4. 绘制价格 - 根据涨跌设置颜色
            if (m_topInfoData.change > 0)
                pDC->SetTextColor(upColor);
            else if (m_topInfoData.change < 0)
                pDC->SetTextColor(downColor);
            else
                pDC->SetTextColor(textColor);
                
            CRect rcPrice(m_rcMainChart.left + itemWidth * 3, m_rcTopInfo.top + 8, m_rcMainChart.left + itemWidth * 4, m_rcTopInfo.bottom);
            pDC->DrawText(priceStr, rcPrice, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
            
        // 5. 绘制涨跌幅 - 与价格同色
            CRect rcPercent(m_rcMainChart.left + itemWidth * 4, m_rcTopInfo.top + 8, m_rcMainChart.left + itemWidth * 5, m_rcTopInfo.bottom);
            pDC->DrawText(percentStr, rcPercent, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
            
        // 6. 绘制成交量 - 使用白色
            pDC->SetTextColor(textColor);
            CString volumeLabel = _T("量:");
            CRect rcVolumeLabel(m_rcMainChart.left + itemWidth * 5, m_rcTopInfo.top + 8, m_rcMainChart.left + itemWidth * 5 + 25, m_rcTopInfo.bottom);
            pDC->DrawText(volumeLabel, rcVolumeLabel, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
            
            CRect rcVolume(m_rcMainChart.left + itemWidth * 5 + 25, m_rcTopInfo.top + 8, m_rcMainChart.left + itemWidth * 6, m_rcTopInfo.bottom);
            pDC->DrawText(volumeStr, rcVolume, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
            
        // 7. 绘制成交额 - 使用白色
            CString amountLabel = _T("额:");
            CRect rcAmountLabel(m_rcMainChart.left + itemWidth * 6, m_rcTopInfo.top + 8, m_rcMainChart.left + itemWidth * 6 + 25, m_rcTopInfo.bottom);
            pDC->DrawText(amountLabel, rcAmountLabel, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
            
            CRect rcAmount(m_rcMainChart.left + itemWidth * 6 + 25, m_rcTopInfo.top + 8, m_rcMainChart.left + itemWidth * 7, m_rcTopInfo.bottom);
            pDC->DrawText(amountStr, rcAmount, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
        
        // 8. 显示涨停板坐标模式状态
        CString modeStr;
        if (m_bShowLimitPriceCoordinates)
            modeStr = _T("涨停板坐标[开]");
        else
            modeStr = _T("涨停板坐标[关]");
        
        pDC->SetTextColor(RGB(255, 165, 0)); // 橙色
        CRect rcMode(m_rcMainChart.left + itemWidth * 7, m_rcTopInfo.top + 8, m_rcMainChart.right, m_rcTopInfo.bottom);
        pDC->DrawText(modeStr, rcMode, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
        }
        else
        {
        // 如果没有有效的鼠标位置数据，仅显示股票代码、日期信息和涨停板坐标模式
            if (!m_strCode.IsEmpty())
            {
                // 显示股票代码
                pDC->SetTextColor(RGB(255, 255, 255));
                pDC->TextOut(m_rcMainChart.left, m_rcTopInfo.top + 8, m_strCode);
            }
            
            // 显示日期信息
            pDC->SetTextColor(RGB(255, 255, 0)); // 日期为黄色
            CString strDateInfo = m_strCurrentDate;
            CSize textSize = pDC->GetTextExtent(strDateInfo);
        pDC->TextOut(m_rcMainChart.right - textSize.cx - 150, m_rcTopInfo.top + 8, strDateInfo);
        
        // 显示涨停板坐标模式状态
        CString modeStr;
        if (m_bShowLimitPriceCoordinates)
            modeStr = _T("涨停板坐标[开]");
        else
            modeStr = _T("涨停板坐标[关]");
        
        pDC->SetTextColor(RGB(255, 165, 0)); // 橙色
        pDC->TextOut(m_rcMainChart.right - 120, m_rcTopInfo.top + 8, modeStr);
    }
    
    pDC->SelectObject(pOldFont);
}

// 绘制底部时间坐标
void CTimeLine::DrawBottomTime(CDC* pDC)
{
    // 绘制背景
    pDC->FillSolidRect(&m_rcBottomTime, RGB(0, 0, 0));
    
    // 绘制时间刻度，进一步增大字号
    CFont timeFont;
    timeFont.CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
                       ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
                       DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
    
    CFont* pOldFont = pDC->SelectObject(&timeFont);
    // 设置文本颜色为红色
    pDC->SetTextColor(RGB(255, 0, 0));
    pDC->SetBkMode(TRANSPARENT);
    
    // 交易时间刻度 - 11个时间点
    CString timeLabels[] = {
        _T("9:15"), _T("9:20"), _T("9:30"), _T("10:00"), _T("10:30"), 
        _T("11:00"), _T("11:30"), _T("13:30"), _T("14:00"), _T("14:30"), _T("15:00")
    };
    
    // 计算竞价区相关位置
    int auctionRightX = m_rcAuctionArea.right;
    int auctionMiddleX = m_rcAuctionArea.left + m_rcAuctionArea.Width() / 2;
    
    // 计算价格区域均分位置
    int priceAreaWidth = m_rcPriceArea.Width();
    int xSegment = priceAreaWidth / 8; // 8等分获得9个点
    
    // 确定起始时间点索引，如果不显示竞价区则从9:30(索引2)开始
    int startIndex = m_bShowAuctionArea ? 0 : 2;
    
    // 绘制每个时间刻度
    for (int i = startIndex; i < 11; i++)
    {
        int x = 0;
        
        if (m_bShowAuctionArea) {
            // 当显示集合竞价区时
            if (i == 0) {
                // 9:15 - 与左边框居中对齐
                CSize textSize = pDC->GetTextExtent(timeLabels[i]);
                x = (m_rcMainChart.left + m_rcAuctionArea.left) / 2;
            }
            else if (i == 1) {
                // 9:20 - 竞价区中间垂直分割线
                x = auctionMiddleX;
            }
            else if (i == 2) {
                // 9:30 - 竞价区与价格区分界线
                x = auctionRightX;
            }
            else {
                // 其余时间点均匀分布在价格区域
                // 10:00至15:00共8个点，均匀分布
                int priceIndex = i - 3; // 从0开始计算
                x = auctionRightX + (priceIndex + 1) * xSegment;
            }
        } else {
            // 当不显示集合竞价区时
            if (i == 2) {
                // 9:30 - 与主图左边框对齐
                x = m_rcMainChart.left;
            }
            else {
                // 其余时间点均匀分布在价格区域
                // 10:00至15:00共8个点，均匀分布在9个位置
                int priceIndex = i - 3; // 从0开始计算
                x = m_rcMainChart.left + (priceIndex + 1) * (m_rcPriceArea.Width() / 8);
            }
        }
        
        CSize textSize = pDC->GetTextExtent(timeLabels[i]);
        pDC->TextOut(x - textSize.cx/2, m_rcBottomTime.top + 7, timeLabels[i]);
    }
    
    pDC->SelectObject(pOldFont);
}

// 绘制左侧价格坐标
void CTimeLine::DrawLeftPrice(CDC* pDC)
{
    // 检查数据有效性
    if (!m_chartData.dataValid || m_chartData.priceRange <= 0) {
        return;
    }
    
    // 绘制价格刻度
    CFont priceFont;
    priceFont.CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
                        ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
                        DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
    
    CFont* pOldFont = pDC->SelectObject(&priceFont);
    pDC->SetBkMode(TRANSPARENT);
    
    // 使用与DrawMainChartBackground相同的计算方法，确保与水平线一致
    double priceAreaHeight = static_cast<double>(m_rcPriceArea.Height());
    double priceYStep = priceAreaHeight / 16.0; // 16等分，与水平线一致
    
    // 使用缓存的价格数据，确保与图表绘制一致
    float preClose = m_chartData.preClose;
    float minPrice = m_chartData.minPrice;
    float maxPrice = m_chartData.maxPrice;
    
    // 定义价格刻度和颜色
    struct PriceLabel {
        CString label;
        COLORREF color;
    };
    
    // 创建动态价格刻度数组
    PriceLabel priceLabels[17];
    
    // 计算价格刻度间隔
    float priceStep = (maxPrice - minPrice) / 16.0f;
    
    // 填充价格刻度数组
    for (int i = 0; i <= 16; i++)
    {
        // 零轴位置（第8根线）应显示昨收价
        if (i == 8)
        {
            // 零轴位置显示昨收价
            priceLabels[i].label.Format(_T("%.2f"), preClose);
            // 零轴价格使用白色
            priceLabels[i].color = RGB(255, 255, 255);  // 白色 - 平盘
        }
        else
        {
            // 计算其他位置的价格值
            float price;
            
            if (m_bShowLimitPriceCoordinates)
            {
                // 涨停板坐标模式下的计算
                // 顶部(i=0)显示涨停价，底部(i=16)显示跌停价，中间(i=8)显示昨收价
                float limitUpPrice = CalcLimitUpPrice(m_chartData.code, m_chartData.name, preClose);
                float limitDownPrice = CalcLimitDownPrice(m_chartData.code, m_chartData.name, preClose);
                
                if (i < 8)
                {
                    // 上半部分：从涨停价到昨收价，均匀分布
                    float ratio = (float)(8 - i) / 8.0f;
                    price = preClose + (limitUpPrice - preClose) * ratio;
                }
                else
                {
                    // 下半部分：从昨收价到跌停价，均匀分布
                    float ratio = (float)(i - 8) / 8.0f;
                    price = preClose - (preClose - limitDownPrice) * ratio;
                }
            }
            else
            {
                // 使用线性映射计算价格值
                price = minPrice + priceStep * (16 - i);
            }
            
            // 设置标签
            priceLabels[i].label.Format(_T("%.2f"), price);
            
            // 设置颜色：与昨收价比较，高于昨收显示红色，低于昨收显示绿色
            if (price > preClose)
                priceLabels[i].color = RGB(255, 50, 50);    // 红色 - 上涨
            else if (price < preClose)
                priceLabels[i].color = RGB(0, 230, 0);      // 绿色 - 下跌
            else
                priceLabels[i].color = RGB(255, 255, 255);  // 白色 - 平盘
        }
    }
    
    // 绘制价格标签，确保与水平线精确对齐
    for (int i = 0; i <= 16; i++)
    {
        // 使用与DrawMainChartBackground相同的计算方法计算Y坐标
        // 注意：i=0对应顶部边框，i=16对应底部边框，i=1-15对应15条水平线
        int y = 0;
        
        if (i == 0) {
            // 顶部边框
            y = m_rcPriceArea.top;
        } else if (i == 16) {
            // 底部边框
            y = m_rcPriceArea.bottom;
        } else {
            // 水平线位置，使用四舍五入确保精确定位
            y = m_rcPriceArea.top + static_cast<int>(i * priceYStep + 0.5);
        }
        
        // 设置文本颜色
        pDC->SetTextColor(priceLabels[i].color);
        
        // 计算文本大小，用于垂直居中定位
        CSize textSize = pDC->GetTextExtent(priceLabels[i].label);
        
        // 绘制文本，注意垂直居中
        pDC->TextOut(m_rcLeftPrice.right - textSize.cx - 5, y - textSize.cy/2, priceLabels[i].label);
    }
    
    // 添加成交量区域左侧坐标标签
    if (m_chartData.maxVolume > 0) {
        // 计算成交量区域水平线的位置 (与DrawMainChartBackground中计算相同)
        double volumeAreaHeight = static_cast<double>(m_rcVolumeArea.Height());
        double volumeYStep = volumeAreaHeight / 5.0; // 5等分，画4条线
        
        // 创建成交量标签数组
        CString volumeLabels[4];
        
        // 计算成交量刻度间隔
        double volumeStep = m_chartData.maxVolume / 5.0;
        
        // 填充成交量标签数组，以万为单位显示 - 只创建4个标签用于中间的4条线
        for (int i = 1; i <= 4; i++) {
            double volume = volumeStep * (5 - i);
            
            // 修改格式化逻辑，只在超过十万时才使用不同单位显示
            if (volume >= 1000000) // 100万以上
                volumeLabels[i-1].Format(_T("%.1f百万"), volume / 1000000.0);
            else if (volume >= 100000) // 10-100万
                volumeLabels[i-1].Format(_T("%.1f万"), volume / 10000.0);
            else // 小于10万，直接显示整数
                volumeLabels[i-1].Format(_T("%.0f"), volume);
        }
        
        // 绘制成交量标签
        pDC->SetTextColor(RGB(0, 191, 255)); // 使用亮蓝色
        
        // 只绘制中间4条水平线处的标签，不在边界处显示
        for (int i = 1; i <= 4; i++) {
            // 计算水平线位置
            int y = m_rcVolumeArea.top + static_cast<int>(i * volumeYStep + 0.5);
            
            // 确保不超出成交量区域边界
            if (y >= m_rcVolumeArea.bottom) {
                y = m_rcVolumeArea.bottom - 1;
            }
            
            // 计算文本大小，用于垂直居中定位
            CSize textSize = pDC->GetTextExtent(volumeLabels[i-1]);
            
            // 绘制文本，注意垂直居中
            pDC->TextOut(m_rcLeftPrice.right - textSize.cx - 5, y - textSize.cy/2, volumeLabels[i-1]);
        }
    }
    
    pDC->SelectObject(pOldFont);
}

// 绘制右侧百分比坐标
void CTimeLine::DrawRightPercent(CDC* pDC)
{
    // 检查数据有效性
    if (!m_chartData.dataValid || m_chartData.priceRange <= 0) {
        return;
    }
    
    // 绘制百分比刻度
    CFont percentFont;
    percentFont.CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
                          ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
                          DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
    
    CFont* pOldFont = pDC->SelectObject(&percentFont);
    pDC->SetBkMode(TRANSPARENT);
    
    // 使用与DrawMainChartBackground相同的计算方法，确保与水平线一致
    double priceAreaHeight = static_cast<double>(m_rcPriceArea.Height());
    double priceYStep = priceAreaHeight / 16.0; // 16等分，与水平线一致
    
    // 使用缓存的价格数据，确保与图表绘制一致
    float preClose = m_chartData.preClose;
    float minPrice = m_chartData.minPrice;
    float maxPrice = m_chartData.maxPrice;
    
    // 定义百分比刻度和颜色
    struct PercentLabel {
        CString label;
        COLORREF color;
    };
    
    // 创建动态百分比刻度数组
    PercentLabel percentLabels[17];
    
    // 计算百分比刻度
    // 零轴百分比始终为0%，其他位置根据价格相对于昨收价计算百分比
    for (int i = 0; i <= 16; i++)
    {
        // 零轴位置（第8根线）显示为0.00%
        if (i == 8)
        {
            percentLabels[i].label = _T("0.00%");
            percentLabels[i].color = RGB(255, 255, 255);  // 白色 - 平盘
        }
        else
        {
            float price;
            
            if (m_bShowLimitPriceCoordinates)
            {
                // 涨停板坐标模式下的计算
                // 顶部(i=0)显示涨停价，底部(i=16)显示跌停价，中间(i=8)显示昨收价
                float limitUpPrice = CalcLimitUpPrice(m_chartData.code, m_chartData.name, preClose);
                float limitDownPrice = CalcLimitDownPrice(m_chartData.code, m_chartData.name, preClose);
                
                if (i < 8)
                {
                    // 上半部分：从涨停价到昨收价，均匀分布
                    float ratio = (float)(8 - i) / 8.0f;
                    price = preClose + (limitUpPrice - preClose) * ratio;
                }
                else
                {
                    // 下半部分：从昨收价到跌停价，均匀分布
                    float ratio = (float)(i - 8) / 8.0f;
                    price = preClose - (preClose - limitDownPrice) * ratio;
                }
            }
            else
            {
                // 价格刻度间隔
                float priceStep = (maxPrice - minPrice) / 16.0f;
                // 使用线性映射计算价格值
                price = minPrice + priceStep * (16 - i);
            }
            
            // 计算百分比
            float percent = (price - preClose) / preClose * 100.0f;
            
            // 设置百分比标签
            if (percent > 0)
                percentLabels[i].label.Format(_T("+%.2f%%"), percent);
            else
                percentLabels[i].label.Format(_T("%.2f%%"), percent);
            
            // 设置颜色：上涨显示红色，下跌显示绿色
            if (percent > 0)
                percentLabels[i].color = RGB(255, 50, 50);    // 红色 - 上涨
            else if (percent < 0)
                percentLabels[i].color = RGB(0, 230, 0);      // 绿色 - 下跌
            else
                percentLabels[i].color = RGB(255, 255, 255);  // 白色 - 平盘
        }
    }
    
    // 绘制百分比标签，确保与水平线精确对齐
    for (int i = 0; i <= 16; i++)
    {
        // 使用与DrawMainChartBackground相同的计算方法计算Y坐标
        // 注意：i=0对应顶部边框，i=16对应底部边框，i=1-15对应15条水平线
        int y = 0;
        
        if (i == 0) {
            // 顶部边框
            y = m_rcPriceArea.top;
        } else if (i == 16) {
            // 底部边框
            y = m_rcPriceArea.bottom;
        } else {
            // 水平线位置，使用四舍五入确保精确定位
            y = m_rcPriceArea.top + static_cast<int>(i * priceYStep + 0.5);
        }
        
        // 设置文本颜色
        pDC->SetTextColor(percentLabels[i].color);
        
        // 计算文本大小，用于垂直居中定位
        CSize textSize = pDC->GetTextExtent(percentLabels[i].label);
        
        // 绘制文本，注意垂直居中和左侧对齐
        pDC->TextOut(m_rcRightPercent.left + 5, y - textSize.cy/2, percentLabels[i].label);
    }
    
    // 添加成交量区域右侧坐标标签
    if (m_chartData.maxVolume > 0) {
        // 计算成交量区域水平线的位置 (与DrawMainChartBackground中计算相同)
        double volumeAreaHeight = static_cast<double>(m_rcVolumeArea.Height());
        double volumeYStep = volumeAreaHeight / 5.0; // 5等分，画4条线
        
        // 创建成交量标签数组
        CString volumeLabels[4];
        
        // 计算成交量刻度间隔
        double volumeStep = m_chartData.maxVolume / 5.0;
        
        // 填充成交量标签数组，以万为单位显示 - 只创建4个标签用于中间的4条线
        for (int i = 1; i <= 4; i++) {
            double volume = volumeStep * (5 - i);
            
            // 修改格式化逻辑，只在超过十万时才使用不同单位显示
            if (volume >= 1000000) // 100万以上
                volumeLabels[i-1].Format(_T("%.1f百万"), volume / 1000000.0);
            else if (volume >= 100000) // 10-100万
                volumeLabels[i-1].Format(_T("%.1f万"), volume / 10000.0);
            else // 小于10万，直接显示整数
                volumeLabels[i-1].Format(_T("%.0f"), volume);
        }
        
        // 绘制成交量标签
        pDC->SetTextColor(RGB(0, 191, 255)); // 使用亮蓝色
        
        // 只绘制中间4条水平线处的标签，不在边界处显示
        for (int i = 1; i <= 4; i++) {
            // 计算水平线位置
            int y = m_rcVolumeArea.top + static_cast<int>(i * volumeYStep + 0.5);
            
            // 确保不超出成交量区域边界
            if (y >= m_rcVolumeArea.bottom) {
                y = m_rcVolumeArea.bottom - 1;
            }
            
            // 计算文本大小，用于垂直居中定位
            CSize textSize = pDC->GetTextExtent(volumeLabels[i-1]);
            
            // 绘制文本，注意垂直居中和左侧对齐
            pDC->TextOut(m_rcRightPercent.left + 5, y - textSize.cy/2, volumeLabels[i-1]);
        }
    }
    
    pDC->SelectObject(pOldFont);
}


// 计算价格范围的通用方法
bool CTimeLine::CalculatePriceRange(std::string code, std::string name, float& preClose, float& minPrice, float& maxPrice)
{
	// 计算涨停价和跌停价
	float limitUpPrice      = CalcLimitUpPrice(code, name, preClose);
	float limitDownPrice    = CalcLimitDownPrice(code, name, preClose);

	// 如果启用涨停板坐标系
	if ((m_bShowLimitPriceCoordinates && preClose > 0) || (limitUpPrice == maxPrice) || (limitDownPrice == minPrice))
	{
		// 首先确保价格范围至少包括涨跌停价格
		maxPrice = max(maxPrice, limitUpPrice);
		minPrice = min(minPrice, limitDownPrice);

		// 然后检查当前价格是否在范围内，如果不在，则扩展范围
		if (m_fCurrentPrice > 0)
		{
			maxPrice = max(maxPrice, m_fCurrentPrice);
			minPrice = min(minPrice, m_fCurrentPrice);
		}
	}
	else
	{
		// 非涨停板坐标模式下，使用原有逻辑扩展价格范围
		float priceRange = maxPrice - minPrice;
		if (priceRange < 0.01f)
		{
			priceRange = 0.01f * preClose;
		}

		// 确保价格范围上下对称，以便与百分比坐标保持一致
		if (preClose > 0) {
			// 计算当前价格范围相对于昨收价的最大百分比偏差
			float maxPriceDev = (maxPrice - preClose) / preClose;
			float minPriceDev = (preClose - minPrice) / preClose;
			float maxDev = max(maxPriceDev, minPriceDev);

			// 确保有一个最小显示范围（至少3%）
			maxDev = max(maxDev, 0.03f);

			// 基于最大偏差重新计算价格范围，确保上下对称
			maxPrice = preClose * (1.0f + maxDev);
			minPrice = preClose * (1.0f - maxDev);

			// 再额外扩展10%的空间，但不超过涨跌停限制
			float finalRange = maxPrice - minPrice;
			float expandedMax = maxPrice + finalRange * 0.1f;
			float expandedMin = minPrice - finalRange * 0.1f;
			
			// 确保扩展后的价格范围不超出涨跌停限制
			maxPrice = min(expandedMax, limitUpPrice);
			minPrice = max(expandedMin, limitDownPrice);
		}
		else {
			// 原有逻辑：扩展范围，上下各增加10%的空间，但不超过涨跌停限制
			float expandedMin = minPrice - priceRange * 0.1f;
			float expandedMax = maxPrice + priceRange * 0.1f;
			
			// 确保扩展后的价格范围不超出涨跌停限制
			maxPrice = min(expandedMax, limitUpPrice);
			minPrice = max(expandedMin, limitDownPrice);
		}
	}

	// 确保最小价格大于0
	minPrice = max(0.01f, minPrice);

	return true;
}

// 准备图表数据，在绘制前调用
void CTimeLine::PrepareChartData()
{
	try {
		// 清除旧数据
		m_chartData.Clear();

		// 如果没有股票代码，不需要准备数据
	if (m_strCode.IsEmpty())
		return;

	// 获取文档
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;

	// 获取股票索引
	int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
	if (stockIndex < 0)
		return;

	// 获取股票数据
	const StockData* pStockData = pDoc->GetStock(stockIndex);
	if (!pStockData)
		return;

	m_chartData.code = pStockData->_Code;
	m_chartData.name = pStockData->_Name;

	// 获取分时数据
	const std::vector<TLINE_DATA>& timeLineData = pStockData->_vecTimeLine;

	// 如果数据为空，返回
	if (timeLineData.empty())
		return;

	// 获取昨日收盘价
	float preClose = pStockData->_preClose;

	if (preClose <= 0)
	{
		// 如果没有有效的昨收价，使用开盘价
		preClose = pStockData->_Open;

		// 如果开盘价也无效，使用当前价格
		if (preClose <= 0 && pStockData->_Close > 0)
			preClose = pStockData->_Close;

		// 如果仍然没有有效的价格，使用默认值
		if (preClose <= 0)
			preClose = 10.0f;
	}

	// 保存昨收价
	m_chartData.preClose = preClose;

	// 先分析数据，计算绘图所需的关键值
	float minPrice = preClose;
	float maxPrice = preClose;
	double maxVolume = 0;

	// 分析极值以便自动调整绘图比例
	for (const auto& data : timeLineData)
	{
		// 价格极值
		if (data._Price > 0)
		{
			minPrice = min(minPrice, data._Price);
			maxPrice = max(maxPrice, data._Price);
		}

		// 成交量极值
		maxVolume = max(maxVolume, data._Volume);
	}

	// 使用统一的价格范围计算方法
	CalculatePriceRange(pStockData->_Code, pStockData->_Name, preClose, minPrice, maxPrice);

	// 保存最大成交量和价格范围
	m_chartData.minPrice = minPrice;
	m_chartData.maxPrice = maxPrice;
	m_chartData.priceRange = maxPrice - minPrice;
	m_chartData.maxVolume = maxVolume;
	m_maxVolume = maxVolume; // 同时更新类成员，用于其他方法

	// 计算涨跌幅范围
	if (preClose > 0) 
    {
		// 使用实际价格计算百分比
		m_chartData.minPercent = (minPrice / preClose - 1) * 100;
		m_chartData.maxPercent = (maxPrice / preClose - 1) * 100;

		// 如果启用涨停板坐标模式，则确保百分比范围至少包含0%
		if (m_bShowLimitPriceCoordinates) {
			if (m_chartData.minPercent > 0) m_chartData.minPercent = 0;
			if (m_chartData.maxPercent < 0) m_chartData.maxPercent = 0;
		}
		// 在非涨停板坐标模式下，百分比范围应已经从价格计算中保持上下对称
		// 无需额外处理，只需确保显示范围合理
		else {
			// 确保有一个最小的显示范围
			float absMaxPercent = max(fabs(m_chartData.maxPercent), fabs(m_chartData.minPercent));
			absMaxPercent = max(absMaxPercent, 3.0f);

			// 确保百分比范围与价格范围保持一致
			if (fabs(m_chartData.maxPercent - absMaxPercent) > 0.1f ||
				fabs(m_chartData.minPercent + absMaxPercent) > 0.1f) {
				m_chartData.maxPercent = absMaxPercent;
				m_chartData.minPercent = -absMaxPercent;
			}
		}
	}
	else {
		// 如果没有昨收价，则使用默认值
		m_chartData.minPercent = -10.0f;
		m_chartData.maxPercent = 10.0f;
	}

	// 准备主图表数据
	PrepareMainChartData();

	// 准备成交量数据
	PrepareVolumeData();

	// 准备集合竞价区数据
	PrepareAuctionData();

		// 更新状态标志
		m_chartData.dataValid = true;
		m_bNeedRecalcData = false;
	}
	catch (const std::exception& e) {
		TRACE(_T("PrepareChartData异常: %hs\n"), e.what());
		m_chartData.dataValid = false;
		m_bNeedRecalcData = true;
	}
	catch (...) {
		TRACE(_T("PrepareChartData发生未知异常\n"));
		m_chartData.dataValid = false;
		m_bNeedRecalcData = true;
	}
}

// 准备主图表数据
void CTimeLine::PrepareMainChartData()
{
	// 如果没有有效的股票数据，直接返回
	if (m_strCode.IsEmpty())
		return;

	// 获取文档
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;

	// 获取股票索引
	int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
	if (stockIndex < 0)
		return;

	// 获取股票数据
	const StockData* pStockData = pDoc->GetStock(stockIndex);
	if (!pStockData)
		return;

	// 获取分时数据
	const std::vector<TLINE_DATA>& timeLineData = pStockData->_vecTimeLine;

	// 如果数据为空，返回
	if (timeLineData.empty())
		return;

	// 确保价格范围不为0，避免除零错误
	if (m_chartData.priceRange < 0.001f) {
		m_chartData.priceRange = 0.001f;
	}

	// 计算价格区域高度和像素比例
	float priceAreaHeight = (float)(m_rcPriceArea.Height() - 2);  // 留出上下各1像素边距
	float priceToPixel = priceAreaHeight / m_chartData.priceRange;
	m_chartData.priceToPixel = priceToPixel;

	// 计算交易时段的绘图宽度
	int drawingWidth = m_rcPriceArea.Width() - 2;  // 留出两侧各1像素边距

	// 常规交易时间从9:30到15:00，共330分钟（包括中午休市）
	// 忽略中午休市时间，实际交易时间为241分钟：上午9:30-11:30(120分钟)，下午13:00-15:00(121分钟)
	int totalMinutes = 241;
	float pixelsPerMinute = (float)drawingWidth / (float)totalMinutes;

	// 获取价格区域的左边界（可能是集合竞价区右边界或主图表左边界）
	int priceAreaLeft = m_rcPriceArea.left + 1;  // 留出1像素边距

	// 用于记录每分钟的最后一个价格，以判断涨跌
	float lastMinutePrice = 0;
	int lastMinuteTime = 0;

	// 清理并预分配数组大小以提高性能
	m_chartData.pricePoints.clear();
	m_chartData.avgPricePoints.clear();
	m_chartData.pricePoints.reserve(timeLineData.size());
	m_chartData.avgPricePoints.reserve(timeLineData.size());

	// 遍历数据，转换为绘图点
	for (size_t i = 0; i < timeLineData.size(); i++)
	{
		const TLINE_DATA& data = timeLineData[i];

		// 计算时间点对应的索引
		int timeIndex = -1;
		int nHour = data._Time / 100;
		int nMinute = data._Time % 100;

		// 处理集合竞价数据 - 跳过，在PrepareAuctionData中处理
		if (nHour == 9 && nMinute >= 15 && nMinute < 30)
			continue;

		// 将时间转换为相对于9:30的分钟数
		if (nHour == 9 && nMinute >= 30)
			timeIndex = nMinute - 30;
		else if (nHour == 10)
			timeIndex = 30 + nMinute;
		else if (nHour == 11 && nMinute <= 30)
			timeIndex = 90 + nMinute;
		else if (nHour == 13)
			timeIndex = 120 + nMinute;
		else if (nHour == 14)
			timeIndex = 180 + nMinute;
		else if (nHour == 15 && nMinute == 0)
			timeIndex = 240; // 15:00点收盘

		// 如果时间点无效，跳过
		if (timeIndex < 0 || timeIndex >= totalMinutes)
			continue;

		// 计算x坐标（从左到右）
		int x = priceAreaLeft + (int)(timeIndex * pixelsPerMinute);

		// 确保x坐标在有效范围内
		x = max(m_rcPriceArea.left, min(x, m_rcPriceArea.right - 1));

		// 计算价格y坐标（从上到下，价格越高y值越小）
		if (data._Price > 0)
		{
			// 确保价格在显示范围内（限制在最小价格和最大价格之间）
			float clampedPrice = max(m_chartData.minPrice, min(data._Price, m_chartData.maxPrice));

			// 计算价格对应的y坐标
			int y = m_rcPriceArea.bottom - 1 - (int)((clampedPrice - m_chartData.minPrice) * priceToPixel);

			// 确保y坐标在有效范围内（考虑上下边距）
			y = max(m_rcPriceArea.top + 1, min(y, m_rcPriceArea.bottom - 1));

			m_chartData.pricePoints.push_back(CPoint(x, y));
		}

		// 计算均价y坐标，使用相同的计算规则保证一致性
		if (data._avgPrice > 0)
		{
			// 确保均价在显示范围内（限制在最小价格和最大价格之间）
			float clampedAvgPrice = max(m_chartData.minPrice, min(data._avgPrice, m_chartData.maxPrice));

			// 计算均价对应的y坐标，与价格计算保持一致
			int y = m_rcPriceArea.bottom - 1 - (int)((clampedAvgPrice - m_chartData.minPrice) * priceToPixel);

			// 确保y坐标在有效范围内（考虑上下边距）
			y = max(m_rcPriceArea.top + 1, min(y, m_rcPriceArea.bottom - 1));

			m_chartData.avgPricePoints.push_back(CPoint(x, y));
		}
	}
}

// 准备成交量数据
void CTimeLine::PrepareVolumeData()
{
	// 如果没有有效的股票数据，直接返回
	if (m_strCode.IsEmpty())
		return;

	// 获取文档
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;

	// 获取股票索引
	int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
	if (stockIndex < 0)
		return;

	// 获取股票数据
	const StockData* pStockData = pDoc->GetStock(stockIndex);
	if (!pStockData)
		return;

	// 获取分时数据
	const std::vector<TLINE_DATA>& timeLineData = pStockData->_vecTimeLine;

	// 如果数据为空，返回
	if (timeLineData.empty())
		return;

	// 计算每分钟的成交量（而非累计量）
	std::vector<double> minuteVolumes;
	// 创建索引映射表，记录哪些数据点实际被添加到minuteVolumes中
	std::vector<size_t> validIndices;

	minuteVolumes.reserve(timeLineData.size());
	validIndices.reserve(timeLineData.size());

	// 找出最大的分钟成交量，用于绘图比例计算
	double maxMinuteVolume = 0.0;

	// 用于记录上一个数据点的成交量和时间
	double lastVolume = 0.0;
	int lastTime = 0;

	// 第一遍遍历，计算每分钟的成交量
	for (size_t i = 0; i < timeLineData.size(); i++)
	{
		const TLINE_DATA& data = timeLineData[i];
		int currentTime = data._Time;
		double currentVolume = data._Volume;

		// 检查时间点是否有效（跳过集合竞价数据）
		int nHour = currentTime / 100;
		int nMinute = currentTime % 100;

		// 跳过集合竞价阶段的数据和无效时间点
		if (nHour == 9 && nMinute >= 15 && nMinute < 30)
			continue;

		// 计算当前分钟的成交量（当前累计量减去上一分钟的累计量）
		double minuteVolume = 0.0;

		if (i == 0 || validIndices.empty())
		{
			// 第一个有效数据点，就是其成交量
			minuteVolume = currentVolume;
		}
		else
		{
			// 非第一个有效数据点，计算与前一点的差值
			minuteVolume = currentVolume - lastVolume;

			// 如果是新的一分钟，确保使用正确的成交量
			if (currentTime != lastTime)
			{
				// 如果差值为负（数据异常），使用当前值
				if (minuteVolume < 0)
				{
					minuteVolume = currentVolume;
				}
			}
		}

		// 存储当前分钟的成交量
		minuteVolumes.push_back(minuteVolume);
		validIndices.push_back(i);  // 记录这是原始数据中的第i个点

		// 更新最大分钟成交量
		maxMinuteVolume = max(maxMinuteVolume, minuteVolume);

		// 更新上一个数据点信息
		lastVolume = currentVolume;
		lastTime = currentTime;
	}

	// 更新图表数据中的最大成交量为分钟最大值
	m_chartData.maxVolume = maxMinuteVolume;

	// 计算成交量区域高度和像素比例
	int volumeAreaHeight = m_rcVolumeArea.Height() - 1; // 留出1像素边距
	m_chartData.volumeToPixel = (maxMinuteVolume > 0) ? ((double)volumeAreaHeight / maxMinuteVolume) : 1;

	// 计算交易时段的绘图宽度
	int drawingWidth = m_rcPriceArea.Width() - 2; // 留出两侧各1像素边距

	// 常规交易时间从9:30到15:00，共330分钟（包括中午休市）
	// 忽略中午休市时间，实际交易时间为241分钟：上午9:30-11:30(120分钟)，下午13:00-15:00(121分钟)
	int totalMinutes = 241;
	float pixelsPerMinute = (float)drawingWidth / (float)totalMinutes;

	// 获取价格区域的左边界（可能是集合竞价区右边界或主图表左边界）
	int priceAreaLeft = m_rcPriceArea.left + 1; // 留出1像素边距

	// 用于记录每分钟的最后一个价格，以判断涨跌
	float lastMinutePrice = 0;
	int lastMinuteTime = 0;

	// 清理并预分配数组大小以提高性能
	m_chartData.volumeLines.clear();
	m_chartData.volumeColors.clear();
	m_chartData.volumeLines.reserve(minuteVolumes.size());
	m_chartData.volumeColors.reserve(minuteVolumes.size());

	// 第二遍遍历，使用有效索引创建成交量直线
	for (size_t j = 0; j < validIndices.size(); j++)
	{
		// 使用记录的有效索引来访问原始数据
		size_t i = validIndices[j];

		// 确保索引有效
		if (i >= timeLineData.size())
			continue;

		const TLINE_DATA& data = timeLineData[i];

		// 计算时间点对应的索引
		int timeIndex = -1;
		int nHour = data._Time / 100;
		int nMinute = data._Time % 100;

		// 将时间转换为相对于9:30的分钟数
		if (nHour == 9 && nMinute >= 30)
			timeIndex = nMinute - 30;
		else if (nHour == 10)
			timeIndex = 30 + nMinute;
		else if (nHour == 11 && nMinute <= 30)
			timeIndex = 90 + nMinute;
		else if (nHour == 13)
			timeIndex = 120 + nMinute;
		else if (nHour == 14)
			timeIndex = 180 + nMinute;
		else if (nHour == 15 && nMinute == 0)
			timeIndex = 240; // 15:00点收盘

		// 如果时间点无效，跳过
		if (timeIndex < 0 || timeIndex >= totalMinutes)
			continue;

		// 计算x坐标（从左到右）
		int x = priceAreaLeft + (int)(timeIndex * pixelsPerMinute);

		// 确保x坐标在有效范围内
		x = max(m_rcVolumeArea.left + 1, min(x, m_rcVolumeArea.right - 1));

		// 使用分钟成交量而不是累计成交量
		double minuteVolume = minuteVolumes[j];  // 改为使用j作为索引，而不是i

		// 绘制成交量直线
		if (minuteVolume > 0)
		{
			int volumeHeight = (int)(minuteVolume * m_chartData.volumeToPixel);

			// 创建一条从底部到成交量高度的直线
			m_chartData.volumeLines.push_back(
				std::make_pair(
					CPoint(x, m_rcVolumeArea.bottom - 1),
					CPoint(x, m_rcVolumeArea.bottom - volumeHeight)
				)
			);

			// 根据一分钟内价格涨跌设置成交量颜色
			COLORREF volumeColor = RGB(255, 255, 255);  // 默认白色（平盘）

			// 判断是否是一个新的分钟
			int currentMinuteTime = nHour * 100 + nMinute;
			if (currentMinuteTime != lastMinuteTime && lastMinutePrice > 0)
			{
				// 与上一分钟最后一个价格比较
				if (data._Price > lastMinutePrice)
					volumeColor = RGB(255, 255, 0);     // 红色（上涨）
				else if (data._Price < lastMinutePrice)
					volumeColor = RGB(0, 255, 255);     // 绿色（下跌）
				// 等于则保持白色（平盘）
			}

			// 保存成交量颜色
			m_chartData.volumeColors.push_back(volumeColor);

			// 更新最后一个价格
			lastMinutePrice = data._Price;
			lastMinuteTime = currentMinuteTime;
		}
	}

	// 更新类成员变量，用于其他方法
	m_maxVolume = maxMinuteVolume;
}

// 准备集合竞价区数据
void CTimeLine::PrepareAuctionData()
{
	// 如果没有显示集合竞价区，直接返回
	if (!m_bShowAuctionArea || m_rcAuctionArea.IsRectEmpty())
		return;

	// 如果没有有效的股票数据，直接返回
	if (m_strCode.IsEmpty())
		return;

	// 获取文档
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;

	// 获取股票索引
	int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
	if (stockIndex < 0)
		return;

	// 获取股票数据
	const StockData* pStockData = pDoc->GetStock(stockIndex);
	if (!pStockData)
		return;

	// 获取分时数据
	const std::vector<TLINE_DATA>& timeLineData = pStockData->_vecTimeLine;

	// 如果数据为空，返回
	if (timeLineData.empty())
		return;

	// 集合竞价区中间位置
	int auctionWidth = m_rcAuctionArea.Width();
	int middleX = m_rcAuctionArea.left + auctionWidth / 2;

	// 清空之前的数据
	m_chartData.auctionPricePoints.clear();
	m_chartData.auctionVolumeLines.clear();

	// 遍历数据，转换为绘图点
	for (const auto& data : timeLineData)
	{
		int nHour = data._Time / 100;
		int nMinute = data._Time % 100;

		// 仅处理集合竞价数据 (9:15-9:30)
		if (!(nHour == 9 && nMinute >= 15 && nMinute < 30))
			continue;

		// 计算在集合竞价区的x坐标
		int auctionMinute = nMinute - 15; // 9:15-9:30的15分钟集合竞价
		float auctionRatio = 0;
		int x = 0;

		if (auctionMinute < 5) {
			// 左半区 - 9:15至9:20
			auctionRatio = auctionMinute / 5.0f;
			x = m_rcAuctionArea.left + (int)(auctionRatio * (middleX - m_rcAuctionArea.left));
		}
		else if (auctionMinute >= 5 && auctionMinute < 10) {
			// 右半区 - 9:20至9:25
			auctionRatio = (auctionMinute - 5) / 5.0f;
			x = middleX + (int)(auctionRatio * (m_rcAuctionArea.right - middleX));
		}
		else {
			// 9:25-9:30 显示在右边界
			x = m_rcAuctionArea.right;
		}

		// 计算价格y坐标
		if (data._Price > 0)
		{
			int y = m_rcPriceArea.bottom - (int)((data._Price - m_chartData.minPrice) * m_chartData.priceToPixel);
			y = max(m_rcPriceArea.top + 1, min(y, m_rcPriceArea.bottom - 1));
			m_chartData.auctionPricePoints.push_back(CPoint(x, y));
		}

		// 计算成交量直线
		if (data._Volume > 0)
		{
			int volumeHeight = (int)(data._Volume * m_chartData.volumeToPixel);

			// 创建一条从底部到成交量高度的直线
			m_chartData.auctionVolumeLines.push_back(
				std::make_pair(
					CPoint(x, m_rcVolumeArea.bottom - 1),
					CPoint(x, m_rcVolumeArea.bottom - volumeHeight)
				)
			);
		}
	}
}

// 设置股票代码
void CTimeLine::SetStockCode(const std::string& strCode)
{
    if (m_strCode == strCode.c_str() && !m_strCurrentDate.IsEmpty() && !m_bNeedRecalcData)
    {
        // 已经是当前显示的股票代码，不需要更新
        return;
    }
    
    // 保存旧的股票代码
    CString strOldCode = m_strCode;
    
    // 设置新的股票代码
    m_strCode = strCode.c_str();
    
    // 更新集合竞价相关数据
    m_LastCode = m_strCode;
    
    // 设置当前日期
    CTime currentTime = CTime::GetCurrentTime();
    m_MinDate = (DWORD)currentTime.GetTime();
    
    // 重置图表数据状态
    m_chartData.Clear();
    m_bNeedRecalcData = true;
    
    // 隐藏十字光标 - 切换股票时隐藏十字光标
    m_bShowCrossCursor = FALSE;
    m_bCrossCursorFixed = FALSE;
    
    // 尝试加载分时数据，优先查找当前日期的数据
    if (m_strCurrentDate.IsEmpty())
    {
        // 获取当前系统日期
        CTime now = CTime::GetCurrentTime();
        m_strCurrentDate.Format(_T("%04d-%02d-%02d"), now.GetYear(), now.GetMonth(), now.GetDay());
    }
    
    // 加载指定日期的分时数据，如果找不到则尝试查找最近可用的数据
    if (!LoadTimeData(strCode, std::string(m_strCurrentDate)))
    {
        // 找不到当天的数据，尝试查找可用数据
        FindAndLoadRecentData(strCode);
    }
    
    // 重新启动定时器确保实时更新
    if (::IsWindow(m_hWnd))
    {
        KillTimer(TIMER_REALTIME_UPDATE);
        KillTimer(TIMER_REFRESH_DISPLAY);
        
        // 根据是否为交易时段来确定更新频率
        if (IsTradingTime())
        {
            // 交易时段：更频繁的更新
            SetTimer(TIMER_REALTIME_UPDATE, 2000, NULL);  // 每2秒检查一次数据更新
            SetTimer(TIMER_REFRESH_DISPLAY, 500, NULL);   // 每0.5秒刷新一次界面
        }
        else
        {
            // 非交易时段：较慢的更新频率
            SetTimer(TIMER_REALTIME_UPDATE, 10000, NULL); // 每10秒检查一次数据更新
            SetTimer(TIMER_REFRESH_DISPLAY, 5000, NULL);  // 每5秒刷新一次界面
        }
    }
    
    // 刷新界面
    Invalidate();
}

// 新增：查找并加载最近的本地分时数据
BOOL CTimeLine::FindAndLoadRecentData(const std::string& stockCode)
{
	// 如果股票代码为空，返回失败
	if (stockCode.empty())
		return FALSE;

	// 获取当前日期
	CTime now = CTime::GetCurrentTime();
	CString strDate;
	strDate.Format(_T("%04d-%02d-%02d"), now.GetYear(), now.GetMonth(), now.GetDay());

	// 保存日期格式
	m_strCurrentDate = strDate;

	// 加载当天数据
	return LoadTimeData(stockCode, std::string(strDate));
}

// 响应文档更新
void CTimeLine::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 调用基类的更新方法
	CView::OnUpdate(pSender, lHint, pHint);

	// 获取文档
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;

	// 根据提示参数lHint判断不同类型的更新
	switch (lHint)
	{
	case 0:
		// 如果lHint为0，表示常规数据更新或股票切换
	{
		// 获取当前选中的股票
		std::string currentStock = pDoc->GetCurrentStock();
		if (!currentStock.empty())
		{
			CString newCode = currentStock.c_str();

			// 如果股票代码有变化，需要更新
			if (m_strCode.IsEmpty() || m_strCode != newCode)
			{
				// 直接更新股票代码 - SetStockCode内部会处理数据清理和刷新
				SetStockCode(currentStock);
                
                // 隐藏十字光标 - 确保在切换股票时隐藏十字光标
                m_bShowCrossCursor = FALSE;
                m_bCrossCursorFixed = FALSE;
			}
			else
			{
				// 同一股票数据更新，标记需要重新计算数据并刷新界面
				m_bNeedRecalcData = true;
				m_chartData.dataValid = false;
				
				// 隐藏十字光标 - 确保在切换股票时隐藏十字光标
				m_bShowCrossCursor = FALSE;
				m_bCrossCursorFixed = FALSE;
			}
		}
	}
	break;

	case 1:
		// 如果lHint为1，表示仅更新视图，不需要重新加载数据
		Invalidate();
		break;

	case 2:
		// 如果lHint为2，表示切换视图状态（如显示/隐藏个股资讯区）
		UpdateLayout();
		Invalidate();
		break;

	default:
		// 对于其他提示值，默认重绘
		Invalidate();
		break;
	}
}

// 处理大小变化事件
void CTimeLine::OnSize(UINT nType, int cx, int cy)
{
    CView::OnSize(nType, cx, cy);
    
    // 如果窗口被最小化，不必调整
    if (nType == SIZE_MINIMIZED)
        return;
    
    // 更新布局
    UpdateLayout();
    
    // 标记需要重新计算数据
    m_bNeedRecalcData = true;
    
    // 强制重绘
    Invalidate();
}

// 显示/隐藏个股资讯区
void CTimeLine::ShowInfoArea(BOOL bShow)
{
    if (m_bShowInfoArea != bShow)
    {
        m_bShowInfoArea = bShow;
        
        // 标记需要重新计算数据
        m_bNeedRecalcData = true;
        m_chartData.dataValid = false;
        
        UpdateLayout();
        Invalidate();
    }
}

// 获取个股资讯区显示状态
BOOL CTimeLine::IsInfoAreaVisible() const
{
    return m_bShowInfoArea;
}

// 切换个股资讯区显示状态
void CTimeLine::ToggleInfoArea()
{
    ShowInfoArea(!m_bShowInfoArea);
}
// 显示集合竞价区域
void CTimeLine::ShowAuctionArea(BOOL bShow)
{
	if (m_bShowAuctionArea != bShow)
	{
		m_bShowAuctionArea = bShow;

		// 标记需要重新计算数据 - 添加这一行，确保图表数据会被重新计算
		m_bNeedRecalcData = true;
		m_chartData.dataValid = false;

		// 重新计算布局并刷新
		UpdateLayout();
		Invalidate();
	}
}

// 切换集合竞价区域显示状态
void CTimeLine::ToggleAuctionArea()
{
	ShowAuctionArea(!m_bShowAuctionArea);
}

// 键盘消息处理
void CTimeLine::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags)
{
    // 获取文档对象
    CStockDoc* pDoc = GetDocument();
    if (!pDoc)
        return;
    
    // 在switch前先声明和初始化pMainFrame变量
    CMainFrame* pMainFrame = nullptr;
    
    switch (nChar)
    {
        case VK_LEFT:  // 左箭头 - 切换到前一个日期
            NavigateToPreviousDate();
            break;
            
        case VK_RIGHT:  // 右箭头 - 切换到后一个日期
            NavigateToNextDate();
            break;
            
        case VK_UP:  // 上箭头 - 切换到上一支股票
            NavigateToPreviousStock();
            break;
            
        case VK_DOWN:  // 下箭头 - 切换到下一支股票
            NavigateToNextStock();
            break;
            
        case VK_RETURN: // 回车键 - 切换到技术分析视图
            // 获取主框架指针
            pMainFrame = (CMainFrame*)AfxGetMainWnd();
            if (pMainFrame)
            {
                // 切换到K线图视图
                pMainFrame->SwitchView(pMainFrame->m_nKLineViewID);
                
            }
            break;
            
        case 'R':  // R键 - 刷新数据
        case 'r':
            // 切换涨停板坐标显示
            ToggleLimitPriceCoordinates();
            TRACE("涨停板坐标模式%s\n", m_bShowLimitPriceCoordinates ? "已启用" : "已禁用");
            break;
            
        case 'I':  // I键 - 切换个股资讯区显示
        case 'i':
            ToggleInfoArea();
            break;
            
        case 'A':  // A键 - 切换集合竞价区显示
        case 'a':
            ToggleAuctionArea();
            break;
            
        // 添加对十字光标快捷键的处理
        case 'C':
        case 'c':
            // C键 - 切换十字光标显示状态
            ToggleCrossCursor();
            break;
    }
    
    CView::OnKeyDown(nChar, nRepCnt, nFlags);
}

// 切换到上一支股票
void CTimeLine::NavigateToPreviousStock()
{
    // 获取文档
    CStockDoc* pDoc = GetDocument();
    if (!pDoc)
        return;
    
    // 切换到上一支股票
    pDoc->ShowPreviousStock();
    
    // 注意: 不需要手动调用SetStockCode
    // 文档会通知所有视图更新（OnUpdate），OnUpdate会调用SetStockCode
}

// 切换到下一支股票
void CTimeLine::NavigateToNextStock()
{
    
    // 获取文档
    CStockDoc* pDoc = GetDocument();
    if (!pDoc)
        return;
    
    // 切换到下一支股票
    pDoc->ShowNextStock();
    
    // 注意: 不需要手动调用SetStockCode
    // 文档会通知所有视图更新（OnUpdate），OnUpdate会调用SetStockCode
}

// 切换到前一天数据
void CTimeLine::NavigateToPreviousDate()
{
    
    if (m_strCode.IsEmpty())
        return;
    
    // 获取当前日期，如果未指定，则使用当前系统日期
    CTime dateNow = CTime::GetCurrentTime();
    CTime dateCurrent;
    
    if (m_strCurrentDate.IsEmpty())
    {
        dateCurrent = dateNow;
    }
    else
    {
        // 将字符串转换为日期格式 (YYYY-MM-DD)
        int year, month, day;
        if (_stscanf_s(m_strCurrentDate, _T("%d-%d-%d"), &year, &month, &day) == 3)
        {
            dateCurrent = CTime(year, month, day, 0, 0, 0);
        }
        else
        {
            dateCurrent = dateNow;
        }
    }
    
    // 最多尝试查找10个交易日
    const int MAX_ATTEMPTS = 10;
    int attempts = 0;
    CTime datePrev = dateCurrent;
    
    while (attempts < MAX_ATTEMPTS)
    {
        // 往前移动一天
        datePrev = datePrev - CTimeSpan(1, 0, 0, 0);
        
        // 检查是否为周末
        if (datePrev.GetDayOfWeek() == 1 || datePrev.GetDayOfWeek() == 7)
        {
            // 周末不是交易日，继续寻找
            continue;
        }

        // 格式化日期为字符串
        CString strDate;
        strDate.Format(_T("%04d-%02d-%02d"), datePrev.GetYear(), datePrev.GetMonth(), datePrev.GetDay());
        
        // 尝试加载该日期的数据
        if (LoadTimeData(std::string(m_strCode), std::string(strDate)))
        {
            // 找到有效的交易日数据，保存当前日期
            m_strCurrentDate = strDate;
            
            // 刷新界面
            Invalidate();
            
            // 成功找到并加载数据，退出
            break;
        }
        
        // 增加尝试次数
        attempts++;
    }
    
    // 如果达到最大尝试次数，可以显示提示信息
    if (attempts >= MAX_ATTEMPTS)
    {
        TRACE(_T("无法找到前一个交易日的数据\n"));
    }
}

// 切换到后一天数据
void CTimeLine::NavigateToNextDate()
{
    if (m_strCode.IsEmpty())
        return;
    
    // 获取当前日期，如果未指定，则使用当前系统日期
    CTime dateNow = CTime::GetCurrentTime();
    CTime dateCurrent;
    
    if (m_strCurrentDate.IsEmpty())
    {
        dateCurrent = dateNow;
    }
    else
    {
        // 将字符串转换为日期格式 (YYYY-MM-DD)
        int year, month, day;
        if (_stscanf_s(m_strCurrentDate, _T("%d-%d-%d"), &year, &month, &day) == 3)
        {
            dateCurrent = CTime(year, month, day, 0, 0, 0);
        }
        else
        {
            dateCurrent = dateNow;
        }
    }
    
    // 最多尝试查找10个交易日
    const int MAX_ATTEMPTS = 10;
    int attempts = 0;
    CTime dateNext = dateCurrent;
    
    while (attempts < MAX_ATTEMPTS)
    {
        // 往后移动一天
        dateNext = dateNext + CTimeSpan(1, 0, 0, 0);
        
        // 不能超过当前日期
        if (dateNext > dateNow)
        {
            TRACE(_T("到达当前日期，无法继续向前\n"));
            return;
        }
        
        // 检查是否为周末
        if (dateNext.GetDayOfWeek() == 1 || dateNext.GetDayOfWeek() == 7)
        {
            // 周末不是交易日，继续寻找
            continue;
        }
        
        // 格式化日期为字符串
        CString strDate;
        strDate.Format(_T("%04d-%02d-%02d"), dateNext.GetYear(), dateNext.GetMonth(), dateNext.GetDay());
        
        // 尝试加载该日期的数据
        if (LoadTimeData(std::string(m_strCode), std::string(strDate)))
        {
            // 找到有效的交易日数据，保存当前日期
            m_strCurrentDate = strDate;
            
            // 刷新界面
            Invalidate();
            
            // 成功找到并加载数据，退出
            break;
        }
        
        // 增加尝试次数
        attempts++;
    }
    
    // 如果达到最大尝试次数，可以显示提示信息
    if (attempts >= MAX_ATTEMPTS)
    {
        TRACE(_T("无法找到后一个交易日的数据\n"));
    }
}

// 视图销毁处理
void CTimeLine::OnDestroy()
{
    // 清理定时器
    KillTimer(TIMER_REALTIME_UPDATE);
    KillTimer(TIMER_REFRESH_DISPLAY);
    
    CView::OnDestroy();
}

// 定时器消息处理
void CTimeLine::OnTimer(UINT_PTR nIDEvent)
{
    switch (nIDEvent)
    {
    case TIMER_REALTIME_UPDATE:
        // 检查交易时段状态变化，必要时调整定时器频率
        {
            bool currentTradingStatus = IsTradingTime();
            if (currentTradingStatus != m_bLastTradingTimeStatus)
            {
                m_bLastTradingTimeStatus = currentTradingStatus;
                
                // 重新设置定时器频率
                KillTimer(TIMER_REALTIME_UPDATE);
                KillTimer(TIMER_REFRESH_DISPLAY);
                
                if (currentTradingStatus)
                {
                    // 切换到交易时段：更频繁的更新
                    SetTimer(TIMER_REALTIME_UPDATE, 2000, NULL);  // 每2秒检查一次数据更新
                    SetTimer(TIMER_REFRESH_DISPLAY, 500, NULL);   // 每0.5秒刷新一次界面
                    TRACE("切换到交易时段，启用频繁更新模式\n");
                }
                else
                {
                    // 切换到非交易时段：较慢的更新频率
                    SetTimer(TIMER_REALTIME_UPDATE, 10000, NULL); // 每10秒检查一次数据更新
                    SetTimer(TIMER_REFRESH_DISPLAY, 5000, NULL);  // 每5秒刷新一次界面
                    TRACE("切换到非交易时段，启用慢速更新模式\n");
                }
            }
        }
        
        // 实时数据更新检查
        if (!m_strCode.IsEmpty())
        {
            // 获取文档
            CStockDoc* pDoc = GetDocument();
            if (pDoc)
            {
                // 获取当前股票代码
                std::string currentStock = pDoc->GetCurrentStock();
                if (!currentStock.empty() && m_strCode == currentStock.c_str())
                {
                    // 获取股票索引
                    int stockIndex = pDoc->GetStockIndex(currentStock);
                    if (stockIndex >= 0)
                    {
                        const StockData* pStockData = pDoc->GetStock(stockIndex);
                        if (pStockData && !pStockData->_vecTimeLine.empty())
                        {
                            // 更新顶部信息数据
                            const TLINE_DATA& lastData = pStockData->_vecTimeLine.back();
                            
                            m_topInfoData.time = lastData._Time;
                            m_topInfoData.date = m_strCurrentDate;
                            m_topInfoData.price = lastData._Price;
                            m_topInfoData.preClose = pStockData->_preClose;
                            m_topInfoData.change = lastData._Price - pStockData->_preClose;
                            m_topInfoData.changePercent = (pStockData->_preClose > 0) ? 
                                ((lastData._Price / pStockData->_preClose - 1) * 100) : 0;
                            m_topInfoData.volume = lastData._Volume;
                            m_topInfoData.amount = lastData._Amount;
                            m_topInfoData.isValid = TRUE;
                            
                            // 标记需要重新计算数据
                            m_bNeedRecalcData = true;
                            m_chartData.dataValid = false;
                        }
                    }
                    
                    // 通知文档更新所有视图（这会触发OnUpdate）
                    pDoc->UpdateAllViews(this, 1);
                }
            }
        }
        break;
        
    case TIMER_REFRESH_DISPLAY:
        // 界面刷新 - 仅当数据有效时刷新
        if (!m_strCode.IsEmpty() && m_bNeedRecalcData)
        {
            // 重新绘制界面
            Invalidate(FALSE);
        }
        break;
    }
    
    CView::OnTimer(nIDEvent);
}

// 判断是否为交易时段
bool CTimeLine::IsTradingTime() const
{
    CTime now = CTime::GetCurrentTime();
    int hour = now.GetHour();
    int minute = now.GetMinute();
    int dayOfWeek = now.GetDayOfWeek();
    
    // 判断是否为交易时段（周一至周五，9:30-11:30, 13:00-15:00）
    return (dayOfWeek >= 2 && dayOfWeek <= 6) &&  // 周一至周五
           (((hour == 9 && minute >= 30) || (hour == 10) || (hour == 11 && minute <= 30)) ||
            ((hour == 13) || (hour == 14) || (hour == 15 && minute == 0)));
}

// 显示/隐藏涨跌停价格坐标
void CTimeLine::ShowLimitPriceCoordinates(BOOL bShow)
{
    if (m_bShowLimitPriceCoordinates != bShow)
    {
        m_bShowLimitPriceCoordinates = bShow;
        Invalidate(); // 刷新视图
    }
}

// 切换涨跌停价格坐标显示状态
void CTimeLine::ToggleLimitPriceCoordinates()
{
    // 切换状态
    m_bShowLimitPriceCoordinates = !m_bShowLimitPriceCoordinates;
    
    // 添加TRACE信息，方便调试
    TRACE("涨停板坐标模式%s\n", m_bShowLimitPriceCoordinates ? "已启用" : "已禁用");
    
    // 标记需要重新计算数据
    m_bNeedRecalcData = true;
    m_chartData.dataValid = false;
    
    // 刷新显示
    Invalidate();
}

// 鼠标滚轮事件处理
BOOL CTimeLine::OnMouseWheel(UINT nFlags, short zDelta, CPoint pt)
{
    // 获取文档
    CStockDoc* pDoc = GetDocument();
    if (!pDoc)
        return FALSE;
    
    // 转换滚轮消息为股票切换操作
    if (zDelta > 0)
    {
        // 向上滚动，切换到上一支股票
        NavigateToPreviousStock();
    }
    else
    {
        // 向下滚动，切换到下一支股票
        NavigateToNextStock();
    }
    
    return TRUE; // 返回TRUE表示已处理该消息
}

// 处理鼠标左键双击事件
void CTimeLine::OnLButtonDblClk(UINT nFlags, CPoint point)
{
    // 获取主框架
    CMainFrame* pMainFrame = (CMainFrame*)AfxGetMainWnd();
    if (pMainFrame)
    {
        // 切换到K线视图（技术分析视图）
        pMainFrame->SwitchView(pMainFrame->m_nKLineViewID);
    }
    
    CView::OnLButtonDblClk(nFlags, point);
}

// 绘制十字光标
void CTimeLine::DrawCrossCursor(CDC* pDC)
{
    // 检查光标位置是否在有效的图表区域内（包括集合竞价区域）
    bool inPriceArea = m_rcPriceArea.PtInRect(m_ptCrossCursor);
    bool inVolumeArea = m_rcVolumeArea.PtInRect(m_ptCrossCursor);
    bool inAuctionArea = m_bShowAuctionArea && m_rcAuctionArea.PtInRect(m_ptCrossCursor);
    
    if (!inPriceArea && !inVolumeArea && !inAuctionArea)
        return;
    
    // 使用白色细实线画笔绘制十字光标
    CPen penCursor(PS_SOLID, 1, RGB(255, 255, 255)); // 白色细实线
    CPen* pOldPen = pDC->SelectObject(&penCursor);
    
    // 保存旧的绘图模式，设置为R2_XORPEN，使光标能够清晰显示在各种背景上
    int nOldDrawMode = pDC->SetROP2(R2_XORPEN);
    
    // 绘制水平线 - 根据光标所在区域绘制
    if (inPriceArea)
    {
        // 在价格区域内，只绘制价格区域内的水平线
        pDC->MoveTo(m_rcPriceArea.left, m_ptCrossCursor.y);
        pDC->LineTo(m_rcPriceArea.right, m_ptCrossCursor.y);
        
        // 如果显示集合竞价区且集合竞价区不为空，在集合竞价区内也画水平线
        if (m_bShowAuctionArea && !m_rcAuctionArea.IsRectEmpty())
        {
            pDC->MoveTo(m_rcAuctionArea.left, m_ptCrossCursor.y);
            pDC->LineTo(m_rcAuctionArea.right, m_ptCrossCursor.y);
        }
    }
    else if (inAuctionArea)
    {
        // 在集合竞价区域内时，同时绘制集合竞价区域和价格区域的水平线
        pDC->MoveTo(m_rcAuctionArea.left, m_ptCrossCursor.y);
        pDC->LineTo(m_rcAuctionArea.right, m_ptCrossCursor.y);
        
        // 延伸至价格区域
        pDC->MoveTo(m_rcPriceArea.left, m_ptCrossCursor.y);
        pDC->LineTo(m_rcPriceArea.right, m_ptCrossCursor.y);
    }
    else if (inVolumeArea)
    {
        // 在成交量区域内，绘制成交量区域内的水平线
        pDC->MoveTo(m_rcVolumeArea.left, m_ptCrossCursor.y);
        pDC->LineTo(m_rcVolumeArea.right, m_ptCrossCursor.y);
        
        // 如果显示集合竞价区且集合竞价区不为空，也在集合竞价区对应的成交量高度绘制水平线
        if (m_bShowAuctionArea && !m_rcAuctionArea.IsRectEmpty())
        {
            int auctionVolumeLeft = m_rcAuctionArea.left;
            int auctionVolumeRight = m_rcAuctionArea.right;
            pDC->MoveTo(auctionVolumeLeft, m_ptCrossCursor.y);
            pDC->LineTo(auctionVolumeRight, m_ptCrossCursor.y);
        }
    }
    
    // 绘制垂直线 - 仅当光标在有效区域内时才绘制
    if (inPriceArea || inAuctionArea)
    {
        // 如果在价格区域或集合竞价区域内，垂直线贯穿整个分时图
        int yTop = m_rcPriceArea.top;
        int yBottom = m_rcVolumeArea.bottom;
        
        pDC->MoveTo(m_ptCrossCursor.x, yTop);
        pDC->LineTo(m_ptCrossCursor.x, yBottom);
    }
    else if (inVolumeArea)
    {
        // 如果在成交量区域内，垂直线贯穿整个分时图
        int yTop = m_rcPriceArea.top;
        int yBottom = m_rcVolumeArea.bottom;
        
        pDC->MoveTo(m_ptCrossCursor.x, yTop);
        pDC->LineTo(m_ptCrossCursor.x, yBottom);
    }
    
    // 恢复绘图模式
    pDC->SetROP2(nOldDrawMode);
    pDC->SelectObject(pOldPen);
    
    // 检查光标数据是否有效
    if (!m_bCursorDataValid)
        return;
    
    // 保存原来的背景模式，设置为透明，以便正确显示带有背景色的文本
    int oldBkMode = pDC->SetBkMode(OPAQUE);
    
    // 创建标签字体
    CFont labelFont;
    labelFont.CreateFont(22, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0,
                        ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                        DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
    
    CFont* pOldFont = pDC->SelectObject(&labelFont);
    
    // 1. 显示左侧价格标签和右侧百分比标签
    if (inPriceArea || inAuctionArea)
    {
        // 计算显示价格 - 根据鼠标Y坐标计算对应价格
        float price = 0.0f;
        
        if (m_bShowLimitPriceCoordinates)
        {
            // 使用涨停板坐标系，价格范围从跌停到涨停
            float priceAreaHeight = static_cast<float>(m_rcPriceArea.Height());
            float midY = m_rcPriceArea.top + priceAreaHeight / 2; // 昨收价的位置（中点）
            
            if (m_ptCrossCursor.y <= midY) {
                // 上半部分：昨收 - 涨停
                float percent = (midY - m_ptCrossCursor.y) / (priceAreaHeight / 2);
                price = m_chartData.preClose * (1.0f + 0.1f * percent);
            } else {
                // 下半部分：昨收 - 跌停
                float percent = (m_ptCrossCursor.y - midY) / (priceAreaHeight / 2);
                price = m_chartData.preClose * (1.0f - 0.1f * percent);
            }
        }
        else
        {
            // 使用自动缩放坐标系，根据显示范围计算
            float priceAreaHeight = static_cast<float>(m_rcPriceArea.Height());
            float pixelToPrice = m_chartData.priceRange / priceAreaHeight;
            float topToY = m_ptCrossCursor.y - m_rcPriceArea.top;
            
            price = m_chartData.maxPrice - pixelToPrice * topToY;
        }
        
        // 计算涨跌幅百分比
        float percent = 0.0f;
        if (m_chartData.preClose > 0)
            percent = (price - m_chartData.preClose) / m_chartData.preClose * 100.0f;
        
        // 确定颜色 - 根据涨跌设置不同颜色
        COLORREF textColor;
        if (price > m_chartData.preClose)
            textColor = RGB(255, 50, 50); // 红色表示上涨
        else if (price < m_chartData.preClose)
            textColor = RGB(0, 230, 0);   // 绿色表示下跌
        else
            textColor = RGB(255, 255, 255); // 白色表示平盘
        
        // 格式化并显示价格和百分比
        CString priceStr, percentStr;
        priceStr.Format(_T("%.2f"), price);
        
        if (percent > 0)
            percentStr.Format(_T("+%.2f%%"), percent);
        else if (percent < 0)
            percentStr.Format(_T("%.2f%%"), percent);
        else
            percentStr = _T("0.00%");
        
        // 计算价格标签的位置和大小
        CSize priceSize = pDC->GetTextExtent(priceStr);
        CRect rcPriceLabel(
            m_rcLeftPrice.right - priceSize.cx - 10,
            m_ptCrossCursor.y - priceSize.cy / 2,
            m_rcLeftPrice.right + 5,
            m_ptCrossCursor.y + priceSize.cy / 2
        );
        
        // 计算百分比标签的位置和大小
        CSize percentSize = pDC->GetTextExtent(percentStr);
        CRect rcPercentLabel(
            m_rcRightPercent.left - 5,
            m_ptCrossCursor.y - percentSize.cy / 2,
            m_rcRightPercent.left + percentSize.cx + 10,
            m_ptCrossCursor.y + percentSize.cy / 2
        );
        
        // 设置文本和背景颜色
        pDC->SetTextColor(textColor);
        pDC->SetBkColor(RGB(0, 0, 90)); // 深邃的蓝色背景
        
        // 绘制左侧价格标签
        pDC->FillSolidRect(&rcPriceLabel, RGB(0, 0, 90));
        pDC->TextOut(rcPriceLabel.right - priceSize.cx - 5, rcPriceLabel.top, priceStr);
        
        // 绘制右侧百分比标签
        pDC->FillSolidRect(&rcPercentLabel, RGB(0, 0, 90));
        pDC->TextOut(rcPercentLabel.left + 5, rcPercentLabel.top, percentStr);
    }
    
    // 2. 显示成交量标签（仅当光标在成交量区域时）
    if (inVolumeArea)
    {
        // 计算成交量值 - 根据鼠标Y坐标计算对应成交量
        float volumeAreaHeight = static_cast<float>(m_rcVolumeArea.Height());
        float pixelToVolume = m_chartData.maxVolume / volumeAreaHeight;
        float bottomToY = m_rcVolumeArea.bottom - m_ptCrossCursor.y;
        
        // 计算成交量值
        double volume = pixelToVolume * bottomToY;
        
        // 格式化成交量字符串
        CString volumeStr;
        // 根据成交量大小使用不同的单位
        if (volume >= 1000000)
            volumeStr.Format(_T("%.1f百万"), volume / 1000000);
        else if (volume >= 100000)
            volumeStr.Format(_T("%.1f万"), volume / 10000);
        else
            volumeStr.Format(_T("%.0f"), volume);
        
        // 计算成交量标签的位置和大小
        CSize volumeSize = pDC->GetTextExtent(volumeStr);
        CRect rcVolumeLabel(
            m_rcLeftPrice.right - volumeSize.cx - 10,
            m_ptCrossCursor.y - volumeSize.cy / 2,
            m_rcLeftPrice.right + 5,
            m_ptCrossCursor.y + volumeSize.cy / 2
        );
        
        // 计算右侧成交量标签位置和大小
        CRect rcVolumeRightLabel(
            m_rcRightPercent.left - 5,
            m_ptCrossCursor.y - volumeSize.cy / 2,
            m_rcRightPercent.left + volumeSize.cx + 10,
            m_ptCrossCursor.y + volumeSize.cy / 2
        );
        
        // 设置文本和背景颜色
        pDC->SetTextColor(RGB(0, 191, 255)); // 亮蓝色
        pDC->SetBkColor(RGB(0, 0, 90)); // 深邃的蓝色背景
        
        // 绘制左侧成交量标签
        pDC->FillSolidRect(&rcVolumeLabel, RGB(0, 0, 90));
        pDC->TextOut(rcVolumeLabel.right - volumeSize.cx - 5, rcVolumeLabel.top, volumeStr);
        
        // 绘制右侧成交量标签（显示相同的值）
        pDC->FillSolidRect(&rcVolumeRightLabel, RGB(0, 0, 90));
        pDC->TextOut(rcVolumeRightLabel.left + 5, rcVolumeRightLabel.top, volumeStr);
    }
    
    // 3. 在底部时间坐标区显示时间标签
    if (m_bCursorDataValid && !m_rcBottomTime.IsRectEmpty() && (inPriceArea || inVolumeArea || inAuctionArea))
    {
        // 格式化时间字符串
        int time = m_cursorData._Time;
        int hour = time / 100;
        int minute = time % 100;
        CString timeStr;
        timeStr.Format(_T("%02d:%02d"), hour, minute);
        
        // 计算时间标签的位置和大小
        CSize timeSize = pDC->GetTextExtent(timeStr);
        
        // 标签位置：垂直居中于底部时间坐标区，水平位置与十字线对齐
        CRect rcTimeLabel(
            m_ptCrossCursor.x - timeSize.cx / 2 - 5,
            m_rcBottomTime.top + (m_rcBottomTime.Height() - timeSize.cy) / 2,
            m_ptCrossCursor.x + timeSize.cx / 2 + 5,
            m_rcBottomTime.top + (m_rcBottomTime.Height() + timeSize.cy) / 2
        );
        
        // 确保标签不超出底部区域边界
        if (rcTimeLabel.left < m_rcBottomTime.left + 5)
            rcTimeLabel.OffsetRect(m_rcBottomTime.left + 5 - rcTimeLabel.left, 0);
        if (rcTimeLabel.right > m_rcBottomTime.right - 5)
            rcTimeLabel.OffsetRect(m_rcBottomTime.right - 5 - rcTimeLabel.right, 0);
        
        // 设置文本和背景颜色
        pDC->SetTextColor(RGB(0, 191, 255)); // 亮蓝色
        pDC->SetBkColor(RGB(0, 0, 90)); // 深邃的蓝色背景
        
        // 绘制时间标签
        pDC->FillSolidRect(&rcTimeLabel, RGB(0, 0, 90));
        pDC->TextOut(rcTimeLabel.left + (rcTimeLabel.Width() - timeSize.cx) / 2, rcTimeLabel.top, timeStr);
        
        // 绘制一条垂直线连接十字光标和时间标签
        pDC->SetROP2(R2_XORPEN); // 重新设为异或模式
        pDC->SelectObject(&penCursor);
        
        // 从主图表区域底部到时间标签顶部绘制垂直线
        pDC->MoveTo(m_ptCrossCursor.x, m_rcVolumeArea.bottom);
        pDC->LineTo(m_ptCrossCursor.x, rcTimeLabel.top);
        
        // 恢复绘图模式
        pDC->SetROP2(nOldDrawMode);
        pDC->SelectObject(pOldPen);
    }
    
    // 恢复设置
    pDC->SetBkMode(oldBkMode);
    pDC->SelectObject(pOldFont);
}

// 绘制光标信息提示
void CTimeLine::DrawCursorInfo(CDC* pDC)
{
    // 如果光标数据无效，直接返回
    if (!m_bCursorDataValid)
        return;
    
    // 设置文本颜色和背景模式
    pDC->SetTextColor(RGB(255, 255, 0)); // 黄色文本
    pDC->SetBkMode(TRANSPARENT);
    
    // 创建信息显示用的字体
    CFont infoFont;
    infoFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0,
                       ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                       DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
    CFont* pOldFont = pDC->SelectObject(&infoFont);
    
    // 在光标位置显示时间、价格和成交量信息
    CString strInfo;
    
    // 格式化时间信息: HHMM -> HH:MM
    int time = m_cursorData._Time;
    int hour = time / 100;
    int minute = time % 100;
    
    // 格式化价格、涨跌幅信息
    float changePercent = 0.0f;
    if (m_chartData.preClose > 0)
        changePercent = (m_cursorData._Price - m_chartData.preClose) / m_chartData.preClose * 100;
    
    // 完整信息字符串
    strInfo.Format(_T("%02d:%02d  价格:%.2f  涨幅:%.2f%%  成交量:%.0f"),
                 hour, minute, m_cursorData._Price, changePercent, m_cursorData._Volume);
    
    // 计算文本尺寸，用于定位显示位置
    CSize textSize = pDC->GetTextExtent(strInfo);
    
    // 创建信息背景矩形
    CRect rcInfo(m_ptCrossCursor.x + 10, m_ptCrossCursor.y - 30,
                m_ptCrossCursor.x + 10 + textSize.cx + 10, m_ptCrossCursor.y);
    
    // 确保信息框不超出图表区域
    if (rcInfo.right > m_rcMainChart.right)
        rcInfo.OffsetRect(-(rcInfo.right - m_rcMainChart.right) - 20, 0);
    
    // 绘制半透明背景
    COLORREF clrBackground = RGB(0, 0, 128); // 深蓝色背景
    CBrush brushBg(clrBackground);
    pDC->FillRect(&rcInfo, &brushBg);
    
    // 绘制边框
    CPen penBorder(PS_SOLID, 1, RGB(255, 255, 0));
    CPen* pOldPen = pDC->SelectObject(&penBorder);
    pDC->Rectangle(&rcInfo);
    pDC->SelectObject(pOldPen);
    
    // 绘制文本
    pDC->TextOut(rcInfo.left + 5, rcInfo.top + 5, strInfo);
    
    // 恢复字体
    pDC->SelectObject(pOldFont);
}

// 获取鼠标位置对应的分时数据
BOOL CTimeLine::GetDataAtPoint(const CPoint& point, TLINE_DATA& data)
{
    // 检查数据是否有效
    if (!m_chartData.dataValid)
        return FALSE;
    
    // 检查点是否在有效区域内（包括集合竞价区域）
    if (!m_rcPriceArea.PtInRect(point) && 
        !m_rcVolumeArea.PtInRect(point) && 
        !(m_bShowAuctionArea && m_rcAuctionArea.PtInRect(point)))
        return FALSE;
    
    // 获取文档
    CStockDoc* pDoc = GetDocument();
    if (!pDoc)
        return FALSE;
    
    // 获取股票索引
    int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
    if (stockIndex < 0)
        return FALSE;
    
    // 获取股票数据
    const StockData* pStockData = pDoc->GetStock(stockIndex);
    if (!pStockData || pStockData->_vecTimeLine.empty())
        return FALSE;
    
    // 获取分时数据
    const std::vector<TLINE_DATA>& timeLineData = pStockData->_vecTimeLine;
    
    // 1. 根据X坐标转换为时间点 - 使用与DrawBottomTime相同的映射逻辑
    int time = -1; // 初始化为无效时间
    
    // 交易时间刻度对应的分钟数（相对于9:00开始计算）
    int timePoints[] = {
        9*60+15,  // 9:15
        9*60+20,  // 9:20
        9*60+30,  // 9:30
        10*60+0,  // 10:00
        10*60+30, // 10:30
        11*60+0,  // 11:00
        11*60+30, // 11:30
        13*60+30, // 13:30
        14*60+0,  // 14:00
        14*60+30, // 14:30
        15*60+0   // 15:00
    };
    
    // 计算点击位置对应的时间
    if (m_bShowAuctionArea && point.x <= m_rcAuctionArea.right)
    {
        // 在集合竞价区域内
        if (point.x <= m_rcAuctionArea.left + m_rcAuctionArea.Width() / 2)
        {
            // 前半部分 - 9:15到9:20之间
            float ratio = (float)(point.x - m_rcAuctionArea.left) / (float)(m_rcAuctionArea.Width() / 2);
            int minutes = (int)(ratio * (timePoints[1] - timePoints[0]) + 0.5f);
            time = timePoints[0] + minutes;
        }
        else
        {
            // 后半部分 - 9:20到9:30之间
            float ratio = (float)(point.x - (m_rcAuctionArea.left + m_rcAuctionArea.Width() / 2)) / (float)(m_rcAuctionArea.Width() / 2);
            int minutes = (int)(ratio * (timePoints[2] - timePoints[1]) + 0.5f);
            time = timePoints[1] + minutes;
        }
    }
    else
    {
        // 在价格区域内
        int startX = m_bShowAuctionArea ? m_rcAuctionArea.right : m_rcPriceArea.left;
        int endX = m_rcPriceArea.right;
        int totalWidth = endX - startX;
        
        // 计算相对位置 (0.0-1.0)
        float relativePos = (float)(point.x - startX) / (float)totalWidth;
        
        // 价格区域中的时间范围是9:30到15:00，共330分钟，但要排除中午休市的时间
        // 计算实际的交易分钟数：上午交易9:30-11:30(120分钟)，下午交易13:00-15:00(120分钟)
        int tradingMinutes = (11*60+30 - 9*60-30) + (15*60 - 13*60);
        
        // 根据相对位置计算分钟数
        int minuteOffset = (int)(relativePos * tradingMinutes + 0.5f);
        
        // 将分钟数转换为实际时间
        if (minuteOffset <= 120) {
            // 上午交易时段：9:30-11:30
            time = 9*60+30 + minuteOffset;
        } else {
            // 下午交易时段：13:00-15:00
            time = 13*60 + (minuteOffset - 120);
        }
    }
    
    // 将分钟时间转换为小时和分钟
    int hour = time / 60;
    int minute = time % 60;
    int timeValue = hour * 100 + minute; // 格式化为HHMM格式
    
    // 2. 根据计算出的时间值，查找最接近的数据点
    int bestIndex = -1;
    int minTimeDiff = INT_MAX;
    
    for (size_t i = 0; i < timeLineData.size(); i++)
    {
        // 计算时间差的绝对值
        int timeDiff = abs(timeLineData[i]._Time - timeValue);
        
        // 如果找到更接近的时间点，更新最佳索引
        if (timeDiff < minTimeDiff)
        {
            minTimeDiff = timeDiff;
            bestIndex = (int)i;
            
            // 如果完全匹配，直接使用该点
            if (timeDiff == 0)
                break;
        }
    }
    
    // 如果找到了有效的最接近点，返回其数据
    if (bestIndex >= 0 && bestIndex < (int)timeLineData.size())
    {
        data = timeLineData[bestIndex];
        return TRUE;
    }
    
    return FALSE;
}

// 鼠标移动消息处理
void CTimeLine::OnMouseMove(UINT nFlags, CPoint point)
{
    // 如果十字光标固定，则不更新位置
    if (m_bCrossCursorFixed)
    {
        CView::OnMouseMove(nFlags, point);
        return;
    }
    
    // 如果十字光标处于显示状态，更新位置
    if (m_bShowCrossCursor)
    {
        // 检查鼠标是否在有效区域内
        bool inValidArea = m_rcPriceArea.PtInRect(point) || 
                           m_rcVolumeArea.PtInRect(point) || 
                           (m_bShowAuctionArea && m_rcAuctionArea.PtInRect(point));
        
        if (inValidArea) 
        {
            // 保存旧的光标位置
            CPoint oldPoint = m_ptCrossCursor;
            
            // 更新光标位置
            m_ptCrossCursor = point;
            
            // 获取光标位置对应的数据
            m_bCursorDataValid = GetDataAtPoint(point, m_cursorData);
            
            // 刷新视图以更新十字光标
            InvalidateRect(NULL, FALSE);
        }
    }
    
    CView::OnMouseMove(nFlags, point);
}

// 鼠标左键按下消息处理
void CTimeLine::OnLButtonDown(UINT nFlags, CPoint point)
{
    // 检查点击位置是否在有效区域内（包括集合竞价区域）
    if (m_rcPriceArea.PtInRect(point) || 
        m_rcVolumeArea.PtInRect(point) || 
        (m_bShowAuctionArea && m_rcAuctionArea.PtInRect(point)))
    {
        // 显示十字光标并设置位置
        m_bShowCrossCursor = TRUE;
        m_ptCrossCursor = point;
        m_bCursorDataValid = GetDataAtPoint(point, m_cursorData);
        
        // 刷新视图
        InvalidateRect(NULL, FALSE);
    }
    
    CView::OnLButtonDown(nFlags, point);
}

// 鼠标左键抬起消息处理
void CTimeLine::OnLButtonUp(UINT nFlags, CPoint point)
{
    CView::OnLButtonUp(nFlags, point);
}

// 显示/隐藏十字光标
void CTimeLine::ShowCrossCursor(BOOL bShow)
{
    if (m_bShowCrossCursor != bShow)
    {
        m_bShowCrossCursor = bShow;
        if (!bShow)
            m_bCrossCursorFixed = FALSE;
        Invalidate();
    }
}

// 切换十字光标显示状态
void CTimeLine::ToggleCrossCursor()
{
    ShowCrossCursor(!m_bShowCrossCursor);
}

// ==================== 集合竞价相关函数实现 ====================

// 检查文件是否存在
BOOL CTimeLine::FileIsExists(const CString& strFilePath)
{
    DWORD dwAttrib = GetFileAttributes(strFilePath);
    return (dwAttrib != INVALID_FILE_ATTRIBUTES && !(dwAttrib & FILE_ATTRIBUTE_DIRECTORY));
}

// 字符串分割函数
std::vector<std::string> CTimeLine::split(const std::string& str, const std::string& delim)
{
    std::vector<std::string> tokens;
    if (str.empty()) return tokens;
    
    size_t prev = 0, pos = 0;
    do {
        pos = str.find(delim, prev);
        if (pos == std::string::npos) pos = str.length();
        std::string token = str.substr(prev, pos - prev);
        if (!token.empty()) tokens.push_back(token);
        prev = pos + delim.length();
    } while (pos < str.length() && prev < str.length());
    
    return tokens;
}

// 四舍五入函数
float CTimeLine::RoundUp(float val, int n)
{
    float factor = pow(10.0f, n);
    return round(val * factor) / factor;
}

// 涨停价格计算
float CTimeLine::PriceLimitUp(const CString& strCode, float fPreClose)
{
    if (fPreClose <= 0) return 0.0f;
    
    // 根据股票代码判断涨停幅度
    CString code = strCode.Mid(0, 3);
    float limitRate = 0.10f; // 默认10%
    
    if (code == _T("300") || code == _T("301"))
        limitRate = 0.20f; // 创业板20%
    else if (code == _T("688") || code == _T("689"))
        limitRate = 0.20f; // 科创板20%
    else if (code == _T("430") || code == _T("830") || code == _T("831") || code == _T("832") || code == _T("833") || code == _T("834") || code == _T("835") || code == _T("836") || code == _T("837") || code == _T("838") || code == _T("839"))
        limitRate = 0.30f; // 北交所30%
    
    return RoundUp(fPreClose * (1 + limitRate), 2);
}

// 跌停价格计算
float CTimeLine::PriceLimitDown(const CString& strCode, float fPreClose)
{
    if (fPreClose <= 0) return 0.0f;
    
    // 根据股票代码判断跌停幅度
    CString code = strCode.Mid(0, 3);
    float limitRate = 0.10f; // 默认10%
    
    if (code == _T("300") || code == _T("301"))
        limitRate = 0.20f; // 创业板20%
    else if (code == _T("688") || code == _T("689"))
        limitRate = 0.20f; // 科创板20%
    else if (code == _T("430") || code == _T("830") || code == _T("831") || code == _T("832") || code == _T("833") || code == _T("834") || code == _T("835") || code == _T("836") || code == _T("837") || code == _T("838") || code == _T("839"))
        limitRate = 0.30f; // 北交所30%
    
    return RoundUp(fPreClose * (1 - limitRate), 2);
}

// 从本地获取集合竞价数据
BOOL CTimeLine::GetStockAucInfo(DWORD dwDate, CString strCode, float fOpen, AUCEX& _AucEx, std::vector<AUC_TIME>& vecAT, float& fPreClose)
{
    CString strAucFile;
    DWORD dwRead;
    CTime T1(dwDate);
    CString strDate = T1.Format(_T("%Y%m%d"));

    strAucFile.Format(_T("F:\\Market\\AUC\\%s\\%s.csv"), strDate, strCode);

    // 集合竞价文件是否存在
    if (!FileIsExists(strAucFile))
        return FALSE;

    _AucEx._Date = dwDate;

    HANDLE hAucFile = CreateFile(strAucFile, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hAucFile == INVALID_HANDLE_VALUE)
        return FALSE;

    double dLastAucLimit = 0;
    DWORD dwSize = GetFileSize(hAucFile, NULL);
    if (dwSize == 0)
    {
        CloseHandle(hAucFile);
        return FALSE;
    }

    char* szBuff = new char[dwSize + 1];
    ReadFile(hAucFile, szBuff, dwSize, &dwRead, NULL);
    szBuff[dwSize] = '\0';

    std::vector<std::string> AA = split(szBuff, "\r\n");
    delete[] szBuff;
    CloseHandle(hAucFile);

    // 无集合竞价数据
    if ((AA.size() == 1) || (AA.size() == 0))
        return TRUE;

    float fAucOpen = 0;
    AUC_TIME AT;

    for (int n = 1; n < AA.size(); n++)
    {
        std::vector<std::string> BB = split(AA.at(n), ",");
        if (BB.size() < 23) continue; // 确保有足够的字段

        CString strTime = BB.at(0).c_str();
        CString strOpen = BB.at(1).c_str();
        CString strPreClose = BB.at(2).c_str();
        CString strVol = BB.at(8).c_str();
        CString strBuyVol1 = BB.at(10).c_str();
        CString strBuyPrice = BB.at(11).c_str();
        CString strBuyVol2 = BB.at(12).c_str();
        CString strSelVol2 = BB.at(22).c_str();

        float _fPreClose = (float)_ttof(strPreClose);
        float aucPrice = (float)_ttof(strBuyPrice);
        float fOpenPrice = (float)_ttof(strOpen);
        double fVolume = _ttof(strVol);
        double fBuyVol1 = _ttof(strBuyVol1);
        double fBuyVol2 = _ttof(strBuyVol2);
        double fSelVol2 = _ttof(strSelVol2);

        int a, b, c;
        if (_stscanf(strTime, _T("%d:%d:%d"), &a, &b, &c) != 3)
            continue;

        CTime T4(1976, 12, 30, a, b, c);
        CTime T3(1976, 12, 30, 0, 0, 0);
        int nTime = (int)(T4.GetTime() - T3.GetTime());

        if (nTime < 33900)  // 09:25:00之前
        {
            if (aucPrice != 0)
            {
                if (_fPreClose != 0)
                    fPreClose = _fPreClose;

                ZeroMemory(&AT, sizeof(AUC_TIME));
                AT._Time = nTime;
                AT._Price = aucPrice;
                AT._MatchedVol = (float)fBuyVol1;
                if (fBuyVol2 > 0)
                {
                    AT._Direction = 1;
                    AT._UnMatchedVol = (float)fBuyVol2;
                }
                else
                {
                    AT._Direction = 0;
                    AT._UnMatchedVol = (float)fSelVol2;
                }
                vecAT.push_back(AT);

                _AucEx._AucPrice = aucPrice;
                if ((_AucEx._MaxPrice == 0) && (_AucEx._MinPrice == 0))
                {
                    _AucEx._MaxPrice = _AucEx._AucPrice;
                    _AucEx._MinPrice = _AucEx._AucPrice;
                }
                else
                {
                    if (_AucEx._AucPrice > _AucEx._MaxPrice)
                        _AucEx._MaxPrice = _AucEx._AucPrice;
                    if (_AucEx._AucPrice < _AucEx._MinPrice)
                        _AucEx._MinPrice = _AucEx._AucPrice;
                }

                _AucEx._MatchedVol = fBuyVol1;
                if (_AucEx._MatchedVol > _AucEx._MaxVol)
                    _AucEx._MaxVol = _AucEx._MatchedVol;

                if (strBuyVol2 != _T("0"))
                    _AucEx._UnMatchedVol = fBuyVol2;
                else
                    _AucEx._UnMatchedVol = 0 - fSelVol2;
            }
        }
        else if (nTime >= 33900) // 09:25:00之后
        {
            if ((fOpenPrice != 0) && (fVolume != 0))
            {
                fAucOpen = fOpenPrice;
                _AucEx._OpenVol = fVolume;
            }
        }
    }
    return TRUE;
}

// 绘制集合竞价区域数据
void CTimeLine::DrawAucZone(CDC* pDC)
{
    if (!m_bShowAuctionArea || m_rcAuctionArea.IsRectEmpty())
        return;

    // 设置绘图模式
    pDC->SetBkMode(TRANSPARENT);

    // 创建画笔
    CPen RedPen(PS_SOLID, 2, RGB(153, 0, 0));
    CPen RedThinPen(PS_SOLID, 1, RGB(80, 0, 0));
    CPen BlackPen(PS_SOLID, 1, RGB(0, 0, 0));
    CPen WhitePen(PS_SOLID, 1, RGB(255, 255, 255));
    CPen RedPen2(PS_SOLID, 1, RGB(255, 0, 0));
    CPen GreenPen(PS_SOLID, 1, RGB(0, 255, 0));

    // 创建画刷
    CBrush fillBrush(RGB(20, 20, 20));

    if (m_strCode.IsEmpty())
        return;

    // 获取当前日期
    CTime currentTime = CTime::GetCurrentTime();
    DWORD dwCurrentDate = (DWORD)currentTime.GetTime();

    // 获取股票文档
    CStockDoc* pDoc = GetDocument();
    if (!pDoc)
        return;

    // 获取股票索引
    int stockIndex = pDoc->GetStockIndex(std::string(m_strCode));
    if (stockIndex < 0)
        return;

    // 获取股票数据
    const StockData* pStockData = pDoc->GetStock(stockIndex);
    if (!pStockData)
        return;

    // 获取集合竞价数据
    AUCEX _AucEx;
    std::vector<AUC_TIME> vecAT;
    ZeroMemory(&_AucEx, sizeof(AUCEX));
    float fPreClose = 0;

    if (!GetStockAucInfo(dwCurrentDate, m_strCode, pStockData->_Open, _AucEx, vecAT, fPreClose))
    {
        return;
    }

    // 如果没有昨收价，使用股票数据中的昨收价
    if (fPreClose == 0)
        fPreClose = pStockData->_preClose;

    // 计算比例尺
    float fLimit = PriceLimitUp(m_strCode, fPreClose);
    float fLoss = PriceLimitDown(m_strCode, fPreClose);
    float fMaxScale = RoundUp((fLimit - fPreClose) / fPreClose * 100, 2);

    // 计算绘制参数
    int auctionWidth = m_rcAuctionArea.Width();
    int auctionHeight = m_rcAuctionArea.Height();
    int priceHeight = m_rcPriceArea.Height();
    int volumeHeight = m_rcVolumeArea.Height();

    float nInter = (float)(auctionWidth - 5) / 599.0f; // 时间间隔像素比
    float nZeroLine = (float)(m_rcPriceArea.top + priceHeight / 2); // 中间基准线高度坐标

    // 计算最大委买额
    double dMaxVol = 0;
    for (size_t m = 0; m < vecAT.size(); m++)
    {
        AUC_TIME pAt = vecAT[m];
        double totalVol = pAt._MatchedVol + abs(pAt._UnMatchedVol);
        if (totalVol > dMaxVol)
            dMaxVol = totalVol;
    }
    dMaxVol = dMaxVol * 1.5; // 增加一些空间

    BOOL bStartDraw = FALSE;
    CPoint lastPoint;

    // 绘制撮合价格线
    for (size_t m = 0; m < vecAT.size(); m++)
    {
        AUC_TIME pAt = vecAT[m];
        int nMinute = pAt._Time - 33300; // 相对于9:15的秒数
        if (nMinute < 0) continue;

        int x = m_rcAuctionArea.left + 2 + (int)(nMinute * nInter);
        if (x > m_rcAuctionArea.right - 2) continue;

        if (pAt._Price != 0)
        {
            int y;
            if (pAt._Price == fPreClose)
            {
                y = (int)nZeroLine;
            }
            else if (pAt._Price > fPreClose)
            {
                float aa = (pAt._Price - fPreClose) / (fPreClose * fMaxScale / 100);
                y = (int)(nZeroLine - (priceHeight / 2) * aa);
            }
            else
            {
                float aa = (fPreClose - pAt._Price) / (fPreClose * fMaxScale / 100);
                y = (int)(nZeroLine + (priceHeight / 2) * aa);
            }

            // 限制y坐标在价格区域内
            y = max(m_rcPriceArea.top + 2, min(y, m_rcPriceArea.bottom - 2));

            // 绘制价格点
            CPen* pOldPen = pDC->SelectObject(&WhitePen);
            CBrush* pOldBrush = pDC->SelectObject(&fillBrush);

            pDC->Ellipse(x - 2, y - 2, x + 2, y + 2);

            // 绘制价格连线
            if (!bStartDraw)
            {
                bStartDraw = TRUE;
                pDC->MoveTo(x, y);
                lastPoint = CPoint(x, y);
            }
            else
            {
                pDC->LineTo(x, y);
                lastPoint = CPoint(x, y);
            }

            pDC->SelectObject(pOldPen);
            pDC->SelectObject(pOldBrush);
        }
    }

    // 绘制撮合量
    for (size_t m = 0; m < vecAT.size(); m++)
    {
        AUC_TIME pAt = vecAT[m];
        int nMinute = pAt._Time - 33300;
        if (nMinute < 0) continue;

        int x = m_rcAuctionArea.left + 2 + (int)(nMinute * nInter);
        if (x > m_rcAuctionArea.right - 2) continue;

        if (pAt._Price != 0 && dMaxVol > 0)
        {
            // 绘制撮合量（下方）
            double dVolume = pAt._MatchedVol / dMaxVol;
            int volumeBarHeight = (int)(volumeHeight * 0.8 * dVolume);
            int volumeY = m_rcVolumeArea.bottom - volumeBarHeight;

            CPen* pOldPen;
            if (pAt._Direction == 1)
            {
                pOldPen = pDC->SelectObject(&RedPen2);
            }
            else
            {
                pOldPen = pDC->SelectObject(&GreenPen);
            }

            pDC->MoveTo(x, m_rcVolumeArea.bottom - 1);
            pDC->LineTo(x, volumeY);

            // 绘制未撮合量（上方扩展）
            double dUnmatchedVol = abs(pAt._UnMatchedVol) / dMaxVol;
            int unmatchedBarHeight = (int)(volumeHeight * 0.2 * dUnmatchedVol);
            int unmatchedY = m_rcVolumeArea.top + unmatchedBarHeight;

            pDC->MoveTo(x, m_rcVolumeArea.top);
            pDC->LineTo(x, unmatchedY);

            pDC->SelectObject(pOldPen);
        }
    }
}

// 网络状态检查
bool CTimeLine::IsNetworkAvailable()
{
    // 使用InternetGetConnectedState检查网络连接状态
    DWORD dwFlags;
    return InternetGetConnectedState(&dwFlags, 0) != FALSE;
}

// 缓存数据加载
BOOL CTimeLine::LoadCachedData(const std::string& stockCode, const std::string& date)
{
    // 这里可以实现从本地缓存文件加载数据的逻辑
    // 目前简单返回FALSE，表示没有缓存数据可用
    TRACE(_T("LoadCachedData: 暂无缓存数据实现，股票代码: %hs, 日期: %hs\n"), 
          stockCode.c_str(), date.c_str());
    
    // TODO: 实现本地缓存文件读取
    // 1. 根据stockCode和date构造缓存文件路径
    // 2. 检查缓存文件是否存在且有效
    // 3. 读取缓存文件并解析为分时数据
    // 4. 更新到文档中的股票数据
    
    return FALSE;
}
