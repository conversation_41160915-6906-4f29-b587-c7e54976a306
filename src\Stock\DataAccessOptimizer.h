#pragma once
#include <unordered_map>
#include <mutex>
#include <string>
#include <vector>
#include "StockDef.h"

// 数据访问优化类，提供缓存优化
class DataAccessOptimizer {
private:
    // 使用普通互斥锁
    mutable std::mutex m_mutex;
    
    // 缓存常用查询结果
    mutable std::unordered_map<std::string, int> m_codeToIndexCache;
    mutable std::unordered_map<std::string, int> m_nameToIndexCache;
    mutable std::unordered_map<std::string, std::string> m_nameToCodeCache;
    
    // 缓存有效性标志
    mutable bool m_cacheValid = false;
    
    // 数据引用
    const std::vector<StockData>* m_pStockData = nullptr;
    const std::unordered_map<std::string, int>* m_pCodeToIndexMap = nullptr;
    const std::unordered_map<std::string, int>* m_pNameToIndexMap = nullptr;
    const std::unordered_map<std::string, std::string>* m_pNameToCodeMap = nullptr;
    
public:
    // 初始化数据引用
    void Initialize(const std::vector<StockData>* stockData,
                   const std::unordered_map<std::string, int>* codeToIndexMap,
                   const std::unordered_map<std::string, int>* nameToIndexMap,
                   const std::unordered_map<std::string, std::string>* nameToCodeMap) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_pStockData = stockData;
        m_pCodeToIndexMap = codeToIndexMap;
        m_pNameToIndexMap = nameToIndexMap;
        m_pNameToCodeMap = nameToCodeMap;
        InvalidateCache();
    }
    
    // 根据股票代码查找索引
    int FindStockIndexByCode(const std::string& stockCode) const {
        if (stockCode.empty() || !m_pCodeToIndexMap) return -1;

        std::lock_guard<std::mutex> lock(m_mutex);

        // 先尝试从缓存读取
        if (m_cacheValid) {
            auto it = m_codeToIndexCache.find(stockCode);
            if (it != m_codeToIndexCache.end()) {
                return it->second;
            }
        }

        // 从原始数据查找
        auto it = m_pCodeToIndexMap->find(stockCode);
        if (it != m_pCodeToIndexMap->end()) {
            // 更新缓存
            const_cast<DataAccessOptimizer*>(this)->m_codeToIndexCache[stockCode] = it->second;
            const_cast<DataAccessOptimizer*>(this)->m_cacheValid = true;
            return it->second;
        }

        return -1;
    }
    
    // 根据股票名称查找索引
    int FindStockIndexByName(const std::string& stockName) const {
        if (stockName.empty() || !m_pNameToIndexMap) return -1;

        std::lock_guard<std::mutex> lock(m_mutex);

        // 先尝试从缓存读取
        if (m_cacheValid) {
            auto it = m_nameToIndexCache.find(stockName);
            if (it != m_nameToIndexCache.end()) {
                return it->second;
            }
        }

        // 从原始数据查找
        auto it = m_pNameToIndexMap->find(stockName);
        if (it != m_pNameToIndexMap->end()) {
            // 更新缓存
            const_cast<DataAccessOptimizer*>(this)->m_nameToIndexCache[stockName] = it->second;
            const_cast<DataAccessOptimizer*>(this)->m_cacheValid = true;
            return it->second;
        }

        return -1;
    }
    
    // 获取股票数据
    const StockData* GetStock(int index) const {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (!m_pStockData || index < 0 || index >= static_cast<int>(m_pStockData->size())) {
            return nullptr;
        }
        return &(*m_pStockData)[index];
    }

    // 获取股票列表大小
    size_t GetStockCount() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_pStockData ? m_pStockData->size() : 0;
    }

    // 批量查找股票索引
    std::vector<int> FindStockIndicesByCodes(const std::vector<std::string>& stockCodes) const {
        std::vector<int> indices;
        indices.reserve(stockCodes.size());

        for (const auto& code : stockCodes) {
            indices.push_back(FindStockIndexByCode(code));
        }

        return indices;
    }

    // 使缓存失效
    void InvalidateCache() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_cacheValid = false;
        m_codeToIndexCache.clear();
    }

    // 获取缓存统计信息
    struct CacheStats {
        size_t cacheSize;
        bool isValid;
    };

    CacheStats GetCacheStats() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return {
            m_codeToIndexCache.size(),
            m_cacheValid
        };
    }
};
