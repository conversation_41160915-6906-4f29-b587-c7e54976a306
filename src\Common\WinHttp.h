﻿#pragma once
#include <map>
#include <string>
#include "httpHeader.h"

/**
 * @file WinHttp.h
 * @brief 基于WinInet的HTTP客户端实现
 * @details 提供HTTP/HTTPS请求、响应处理和GZIP解压功能
 */

// 定义GZIP检测和解压的宏，便于条件编译
#ifdef ZLIB_SUPPORT
#define HAS_GZIP_SUPPORT 0
#else
#define HAS_GZIP_SUPPORT 0  // 默认禁用，如需启用请改为1
#endif

/**
 * @enum HttpErrorCode
 * @brief HTTP请求过程中可能出现的错误代码
 */
enum HttpErrorCode {
	HTTP_ERROR_SUCCESS = 0,             ///< 操作成功
	HTTP_ERROR_INITIALIZE_FAILED = 1,   ///< 初始化失败
	HTTP_ERROR_CONNECT_FAILED = 2,      ///< 连接服务器失败
	HTTP_ERROR_SEND_FAILED = 3,         ///< 发送请求失败
	HTTP_ERROR_RECEIVE_FAILED = 4,      ///< 接收响应失败
	HTTP_ERROR_DECOMPRESS_FAILED = 5,   ///< 解压数据失败
	HTTP_ERROR_MEMORY_ALLOC_FAILED = 6, ///< 内存分配失败
	HTTP_ERROR_INVALID_PARAMETER = 7,   ///< 无效参数
	HTTP_ERROR_NOT_CONNECTED = 8,       ///< 未连接到服务器
	HTTP_ERROR_OPERATION_CANCELLED = 9, ///< 操作已取消
	HTTP_ERROR_TIMEOUT = 10,            ///< 操作超时
	HTTP_ERROR_UNKNOWN = 999            ///< 未知错误
};

/**
 * @class CWinHttp
 * @brief HTTP客户端类，提供HTTP/HTTPS请求支持
 * @details 封装了WinInet API，支持GET/POST/PUT/DELETE等HTTP方法
 *          提供GZIP压缩响应的自动解压、证书验证和错误处理
 */
class CWinHttp
{
public:
	/**
	 * @brief 构造函数
	 */
	CWinHttp(void);
	
	/**
	 * @brief 析构函数
	 */
	~CWinHttp(void);
	
	// WinInet句柄，对外暴露以便状态回调函数访问
	HINTERNET m_hRequest;              ///< 请求句柄
	
public:
	/**
	 * @brief 连接HTTP服务器
	 * @param lpUrl 服务器URL
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL ConnectHttpServer(LPCSTR lpUrl);
	
	/**
	 * @brief 发送HTTP请求
	 * @param pUrl 目标URL
	 * @param type 请求类型(GET/POST)
	 * @param strHtml 接收响应内容的字符串
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL Request(LPCSTR pUrl, HttpRequest type, CString& strHtml);
	
	/**
	 * @brief 发送POST请求
	 * @param pUrl 目标URL
	 * @param pPostData POST数据
	 * @param dwPostDataLen POST数据长度
	 * @param strHtml 接收响应内容的字符串
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL Post(LPCSTR pUrl, const char* pPostData, DWORD dwPostDataLen, CString& strHtml);
	
	/**
	 * @brief 发送PUT请求
	 * @param pUrl 目标URL
	 * @param pPutData PUT数据
	 * @param dwPutDataLen PUT数据长度
	 * @param strHtml 接收响应内容的字符串
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL Put(LPCSTR pUrl, const char* pPutData, DWORD dwPutDataLen, CString& strHtml);
	
	/**
	 * @brief 发送DELETE请求
	 * @param pUrl 目标URL
	 * @param strHtml 接收响应内容的字符串
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL Delete(LPCSTR pUrl, CString& strHtml);
	
	/**
	 * @brief 添加HTTP请求头
	 * @param key 头部名称
	 * @param value 头部值
	 */
	void AddHeader(LPCSTR key, LPCSTR value);
	
	/**
	 * @brief 设置HTTP请求超时
	 * @param dwConnectTimeout 连接超时(毫秒)
	 * @param dwSendTimeout 发送超时(毫秒)
	 * @param dwReceiveTimeout 接收超时(毫秒)
	 */
	void SetTimeout(DWORD dwConnectTimeout, DWORD dwSendTimeout, DWORD dwReceiveTimeout);
	
	/**
	 * @brief 获取完整HTTP响应头
	 * @return 响应头字符串
	 */
	CString GetResponseHeader();
	
	/**
	 * @brief 获取指定的HTTP响应头值
	 * @param strKey 头部名称
	 * @return 头部值字符串
	 */
	std::string GetResponseHeaderValue(const std::string& strKey);
	
	/**
	 * @brief 获取HTTP响应状态码
	 * @return HTTP状态码
	 */
	int GetResponseCode() { return m_nResponseCode; }
	
	/**
	 * @brief 解析URL
	 * @param lpUrl 输入URL
	 * @param strHostName 接收主机名
	 * @param strPage 接收页面路径
	 * @param sPort 接收端口号
	 */
	void ParseUrlA(LPCSTR lpUrl, string& strHostName, string& strPage, WORD& sPort);
	
	/**
	 * @brief 验证URL是否合法
	 * @param lpUrl 待验证的URL
	 * @return 合法返回TRUE，不合法返回FALSE
	 */
	BOOL ValidateUrl(LPCSTR lpUrl);
	
	/**
	 * @brief 关闭连接
	 */
	void Close();
	
	/**
	 * @brief 检查是否已连接
	 * @return 已连接返回TRUE，否则返回FALSE
	 */
	BOOL IsConnect();
	
	/**
	 * @brief 启用/禁用GZIP自动解压
	 * @param bEnable TRUE表示启用，FALSE表示禁用
	 */
	void EnableAutoGZip(BOOL bEnable) { m_bAutoGZip = bEnable; }
	
	/**
	 * @brief 检查是否启用GZIP自动解压
	 * @return 启用返回TRUE，否则返回FALSE
	 */
	BOOL IsAutoGZip() { return m_bAutoGZip; }
	
	/**
	 * @brief 获取最后一次错误代码
	 * @return 错误代码
	 */
	HttpErrorCode GetLastError() { return m_lastError; }
	
	/**
	 * @brief 获取最后一次错误的中文描述
	 * @return 错误描述字符串
	 */
	std::string GetLastErrorDescription();
	
	/**
	 * @brief 设置错误代码
	 * @param code 错误代码
	 */
	void SetLastError(HttpErrorCode code);
	
	/**
	 * @brief 启用/禁用证书验证
	 * @param bEnable TRUE表示启用，FALSE表示禁用
	 */
	void EnableCertificateValidation(BOOL bEnable) { m_bVerifyCert = bEnable; }
	
	/**
	 * @brief 检查是否启用证书验证
	 * @return 启用返回TRUE，否则返回FALSE
	 */
	BOOL IsCertificateValidationEnabled() { return m_bVerifyCert; }
protected:
	/**
	 * @brief 关闭HINTERNET句柄
	 * @param hInternet 待关闭的句柄
	 */
	void ReleaseHandle(HINTERNET& hInternet);
	
	/**
	 * @brief 发送HTTP请求(内部实现)
	 * @param lpMethod HTTP方法(GET/POST/PUT/DELETE等)
	 * @param lpUrl 目标URL
	 * @param pData 请求体数据
	 * @param dwDataLen 请求体数据长度
	 * @param strHtml 接收响应内容的字符串
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL SendHttpRequest(LPCSTR lpMethod, LPCSTR lpUrl, const char* pData, DWORD dwDataLen, CString& strHtml);
	
	/**
	 * @brief 查询HTTP状态码
	 * @return HTTP状态码
	 */
	int QueryStatusCode();
	
	/**
	 * @brief 检查数据是否为GZIP格式
	 * @param pBuffer 数据缓冲区
	 * @param dwLen 数据长度
	 * @return 是GZIP返回TRUE，否则返回FALSE
	 */
	BOOL IsGZipContent(const BYTE* pBuffer, DWORD dwLen);
	
	/**
	 * @brief 解压GZIP数据
	 * @param pGZipData GZIP压缩数据
	 * @param dwGZipDataLen 压缩数据长度
	 * @param pUnGZipData 接收解压数据的缓冲区
	 * @param dwUnGZipDataLen 输入缓冲区大小，输出实际解压大小
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL UnGZipData(const BYTE* pGZipData, DWORD dwGZipDataLen, BYTE* pUnGZipData, DWORD& dwUnGZipDataLen);
	
	/**
	 * @brief 处理GZIP响应
	 * @param pData 压缩数据
	 * @param dwDataLen 压缩数据长度
	 * @param strHtml 接收解压后内容的字符串
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL ProcessGZipResponse(const BYTE* pData, DWORD dwDataLen, CString& strHtml);
	
	/**
	 * @brief 获取指定类型的HTTP头值(内部方法)
	 * @param dwInfoLevel HTTP_QUERY_XXX标志
	 * @return 头部值字符串
	 */
	std::string GetHeaderValueInternal(DWORD dwInfoLevel);
	
	/**
	 * @brief 读取HTTP响应数据
	 * @param strHtml 接收内容的字符串
	 * @param bGZipContent 是否为GZIP压缩内容
	 * @return 成功返回TRUE，失败返回FALSE
	 */
	BOOL ReadResponseData(CString& strHtml, BOOL bGZipContent);
private:
	BOOL m_bConnect;                   ///< 连接状态
	int m_nResponseCode;               ///< HTTP响应状态码
	bool m_bHttps;                     ///< 是否为HTTPS连接
	HINTERNET m_hSession;              ///< Internet会话句柄
	HINTERNET m_hConnect;              ///< 连接句柄
	CHttpHeader m_header;              ///< HTTP头部管理器
	HttpParamsData m_paramsData;       ///< HTTP参数数据
	DWORD m_dwConnectTimeout;          ///< 连接超时(毫秒)
	DWORD m_dwSendTimeout;             ///< 发送超时(毫秒)
	DWORD m_dwReceiveTimeout;          ///< 接收超时(毫秒)
	CString m_strResponseHeader;       ///< 响应头缓存
	BOOL m_bAutoGZip;                  ///< 是否启用GZIP自动解压
	BOOL m_bVerifyCert;                ///< 是否验证证书
	HttpErrorCode m_lastError;         ///< 最后一次错误代码
	static const DWORD DEFAULT_BUFFER_SIZE = 4096; ///< 默认缓冲区大小
	static const DWORD MAX_BUFFER_SIZE = 10 * 1024 * 1024; ///< 最大缓冲区大小(10MB)
};
