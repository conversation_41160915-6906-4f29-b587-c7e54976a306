﻿// KLineView.cpp: CKLineView 类的实现
//

#include "pch.h"
#include "..\framework.h"
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "KLineView.h"
#include "..\MainFrm.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// CKLineView

IMPLEMENT_DYNCREATE(CKLineView, CView)

BEGIN_MESSAGE_MAP(CKLineView, CView)
	ON_WM_CREATE()
	ON_WM_SIZE()
	ON_WM_ERASEBKGND()
	ON_WM_KEYDOWN()
	ON_WM_TIMER()
END_MESSAGE_MAP()

// CKLineView 构造/析构

CKLineView::CKLineView() noexcept
{
	// 初始化视图ID
	m_nVolumeViewID = 0;
	m_nInfoViewID = 0;
	m_nIndicatorViewID = 0;
	m_nKLineSignalID = 0;
	m_nKLineTimeID = 0;
}

CKLineView::~CKLineView()
{
}

// 获取文档指针
CStockDoc* CKLineView::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 响应文档更新
void CKLineView::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 调用基类的OnUpdate方法
	CView::OnUpdate(pSender, lHint, pHint);
	
	// 获取当前股票代码
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;
		
	CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
	
	// 更新各个子视图
	UpdateChildViews(strCurrentStock);
}

// 更新所有子视图数据
void CKLineView::UpdateChildViews(const CString& strCode)
{
	// 检查股票代码是否有效
	if (strCode.IsEmpty())
		return;
		
	// 开始更新，显示等待光标
	CWaitCursor waitCursor;
	
	// 获取文档对象
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;
	
	// 只有当分割窗口已创建才进行更新
	if (m_wndSplitter2.GetSafeHwnd() && m_wndSplitter1.GetSafeHwnd())
	{
		// 更新KLineVolume视图
		if (m_nVolumeViewID > 0)
		{
			CKLineVolume* pVolumeView = (CKLineVolume*)m_wndSplitter2.GetView(m_nVolumeViewID);
			if (pVolumeView)
			{
				pVolumeView->SetStockCode(strCode);
				TRACE(_T("K线成交量视图股票代码已更新: %s\n"), strCode);
			}
		}
		
		// 更新技术指标视图
		if (m_nIndicatorViewID > 0)
		{
			CKLineIndicator* pIndicatorView = (CKLineIndicator*)m_wndSplitter2.GetView(m_nIndicatorViewID);
			if (pIndicatorView)
			{
				pIndicatorView->SetStockCode(strCode);
				TRACE(_T("技术指标视图股票代码已更新: %s\n"), strCode);
			}
		}
		
		// 更新历史分时图视图
		if (m_nKLineTimeID > 0)
		{
			CKLineTime* pTimeView = (CKLineTime*)m_wndSplitter2.GetView(m_nKLineTimeID);
			if (pTimeView)
			{
				pTimeView->SetStockCode(strCode);
				TRACE(_T("历史分时图视图股票代码已更新: %s\n"), strCode);
			}
		}
		
		// 更新信号视图
		if (m_nKLineSignalID > 0)
		{
			CKLineSignal* pSignalView = (CKLineSignal*)m_wndSplitter2.GetView(m_nKLineSignalID);
			if (pSignalView)
			{
				pSignalView->SetStockCode(strCode);
				TRACE(_T("信号视图股票代码已更新: %s\n"), strCode);
			}
		}
		
		// 更新KLineInfo视图
		if (m_nInfoViewID > 0)
		{
			CKLineInfo* pInfoView = (CKLineInfo*)m_wndSplitter1.GetView(m_nInfoViewID);
			if (pInfoView)
			{
				// 获取股票名称
				std::string name = pDoc->GetStockName(std::string(strCode));
				CString strName = name.c_str();
				
				pInfoView->SetStockInfo(strCode, strName);
				TRACE(_T("K线信息视图股票代码和名称已更新: %s (%s)\n"), strCode, strName);
			}
		}
		
		// 设置延迟重绘定时器，减少闪烁
		SetTimer(1001, 100, NULL);
	}
}

// 设置股票代码
void CKLineView::SetStockCode(const CString& strCode)
{
	UpdateChildViews(strCode);
}

BOOL CKLineView::PreCreateWindow(CREATESTRUCT& cs)
{
	// 去除边框样式
	cs.style &= ~WS_BORDER;
	return CView::PreCreateWindow(cs);
}

// 设置黑色背景
BOOL CKLineView::OnEraseBkgnd(CDC* pDC)
{
	CRect rect;
	GetClientRect(&rect);
	pDC->FillSolidRect(&rect, RGB(0, 0, 0));
	return TRUE;
}

// CKLineView 绘图

void CKLineView::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;
		
	// 分割视图模式下，这里不需要额外绘制内容
}

// 初始化视图
void CKLineView::OnInitialUpdate()
{
	CView::OnInitialUpdate();
	
	// 获取当前文档
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		// 获取当前股票代码
		CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
		
		// 如果当前有股票，立即更新视图
		if (!strCurrentStock.IsEmpty())
		{
			// 使用延时调用，确保所有视图已完全创建
			TRACE(_T("OnInitialUpdate: 设置延时更新当前股票: %s\n"), strCurrentStock);
			SetTimer(1002, 200, NULL);  // 200毫秒后更新，给视图创建留出时间
		}
	}
}

// 创建分割窗口
BOOL CKLineView::CreateSplitterWindow()
{
	// 获取客户区大小
	CRect rcClient;
	GetClientRect(&rcClient);
	
	// 检查客户区是否有效
	if (rcClient.Width() <= 0 || rcClient.Height() <= 0)
	{
		TRACE0("警告: 客户区尺寸无效，稍后尝试重新创建分割窗口\n");
		
		// 设置一个初始最小尺寸
		rcClient.right = rcClient.left + 600;
		rcClient.bottom = rcClient.top + 400;
	}
	
	TRACE2("CreateSplitterWindow: 客户区尺寸: 宽=%d, 高=%d\n", rcClient.Width(), rcClient.Height());
	
	// 创建创建上下文
	CCreateContext context;
	context.m_pNewViewClass = nullptr;
	context.m_pCurrentDoc = GetDocument();
	context.m_pNewDocTemplate = nullptr;
	context.m_pLastView = nullptr;
	context.m_pCurrentFrame = nullptr;
	
	// 创建左右分割的主分割窗口，移除WS_BORDER样式
	if (!m_wndSplitter1.CreateStatic(this, 1, 2, WS_CHILD | WS_VISIBLE))
	{
		TRACE0("Failed to create main splitter window\n");
		return FALSE;
	}

	// 设置左右分割比例，右侧固定宽度为300像素
	// 但确保左侧至少有200像素宽度，否则按照7:3比例分割
	int rightWidth = 300;
	int minLeftWidth = 200;  // 左侧最小宽度
	int leftWidth;
	
	if (rcClient.Width() >= (rightWidth + minLeftWidth))
	{
		// 窗口足够宽，可以使用固定宽度
		leftWidth = rcClient.Width() - rightWidth;
	}
	else
	{
		// 窗口宽度不足，按比例分割
		leftWidth = rcClient.Width() * 7 / 10;
		rightWidth = rcClient.Width() - leftWidth;
	}
	
	// 确保宽度不为负数
	leftWidth = max(10, leftWidth);
	rightWidth = max(10, rightWidth);
	
	TRACE2("CreateSplitterWindow: 设置宽度 - 左=%d, 右=%d\n", leftWidth, rightWidth);
	m_wndSplitter1.SetColumnInfo(0, leftWidth, 100);
	m_wndSplitter1.SetColumnInfo(1, rightWidth, 10); // 设置最小宽度为10，避免限制过严
	
	// 在左侧窗格中创建上下分割的子分割窗口，移除WS_BORDER样式
	if (!m_wndSplitter2.CreateStatic(&m_wndSplitter1, 2, 1, WS_CHILD | WS_VISIBLE, m_wndSplitter1.IdFromRowCol(0, 0)))
	{
		TRACE0("Failed to create nested splitter window\n");
		return FALSE;
	}

	// 设置上下分割比例，下侧占30%
	int topHeight = rcClient.Height() * 7 / 10;
	int bottomHeight = rcClient.Height() * 3 / 10;
	
	// 确保高度不会过小
	topHeight = max(100, topHeight);
	bottomHeight = max(100, bottomHeight);
	TRACE2("CreateSplitterWindow: 设置高度 - 上=%d, 下=%d\n", topHeight, bottomHeight);
	
	m_wndSplitter2.SetRowInfo(0, topHeight, 100);
	m_wndSplitter2.SetRowInfo(1, bottomHeight, 100);


	// 为子分割窗口创建视图 - 使用AddView方法
	// 左上视图 - K线和成交量视图
	m_nVolumeViewID = m_wndSplitter2.AddView(0, 0, RUNTIME_CLASS(CKLineVolume), &context);
	
	// 左下视图 - 技术指标视图
	m_nKLineSignalID	= m_wndSplitter2.AddView(1, 0, RUNTIME_CLASS(CKLineSignal), &context);
	m_nKLineTimeID		= m_wndSplitter2.AddView(1, 0, RUNTIME_CLASS(CKLineTime), &context);
	m_nIndicatorViewID	= m_wndSplitter2.AddView(1, 0, RUNTIME_CLASS(CKLineIndicator), &context);
	
	// 右侧视图 - 股票信息视图
	m_nInfoViewID = m_wndSplitter1.AddView(0, 1, RUNTIME_CLASS(CKLineInfo), &context);
	TRACE1("创建右侧信息视图，ID=%d\n", m_nInfoViewID);
	
	if (m_nInfoViewID <= 0) {
		TRACE0("警告: 右侧信息视图创建失败!\n");
		
		// 尝试重新创建右侧视图
		TRACE0("尝试重新创建右侧信息视图...\n");
		m_nInfoViewID = m_wndSplitter1.AddView(0, 1, RUNTIME_CLASS(CKLineInfo), &context);
		TRACE1("重新创建右侧信息视图，ID=%d\n", m_nInfoViewID);
	}
	
	// 强制重新计算布局，确保右侧视图可见
	m_wndSplitter1.RecalcLayout();
	
	// 保存视图指针以便后续使用
	CKLineVolume* pVolumeView = NULL;
	CKLineIndicator* pIndicatorView = NULL;
	CKLineInfo* pInfoView = NULL;
	
	// 安全检查，确保分割窗口已创建
	if (m_wndSplitter2.GetSafeHwnd())
	{
		pVolumeView = (CKLineVolume*)m_wndSplitter2.GetPane(0, 0);
		pIndicatorView = (CKLineIndicator*)m_wndSplitter2.GetPane(1, 0);
		
		// 检查是否成功获取视图指针
		if (pVolumeView == NULL || pIndicatorView == NULL)
		{
			TRACE0("警告: 无法获取视图指针!\n");
		}
	}
	
	if (m_wndSplitter1.GetSafeHwnd())
	{
		pInfoView = (CKLineInfo*)m_wndSplitter1.GetPane(0, 1);
		
		if (pInfoView == NULL)
		{
			TRACE0("警告: 无法获取信息视图指针!\n");
		}
	}
	
	// 调整分割线位置
	m_wndSplitter1.RecalcLayout();
	m_wndSplitter2.RecalcLayout();
	
	// 确保设置为7:3的分割比例
	m_wndSplitter2.SetRowInfo(0, topHeight, 100);
	m_wndSplitter2.SetRowInfo(1, bottomHeight, 100);
	
	// 默认显示技术指标视图
	m_wndSplitter2.SwitchView(m_nIndicatorViewID);
	
	// 重新计算布局
	m_wndSplitter1.RecalcLayout();
	m_wndSplitter2.RecalcLayout();
	
	// 检查分割窗口状态
	int cyCur, cyMin;
	m_wndSplitter2.GetRowInfo(0, cyCur, cyMin);
	TRACE2("分割窗口2行0状态: 当前高度=%d, 最小高度=%d\n", cyCur, cyMin);
	m_wndSplitter2.GetRowInfo(1, cyCur, cyMin);
	TRACE2("分割窗口2行1状态: 当前高度=%d, 最小高度=%d\n", cyCur, cyMin);
	
	int cxCur, cxMin;
	m_wndSplitter1.GetColumnInfo(0, cxCur, cxMin);
	TRACE2("分割窗口1列0状态: 当前宽度=%d, 最小宽度=%d\n", cxCur, cxMin);
	m_wndSplitter1.GetColumnInfo(1, cxCur, cxMin);
	TRACE2("分割窗口1列1状态: 当前宽度=%d, 最小宽度=%d\n", cxCur, cxMin);
	
	// 检查视图状态
	TRACE1("左上视图ID: %d\n", m_nVolumeViewID);
	TRACE1("技术指标视图ID: %d\n", m_nIndicatorViewID);
	TRACE1("信号指标视图ID: %d\n", m_nKLineSignalID);
	TRACE1("历史分时视图ID: %d\n", m_nKLineTimeID);
	TRACE1("右侧信息视图ID: %d\n", m_nInfoViewID);
	
	return TRUE;
}

// 消息处理程序

int CKLineView::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CView::OnCreate(lpCreateStruct) == -1)
		return -1;
	
	// 创建分割窗口
	if (!CreateSplitterWindow())
		return -1;
		
	return 0;
}

void CKLineView::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);
	
	// 调整分割窗口大小
	if (m_wndSplitter1.GetSafeHwnd())
	{
		CRect rcClient;
		GetClientRect(&rcClient);
		TRACE2("客户区矩形: (%d, %d)\n", rcClient.Width(), rcClient.Height());
		
		// 调整主分割窗口大小以填充整个客户区
		m_wndSplitter1.MoveWindow(&rcClient);
		
		// 确保CKLineInfo视图固定宽度为300像素
		// 但确保左侧至少有200像素宽度，否则按照7:3比例分割
		int rightWidth = 300;
		int minLeftWidth = 200;  // 左侧最小宽度
		int leftWidth;
		
		if (rcClient.Width() >= (rightWidth + minLeftWidth))
		{
			// 窗口足够宽，可以使用固定宽度
			leftWidth = rcClient.Width() - rightWidth;
		}
		else
		{
			// 窗口宽度不足，按比例分割
			leftWidth = rcClient.Width() * 7 / 10;
			rightWidth = rcClient.Width() - leftWidth;
		}
		
		// 确保宽度不为负数
		leftWidth = max(10, leftWidth);
		rightWidth = max(10, rightWidth);
		
		TRACE2("OnSize: 设置宽度 - 左=%d, 右=%d\n", leftWidth, rightWidth);
		m_wndSplitter1.SetColumnInfo(0, leftWidth, 100);
		m_wndSplitter1.SetColumnInfo(1, rightWidth, 10); // 设置最小宽度为10，避免限制过严
		TRACE2("设置左右分割宽度: 左=%d, 右=%d\n", leftWidth, rightWidth);
		
		// 计算左侧区域的矩形并打印出来
		CRect rcLeft;
		if (m_wndSplitter1.GetPane(0, 0))
		{
			m_wndSplitter1.GetPane(0, 0)->GetWindowRect(&rcLeft);
			ScreenToClient(&rcLeft);
			TRACE2("左侧区域矩形: 宽=%d, 高=%d\n", rcLeft.Width(), rcLeft.Height());
			TRACE2("左侧区域位置: 左=%d, 上=%d\n", rcLeft.left, rcLeft.top);
			
			// 确保嵌套的分割窗口正确调整大小
			if (m_wndSplitter2.GetSafeHwnd())
			{
				// 调整子分割窗口大小
				m_wndSplitter2.MoveWindow(&rcLeft);
				
				// 设置上下分割比例（上部占总高度的70%）
				int topHeight = rcLeft.Height() * 7 / 10;
				int bottomHeight = rcLeft.Height() - topHeight;
				
				// 确保高度不会过小
				topHeight = max(100, topHeight);
				bottomHeight = max(100, bottomHeight);
				TRACE2("OnSize: 设置高度 - 上=%d, 下=%d\n", topHeight, bottomHeight);
				
				m_wndSplitter2.SetRowInfo(0, topHeight, 100);
				m_wndSplitter2.SetRowInfo(1, bottomHeight, 100);
			}
			else
			{
				TRACE0("警告: 子分割窗口句柄无效!\n");
			}
		}
		else
		{
			TRACE0("警告: 无法获取主分割窗口的左侧窗格!\n");
		}
		
		// 重新计算主分割窗口布局
		m_wndSplitter1.RecalcLayout();
		
		// 重新计算嵌套的子分割窗口布局
		if (m_wndSplitter2.GetSafeHwnd())
		{
			m_wndSplitter2.RecalcLayout();
		}
		
		// 强制更新所有子视图
		if (m_wndSplitter1.GetPane(0, 1))
		{
			m_wndSplitter1.GetPane(0, 1)->Invalidate();  // 右侧信息视图
			TRACE0("更新右侧信息视图\n");
		}
		
		if (m_wndSplitter2.GetSafeHwnd())
		{
			if (m_wndSplitter2.GetPane(0, 0))
			{
				m_wndSplitter2.GetPane(0, 0)->Invalidate();  // 左上K线视图
				TRACE0("更新左上K线视图\n");
			}
			else
			{
				TRACE0("警告: 无法获取左上K线视图!\n");
			}
			
			if (m_wndSplitter2.GetPane(1, 0))
			{
				m_wndSplitter2.GetPane(1, 0)->Invalidate();  // 左下指标视图
				TRACE0("更新左下指标视图\n");
			}
			else
			{
				TRACE0("警告: 无法获取左下指标视图!\n");
			}
		}
	}
}

// 处理按键事件，添加在文件末尾
void CKLineView::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags)
{
	// 处理上下左右键
	CStockDoc* pDoc = GetDocument();
	
	if (nChar == VK_LEFT || nChar == VK_UP)
	{
		// 显示上一支股票
		if (pDoc)
			pDoc->ShowPreviousStock();
	}
	else if (nChar == VK_RIGHT || nChar == VK_DOWN)
	{
		// 显示下一支股票
		if (pDoc)
			pDoc->ShowNextStock();
	}
	
	CView::OnKeyDown(nChar, nRepCnt, nFlags);
}

// 处理定时器事件
void CKLineView::OnTimer(UINT_PTR nIDEvent)
{
	if (nIDEvent == 1001) // 延迟重绘定时器
	{
		// 停止定时器
		KillTimer(nIDEvent);
		
		// 强制重绘所有视图，使用双缓冲减少闪烁
		RedrawWindow(NULL, NULL, RDW_INVALIDATE | RDW_UPDATENOW | RDW_ALLCHILDREN);
	}
	else if (nIDEvent == 1002) // 延时更新定时器
	{
		// 停止定时器
		KillTimer(nIDEvent);
		
		// 获取当前文档
		CStockDoc* pDoc = GetDocument();
		if (pDoc)
		{
			// 获取当前股票代码
			CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
			
			// 如果当前有股票，立即更新视图
			if (!strCurrentStock.IsEmpty())
			{
				// 更新视图
				UpdateChildViews(strCurrentStock);
			}
		}
	}
	
	CView::OnTimer(nIDEvent);
} 