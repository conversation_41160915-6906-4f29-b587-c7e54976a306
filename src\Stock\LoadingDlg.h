﻿#pragma once
#include "afxdialogex.h"

// 加载进度对话框
class CLoadingDlg : public CDialog
{
	DECLARE_DYNAMIC(CLoadingDlg)

public:
    CLoadingDlg(CWnd* pParent = NULL);
    virtual ~CLoadingDlg();

    // 对话框数据
    enum { IDD = IDD_LOADING_DIALOG };

    // 设置进度（范围0-100）
    void SetProgress(int nProgress);
    
    // 设置状态文本
    void SetStatusText(LPCTSTR lpszText);
    
    // 创建并显示对话框（非模态）
    BOOL Create(CWnd* pParentWnd = NULL);
    
    // 销毁对话框
    BOOL DestroyWindow();

protected:
    virtual void DoDataExchange(CDataExchange* pDX);
    virtual BOOL OnInitDialog();
    virtual void PostNcDestroy();

    DECLARE_MESSAGE_MAP()

private:
    CProgressCtrl m_ctrlProgress;  // 进度控件
    CStatic m_staticStatus;        // 状态文本控件
    int m_nProgress;               // 当前进度值
    CString m_strStatus;           // 当前状态文本
}; 