﻿#pragma once

#include "ugctrl.h"
#include "UGCTsarw.h"	
#include "..\resource.h"


// 定义列数常量
#define NUM_COLS_MAX 50  // 最大支持的列数
#define NUM_COLS_DEFAULT 20  // 默认列数


// 表格颜色
#define MY_COLOR_GRID_HEADER RGB(0, 0, 0)          // 表头背景色改为纯黑色
#define MY_COLOR_GRID_HEADER_TEXT RGB(255, 255, 0) // 表头文字颜色为黄色
#define MY_COLOR_GRID_FOCUS RGB(0, 0, 160)         // 选中行背景色
#define MY_COLOR_GRID_FOCUS_TEXT RGB(255, 255, 255) // 选中行文字颜色
#define MY_COLOR_GRID_EVEN RGB(0, 0, 0)            // 偶数行背景色为黑色
#define MY_COLOR_GRID_ODD RGB(0, 0, 0)             // 奇数行背景色为黑色

// 列数据类型
enum COLUMN_DATA_TYPE
{
    COL_TYPE_STRING = 0,    // 字符串
    COL_TYPE_NUMBER,        // 数字
    COL_TYPE_PRICE,         // 价格
    COL_TYPE_PERCENT,       // 百分比
    COL_TYPE_DATE,          // 日期
    COL_TYPE_STATUS         // 状态
};

// 列对齐方式
enum COLUMN_ALIGN
{
    COL_ALIGN_LEFT = 0,     // 左对齐
    COL_ALIGN_CENTER,       // 居中对齐
    COL_ALIGN_RIGHT         // 右对齐
};

// 定义列信息结构体
struct COLUMN_INFO
{
	char	_FieldName[32];      // 列标题
	int		_Width;              // 列宽度
	bool	_Visible;            // 是否可见
	int		_DataType;           // 数据类型
    int     _Alignment;          // 对齐方式
    bool    _UserDefined;        // 是否用户自定义
    char    _DataField[32];      // 数据字段名
    int     _DisplayOrder;       // 显示顺序
};

enum TAB_INDEX
{
	TAB_INDEX_ALL = 0,     // 全部股票
	TAB_INDEX_SH_A,        // 上证A股
	TAB_INDEX_SZ_A,        // 深证A股
	TAB_INDEX_GEM,         // 创业板
	TAB_INDEX_STAR,        // 科创板
	TAB_INDEX_BJ_A         // 北证A股
};

struct STOCK_STATE
{
	int		_Index;
	char	_Code[16];
	char	_Name[16];
	char	_Desc[16];
};

class CSymbolGrid : public CUGCtrl
{
public:
	CSymbolGrid();
	~CSymbolGrid();

	// 设置当前选项卡的列配置
	void SetupColumnsForTab(int tabIndex);
	
	// 获取当前选项卡的列配置
	const COLUMN_INFO* GetCurrentTabColumns() const;
	
	// 获取当前选项卡的列数
	int GetCurrentColumnCount() const;

	// 批量操作优化函数
	void BatchSetCellParams(const std::vector<std::pair<int, int>>& cellParams);  // row, stockID-1
	void SetCellParamOptimized(int row, int stockIndex);
	
	// 添加自定义列
	bool AddCustomColumn(COLUMN_INFO& colInfo);
	
	// 隐藏指定列
	bool HideColumn(int colIndex);
	
	// 显示指定列
	bool ShowColumn(int colIndex);
	
	// 重置列配置为默认值
	void ResetColumnsToDefault();
	
	// 保存当前列配置
	bool SaveColumnConfig(const char* configName);
	
	// 加载列配置
	bool LoadColumnConfig(const char* configName);
	
	// 设置选项卡高度
	void SetTabHeight(int height);
	
	// 设置选项卡前景色
	bool SetTabTextColor(int tabID, COLORREF color);
	
	// 设置选项卡背景色
	bool SetTabBackColor(int tabID, COLORREF color);
	
	// 设置选项卡选中时的前景色
	bool SetTabHTextColor(int tabID, COLORREF color);
	
	// 设置选项卡选中时的背景色
	bool SetTabHBackColor(int tabID, COLORREF color);

	// 刷新重绘所有单元格
	void RedrawAll();

	// 市场类型转换
	int GetTabToMarketType(int nTabIndex);

	// 使缓存失效，强制下次重新加载
	void InvalidateCache() { m_bCacheValid = false; }
	
	// 设置是否在更新时启用防闪烁模式
	void EnableAntiFlicker(bool enable) { m_bAntiFlickerEnabled = enable; }

protected:
	// 初始化回调函数
	virtual void OnSetup();
	
	// 自定义颜色
	COLORREF OnGetDefBackColor(int section);
	
	// 表格单元格事件处理函数
	virtual void OnGetCell(int col, long row, CUGCell *cell);
	virtual void OnDClicked(int col, long row, RECT *rect, POINT *point, BOOL processed);
	virtual void OnLClicked(int col, long row, int updn, RECT *rect, POINT *point, int processed);
	virtual void OnRClicked(int col, long row, int updn, RECT *rect, POINT *point, int processed);
	virtual void OnTH_LClicked(int col, long row, int updn, RECT *rect, POINT *point, BOOL processed);
	virtual void OnTH_RClicked(int col, long row, int updn, RECT *rect, POINT *point, BOOL processed);
	virtual void OnMouseMove(int col, long row, POINT *point, UINT nFlags, BOOL processed);
	virtual void OnKeyDown(UINT* vcKey, BOOL processed);
	
	// 表头事件
	virtual int OnMenuStart(int col, long row, int section);
	virtual void OnMenuCommand(int col, long row, int section, int item);
	
	// 排序函数
	virtual int OnSortEvaluate(CUGCell* cell1, CUGCell* cell2, int flags);
	
	// 设置表格样式
	virtual void OnSheetSetup(int sheetNumber);
	
	// 顶部行变更
	virtual void OnTopRowChange(long oldrow, long newrow);
	
	// 行变更
	virtual void OnRowChange(long oldrow, long newrow);
	
	// 选项卡选择事件
	virtual void OnTabSelected(int ID);
	
	// 鼠标滚轮事件处理
	BOOL OnMouseWheel(UINT nFlags, short zDelta, CPoint pt);

	// Windows消息处理
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnColumnConfig();
	afx_msg void OnRecentStrong();
	afx_msg LRESULT		OnNotifyStockUpdate(WPARAM wparam, LPARAM lparam);
	DECLARE_MESSAGE_MAP()

private:
	// 排序相关变量
	int m_iArrowIndex;
	int m_iSortCol;
	BOOL m_bSortedAscending;
	CUGSortArrowType m_sortArrow;
	

	// 当前列配置
	COLUMN_INFO m_colInfos[NUM_COLS_MAX];
	
	// 默认列配置
	COLUMN_INFO m_defaultColInfos[NUM_COLS_MAX];
	
	// 当前列数
	int m_nCurrentColCount;
	
	// 是否使用自定义配置
	bool m_bUsingCustomConfig;
	
	// 选中行
	long m_nCurrentRow;
	
	
	// 缓存的市场类型
	int m_cachedMarketType;
	
	// 缓存有效标志
	bool m_bCacheValid;
	
	// 当前选项卡索引
	int m_CurTabIndex;
	
	// 防闪烁标志
	bool m_bAntiFlickerEnabled;
	
	// 上次更新时间
	DWORD m_lastUpdateTime;

	// 辅助函数
	
	// 缓存当前显示的股票列表
	void CacheCurrentStockList();
	
	// 获取列文本颜色
	COLORREF GetColTextColor(int col, long row);
	
	// 调整组件大小
	void AdjustComponentSizes();
	
	// 获取对齐方式标志
	int GetAlignmentFlag(int alignment);

};
