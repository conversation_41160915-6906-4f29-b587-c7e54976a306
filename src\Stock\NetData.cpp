﻿#include "pch.h"
#include "Stock.h"
#include "NetData.h"
#include "StockDoc.h"
#include <algorithm> // 确保algorithm头文件被包含，std::min函数在此定义
#include <chrono>
#include <sstream>
#include <thread>
#include <functional>
#include "..\Common\PerformanceMonitor.h"


CNetData::CNetData()
    : m_pDoc(nullptr), 
      m_pHttp(nullptr), 
      m_pRealtimeHttp(nullptr),
      m_pTimelineHttp(nullptr),
      m_pMarketTimelineHttp(nullptr),
      m_pMainFundFlowHttp(nullptr),
      m_pAuctionHttp(nullptr),
      m_running(false), 
      m_timerRunning(false),
      m_threadCount(5),
      m_activeThreads(0),
      m_readyForDownload(false),
      m_initializedFlag(false)
{
}

CNetData::~CNetData()
{
    // 关闭并清理资源
    Shutdown();
    
    // 释放HTTP对象
    ReleaseHttpObjects();
}

void CNetData::TimerThread()
{
    TRACE("数据下载线程已启动，等待应用程序完全初始化...\n");

    //// 实时行情数据下载线程
    std::thread realtimeThread([this]() 
    {
        TRACE("应用程序已就绪，开始实时数据更新\n");
        
        // 添加标志，记录非交易时段是否已下载过数据
        bool nonTradingDataDownloaded = false;
        
        while (m_timerRunning) 
        {
            try {
                // 获取当前时间
                auto now = std::chrono::system_clock::now();
                std::time_t currentTime = std::chrono::system_clock::to_time_t(now);
                std::tm localTime;
                localtime_s(&localTime, &currentTime);
                
                // 检查是否是交易日（周一至周五）
                bool isWeekday = (localTime.tm_wday >= 1 && localTime.tm_wday <= 5);
                
                // 检查是否在交易时段（9:30-11:30, 13:00-15:00）
                bool isMorningSession = (localTime.tm_hour == 9 && localTime.tm_min >= 30) || 
                                       (localTime.tm_hour == 10) || 
                                       (localTime.tm_hour == 11 && localTime.tm_min <= 30);
                
                bool isAfternoonSession = (localTime.tm_hour == 13) || 
                                         (localTime.tm_hour == 14) || 
                                         (localTime.tm_hour == 15 && localTime.tm_min == 0);
                
                bool isTradingTime = isMorningSession || isAfternoonSession;
                
                // 交易日交易时段频繁下载，其他时间只下载一次
                if (isWeekday && isTradingTime) {
                    // 重置非交易时段下载标志，以便下次非交易时段再次下载
                    nonTradingDataDownloaded = false;
                    
                    // 交易时段中频繁下载数据
                    TRACE("当前为交易日交易时段，开始下载实时行情数据\n");
                    DownloadRealtimeData(m_pDoc);
                    TRACE("实时行情数据下载完成，等待2秒后继续\n");
                    std::this_thread::sleep_for(std::chrono::seconds(2));
                } else {
                    // 非交易日或非交易时段
                    if (!nonTradingDataDownloaded) {
                        // 如果还没有下载过，则下载一次
                        TRACE("当前为非交易日或非交易时段，仅下载一次实时行情数据\n");
                        DownloadRealtimeData(m_pDoc);
                        nonTradingDataDownloaded = true;
                        TRACE("非交易时段实时行情数据下载完成\n");
                    }
                    
                    // 非交易时段，延长检查间隔
                    if (!isWeekday) {
                        // 非交易日，每30分钟检查一次
                        std::this_thread::sleep_for(std::chrono::minutes(30));
                    } else {
                        // 交易日非交易时段，每5分钟检查一次
                        std::this_thread::sleep_for(std::chrono::minutes(5));
                    }
                }
            } catch (const std::exception& e) {
                TRACE("实时行情数据下载异常: %s\n", e.what());
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
        TRACE("实时行情数据下载线程已停止\n");
    });
    

    //// 大盘分时数据下载线程
    std::thread marketThread([this]() 
    {
        TRACE("应用程序已就绪，开始大盘分时数据更新\n");
        
        // 添加标志，记录非交易时段是否已下载过数据
        bool nonTradingDataDownloaded = false;
        
        while (m_timerRunning) {
            try {
                // 获取当前时间
                auto now = std::chrono::system_clock::now();
                std::time_t currentTime = std::chrono::system_clock::to_time_t(now);
                std::tm localTime;
                localtime_s(&localTime, &currentTime);
                
                // 检查是否是交易日（周一至周五）
                bool isWeekday = (localTime.tm_wday >= 1 && localTime.tm_wday <= 5);
                
                // 检查是否在交易时段（9:30-11:30, 13:00-15:00）
                bool isMorningSession = (localTime.tm_hour == 9 && localTime.tm_min >= 30) || 
                                       (localTime.tm_hour == 10) || 
                                       (localTime.tm_hour == 11 && localTime.tm_min <= 30);
                
                bool isAfternoonSession = (localTime.tm_hour == 13) || 
                                         (localTime.tm_hour == 14) || 
                                         (localTime.tm_hour == 15 && localTime.tm_min == 0);
                
                bool isTradingTime = isMorningSession || isAfternoonSession;
                
                // 交易日交易时段频繁下载，其他时间只下载一次
                if (isWeekday && isTradingTime) {
                    // 重置非交易时段下载标志，以便下次非交易时段再次下载
                    nonTradingDataDownloaded = false;
                    
                    // 交易时段中频繁下载数据
                    TRACE("当前为交易日交易时段，开始下载大盘分时数据\n");
                    DownloadMarketTimelineData();
                    TRACE("大盘分时数据下载完成，等待2秒后继续\n");
                    std::this_thread::sleep_for(std::chrono::seconds(2));
                } else {
                    // 非交易日或非交易时段
                    if (!nonTradingDataDownloaded) {
                        // 如果还没有下载过，则下载一次
                        TRACE("当前为非交易日或非交易时段，仅下载一次大盘分时数据\n");
                        DownloadMarketTimelineData();
                        nonTradingDataDownloaded = true;
                        TRACE("非交易时段大盘分时数据下载完成\n");
                    }
                    
                    // 非交易时段，延长检查间隔
                    if (!isWeekday) {
                        // 非交易日，每30分钟检查一次
                        TRACE("当前是非交易日，等待30分钟后再次检查\n");
                        std::this_thread::sleep_for(std::chrono::minutes(30));
                    } else {
                        // 交易日非交易时段，每5分钟检查一次
                        TRACE("当前是交易日非交易时段，等待5分钟后再次检查\n");
                        std::this_thread::sleep_for(std::chrono::minutes(5));
                    }
                }
            }
            catch (const std::exception& e) {
                TRACE("大盘分时数据下载异常: %s\n", e.what());
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
        TRACE("大盘分时数据下载线程已停止\n");
    });
    

    //// 主力资金流向下载线程
    std::thread fundFlowThread([this]() 
    {
        TRACE("主力资金流向数据下载线程已启动\n");
        
        // 添加标志，记录非交易时段是否已下载过数据
        bool nonTradingDataDownloaded = false;
        
        while (m_timerRunning) {
            try {
                // 获取当前时间
                auto now = std::chrono::system_clock::now();
                std::time_t currentTime = std::chrono::system_clock::to_time_t(now);
                std::tm localTime;
                localtime_s(&localTime, &currentTime);
                
                // 检查是否是交易日（周一至周五）
                bool isWeekday = (localTime.tm_wday >= 1 && localTime.tm_wday <= 5);
                
                // 检查是否在交易时段（9:30-11:30, 13:00-15:00）
                bool isMorningSession = (localTime.tm_hour == 9 && localTime.tm_min >= 30) || 
                                       (localTime.tm_hour == 10) || 
                                       (localTime.tm_hour == 11 && localTime.tm_min <= 30);
                
                bool isAfternoonSession = (localTime.tm_hour == 13) || 
                                         (localTime.tm_hour == 14) || 
                                         (localTime.tm_hour == 15 && localTime.tm_min == 0);
                
                bool isTradingTime = isMorningSession || isAfternoonSession;
                
                // 交易日交易时段频繁下载，其他时间只下载一次
                if (isWeekday && isTradingTime) {
                    // 重置非交易时段下载标志，以便下次非交易时段再次下载
                    nonTradingDataDownloaded = false;
                    
                    // 交易时段中频繁下载数据
                    TRACE("当前为交易日交易时段，开始下载主力资金流向数据\n");
                    DownloadMainFundFlowData();
                    TRACE("主力资金流向数据下载完成，等待2秒后继续\n");
                    std::this_thread::sleep_for(std::chrono::seconds(2));
                } else {
                    // 非交易日或非交易时段
                    if (!nonTradingDataDownloaded) {
                        // 如果还没有下载过，则下载一次
                        TRACE("当前为非交易日或非交易时段，仅下载一次主力资金流向数据\n");
                        DownloadMainFundFlowData();
                        nonTradingDataDownloaded = true;
                        TRACE("非交易时段主力资金流向数据下载完成\n");
                    }
                    
                    // 非交易时段，延长检查间隔
                    if (!isWeekday) {
                        // 非交易日，每30分钟检查一次
                        TRACE("当前是非交易日，等待30分钟后再次检查\n");
                        std::this_thread::sleep_for(std::chrono::minutes(30));
                    } else {
                        // 交易日非交易时段，每5分钟检查一次
                        TRACE("当前是交易日非交易时段，等待5分钟后再次检查\n");
                        std::this_thread::sleep_for(std::chrono::minutes(5));
                    }
                }
            } catch (const std::exception& e) {
                TRACE("主力资金流向数据下载异常: %s\n", e.what());
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
        TRACE("主力资金流向数据下载线程已停止\n");
    });
    

    //// 启用集合竞价数据下载线程
    std::thread auctionThread([this]() 
    {
        TRACE("集合竞价数据下载线程已启动\n");
        while (m_timerRunning) {
            try {
                // 获取当前时间
                auto now = std::chrono::system_clock::now();
                std::time_t currentTime = std::chrono::system_clock::to_time_t(now);
                std::tm localTime;
                localtime_s(&localTime, &currentTime);
                
                // 检查是否是交易日（周一至周五）
                bool isWeekday = (localTime.tm_wday >= 1 && localTime.tm_wday <= 5);
                
                // 检查是否在集合竞价时间段内（9:14-9:26）
                bool isAuctionTime = (localTime.tm_hour == 9 && localTime.tm_min >= 14 && localTime.tm_min <= 26);
                
                if (isWeekday && isAuctionTime) {
                    TRACE("当前时间 %02d:%02d，开始下载集合竞价数据\n", 
                          localTime.tm_hour, localTime.tm_min);
                    DownloadAuctionData();
                    TRACE("集合竞价数据下载完成，等待20秒后继续\n");
                    
                    // 在集合竞价时间段内，每20秒更新一次数据
                    std::this_thread::sleep_for(std::chrono::seconds(20));
                } else {
                    if (!isWeekday) {
                        // 非交易日时，每5分钟检查一次
                        TRACE("当前是非交易日，集合竞价数据下载处于休眠状态\n");
                        std::this_thread::sleep_for(std::chrono::minutes(5));
                    } else if (localTime.tm_hour < 9 || (localTime.tm_hour == 9 && localTime.tm_min < 14)) {
                        // 交易日但未到集合竞价时间，每分钟检查一次
                        TRACE("当前时间 %02d:%02d，未到集合竞价时间(9:14-9:26)，等待\n", 
                              localTime.tm_hour, localTime.tm_min);
                        std::this_thread::sleep_for(std::chrono::minutes(1));
                    } else if (localTime.tm_hour > 9 || (localTime.tm_hour == 9 && localTime.tm_min > 26)) {
                        // 交易日但超过集合竞价时间，等待到第二天
                        int hoursToNextDay = 24 - localTime.tm_hour;
                        TRACE("当前时间 %02d:%02d，已超过集合竞价时间，等待到明天\n", 
                              localTime.tm_hour, localTime.tm_min);
                        std::this_thread::sleep_for(std::chrono::hours(hoursToNextDay));
                    }
                }
            } catch (const std::exception& e) {
                TRACE("集合竞价数据下载异常: %s\n", e.what());
                std::this_thread::sleep_for(std::chrono::seconds(30));
            }
        }
        TRACE("集合竞价数据下载线程已停止\n");
    });


    //// 个股分时数据下载线程
    std::thread timelineThread([this]() 
    {
        TRACE("个股分时数据下载线程已启动\n");
        
        // 添加标志，记录非交易时段是否已下载过数据
        bool nonTradingDataDownloaded = false;
        
        while (m_timerRunning) {
            try {
                std::string currentStock = m_pDoc->GetCurrentStock();
                if (!currentStock.empty()) {
                    // 获取当前时间
                    auto now = std::chrono::system_clock::now();
                    std::time_t currentTime = std::chrono::system_clock::to_time_t(now);
                    std::tm localTime;
                    localtime_s(&localTime, &currentTime);
                    
                    // 检查是否是交易日（周一至周五）
                    bool isWeekday = (localTime.tm_wday >= 1 && localTime.tm_wday <= 5);
                    
                    // 检查是否在交易时段（9:30-11:30, 13:00-15:00）
                    bool isMorningSession = (localTime.tm_hour == 9 && localTime.tm_min >= 30) || 
                                           (localTime.tm_hour == 10) || 
                                           (localTime.tm_hour == 11 && localTime.tm_min <= 30);
                    
                    bool isAfternoonSession = (localTime.tm_hour == 13) || 
                                             (localTime.tm_hour == 14) || 
                                             (localTime.tm_hour == 15 && localTime.tm_min == 0);
                    
                    bool isTradingTime = isMorningSession || isAfternoonSession;
                    
                    // 交易日交易时段频繁下载，其他时间只下载一次
                    if (isWeekday && isTradingTime) {
                        // 重置非交易时段下载标志，以便下次非交易时段再次下载
                        nonTradingDataDownloaded = false;
                        
                        // 交易时段中频繁下载数据
                        TRACE("当前为交易日交易时段，开始下载当前股票分时数据: %s\n", currentStock.c_str());
                        DownloadTimelineData(currentStock);
                        TRACE("当前股票分时数据下载完成，等待2秒后继续\n");
                        std::this_thread::sleep_for(std::chrono::seconds(2));
                    } else {
                        // 非交易日或非交易时段
                        if (!nonTradingDataDownloaded) {
                            // 如果还没有下载过，则下载一次
                            TRACE("当前为非交易日或非交易时段，仅下载一次当前股票分时数据: %s\n", currentStock.c_str());
                            DownloadTimelineData(currentStock);
                            nonTradingDataDownloaded = true;
                            TRACE("非交易时段当前股票分时数据下载完成\n");
                        }
                        
                        // 非交易时段，延长检查间隔
                        if (!isWeekday) {
                            // 非交易日，每30分钟检查一次
                            TRACE("当前是非交易日，等待30分钟后再次检查\n");
                            std::this_thread::sleep_for(std::chrono::minutes(30));
                        } else {
                            // 交易日非交易时段，每5分钟检查一次
                            TRACE("当前是交易日非交易时段，等待5分钟后再次检查\n");
                            std::this_thread::sleep_for(std::chrono::minutes(5));
                        }
                    }
                } else {
                    TRACE("当前未选中股票，等待选中\n");
                    std::this_thread::sleep_for(std::chrono::seconds(2));
                }
            } catch (const std::exception& e) {
                TRACE("当前股票分时数据下载异常: %s\n", e.what());
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
        TRACE("个股分时数据下载线程已停止\n");
    });
    

    // 分离所有线程，让它们在后台运行
    realtimeThread.detach();
    marketThread.detach();
    fundFlowThread.detach();
    auctionThread.detach();
    timelineThread.detach();
    
    // 主线程等待，直到m_timerRunning变为false
    while (m_timerRunning) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    TRACE("数据下载主线程已停止\n");
}

bool CNetData::Initialize(CStockDoc* pDoc, int threadCount)
{
    if (!pDoc)
        return false;
    
    TRACE("CNetData::Initialize - 开始初始化网络数据模块\n");
    
    m_pDoc = pDoc;
    m_threadCount = threadCount;
    
    // 初始状态设置为未就绪，等待UI完全初始化后再开始数据下载
    m_readyForDownload = false;
    m_initializedFlag = false;
    
    // 初始化HTTP对象
    if (!InitializeHttpObjects())
    {
        TRACE("CNetData::Initialize - 初始化HTTP对象失败\n");
        return false;
    }
    
    // 设置初始化标志
    m_initializedFlag = true;
    TRACE("CNetData::Initialize - 网络数据模块初始化成功，等待UI就绪\n");
    
    return true;
}

void CNetData::Shutdown()
{
    TRACE("开始关闭NetData模块...\n");
    
    // 首先停止定时器线程（会等待足够时间让线程安全终止）
    StopTimedDownload();
    
    // 停止工作线程
    m_running = false;
    
    // 通知所有等待的线程
    {
        std::unique_lock<std::mutex> lock(m_queueMutex);
        m_condition.notify_all();
    }
    
    // 等待所有线程结束
    for (auto& t : m_threads)
    {
        if (t.joinable())
        {
            TRACE("等待工作线程结束...\n");
            t.join();
        }
    }
    
    // 清空线程容器
    m_threads.clear();
    
    // 额外等待一些时间，确保所有线程操作都已完成
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    TRACE("NetData模块已安全关闭\n");
}

// 初始化所有HTTP对象
bool CNetData::InitializeHttpObjects()
{
    try
    {
        // 通用HTTP对象（保留以兼容现有代码）
        m_pHttp = std::make_unique<CWinHttp>();

        // 实时行情数据专用HTTP对象
        // http://qt.gtimg.cn/q=sh000001,sz399001,sz399006,sh000688,bj899050&offset=2,3,4,32,33,38
        m_pRealtimeHttp = CreateHttpClient("http://qt.gtimg.cn");
        if (!m_pRealtimeHttp) return false;

        // 分时数据专用HTTP对象
        // https://web.ifzq.gtimg.cn/appstock/app/minute/query?code=sz301277
        m_pTimelineHttp = CreateHttpClient("https://web.ifzq.gtimg.cn");
        if (!m_pTimelineHttp) return false;


        // 大盘分时数据专用HTTP对象
        // http://d.10jqka.com.cn/v6/time/hs_1A0001/last.js
        m_pMarketTimelineHttp = CreateHttpClient("http://d.10jqka.com.cn");
        if (!m_pMarketTimelineHttp) return false;


        // 主力资金流向专用HTTP对象
        // https://apphq.longhuvip.com/w1/api/index.php?Order=9&st=6000&a=KanPanNew&c=YiDongKanPan&apiv=w29&Type=1
        m_pMainFundFlowHttp = CreateHttpClient("https://apphq.longhuvip.com");
        if (!m_pMainFundFlowHttp) return false;

        // 集合竞价数据专用HTTP对象
        // http://hq.sinajs.cn/list=sz301277
        m_pAuctionHttp = CreateHttpClient("http://hq.sinajs.cn");
        if (m_pAuctionHttp) {
            m_pAuctionHttp->AddHeader("Referer", "https://finance.sina.com.cn/");
        }
        if (!m_pAuctionHttp) return false;

        return true;
    }
    catch (const std::exception& e)
    {
        TRACE("初始化HTTP对象异常：%s\n", e.what());
        ReleaseHttpObjects();
        return false;
    }
}

// 释放所有HTTP对象
void CNetData::ReleaseHttpObjects()
{
    TRACE("开始释放所有HTTP对象...\n");
    
    // 确保所有线程已停止使用HTTP对象
    if (m_timerRunning)
    {
        TRACE("警告：在线程运行时释放HTTP对象可能导致崩溃！先停止线程...\n");
        StopTimedDownload();
        
        // 额外等待一段时间，确保所有线程都已停止
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    // 设置一个标志，表示资源正在被清理
    m_running = false;

    // 使用智能指针，自动释放资源，无需手动delete
    TRACE("释放通用HTTP对象\n");
    m_pHttp.reset();

    TRACE("释放实时行情HTTP对象\n");
    m_pRealtimeHttp.reset();

    TRACE("释放分时数据HTTP对象\n");
    m_pTimelineHttp.reset();

    TRACE("释放大盘分时数据HTTP对象\n");
    m_pMarketTimelineHttp.reset();

    TRACE("释放主力资金流HTTP对象\n");
    m_pMainFundFlowHttp.reset();

    TRACE("释放集合竞价HTTP对象\n");
    m_pAuctionHttp.reset();
    
    TRACE("所有HTTP对象已释放\n");
}

// HTTP对象创建辅助函数
std::unique_ptr<CWinHttp> CNetData::CreateHttpClient(const char* serverUrl, bool addCommonHeaders)
{
    auto httpClient = std::make_unique<CWinHttp>();

    if (addCommonHeaders) {
        AddCommonHeaders(httpClient.get());
    }

    // 连接到服务器
    if (!httpClient->ConnectHttpServer(serverUrl)) {
        TRACE("连接服务器失败: %s\n", serverUrl);
        return nullptr;
    }

    return httpClient;
}

// 添加通用HTTP头
void CNetData::AddCommonHeaders(CWinHttp* httpClient)
{
    if (!httpClient) return;

    httpClient->AddHeader("Name", "LiYang");
    httpClient->AddHeader("Address", "ChengDu");
}


// 启动定时下载
void CNetData::StartTimedDownload()
{
    // 检查是否已经在运行
    if (m_timerRunning)
    {
        TRACE("CNetData::StartTimedDownload - 定时器线程已在运行\n");
        return;
    }
    
    // 启动定时线程
    m_timerRunning = true;
    m_timerThread = std::thread(&CNetData::TimerThread, this);
    
    TRACE("CNetData::StartTimedDownload - 启动定时器线程，等待UI初始化完成\n");
}

// 停止定时下载
void CNetData::StopTimedDownload()
{
    TRACE("正在停止所有定时下载线程...\n");
    
    // 先设置停止标志，以便线程能够自行退出
    m_timerRunning = false;
    m_running = false;
    
    // 增加等待，确保所有线程都看到停止标志
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    if (m_timerThread.joinable())
    {
        TRACE("等待定时器线程结束...\n");
        try {
            m_timerThread.join();
            TRACE("定时器线程已安全退出\n");
        }
        catch (const std::exception& e) {
            TRACE("等待线程结束时发生异常: %s\n", e.what());
        }
    }
    else
    {
        TRACE("定时器线程不可加入，可能已经退出\n");
    }
    
    // 确保所有工作线程都已停止
    for (auto& thread : m_workThreads)
    {
        if (thread.joinable())
        {
            TRACE("等待工作线程结束...\n");
            try {
                thread.join();
                TRACE("工作线程已安全退出\n");
            }
            catch (const std::exception& e) {
                TRACE("等待工作线程结束时发生异常: %s\n", e.what());
            }
        }
    }
    
    // 清空工作线程列表
    m_workThreads.clear();
    
    TRACE("所有定时下载线程已停止\n");
}


bool CNetData::DownloadRealtimeData(const CStockDoc* pDocument)
{
    PERF_MONITOR_FUNC();  // 性能监控

    if (!m_pRealtimeHttp || !pDocument)
    {
       // TRACE("实时行情HTTP对象或文档对象为空\n");
        return false;
    }
    
    try
    {
        m_pDoc = const_cast<CStockDoc*>(pDocument);
        
        // 获取股票总数
        size_t totalStocks = m_pDoc->m_vecStocks.size();
        if (totalStocks == 0)
        {
           // TRACE("股票列表为空\n");
            return false;
        }
        
       // TRACE("股票总数: %d\n", (int)totalStocks);
        
        // 计算需要多少批次请求
        size_t batchCount = (totalStocks + BATCH_SIZE - 1) / BATCH_SIZE;
       // TRACE("需要分%d批次下载实时数据\n", (int)batchCount);
		
        // 预分配代码列表空间
		std::vector<std::string> vCodes;
		vCodes.reserve(BATCH_SIZE);


        bool overallResult = true;  // 记录总体下载结果
        
        // 循环请求每个批次
        for (size_t batchIndex = 0; batchIndex < batchCount; batchIndex++)
        {
            // 计算当前批次的起始和结束索引
            size_t startIndex = batchIndex * BATCH_SIZE;
            size_t endIndex = (startIndex + BATCH_SIZE < totalStocks) ? (startIndex + BATCH_SIZE) : totalStocks;
            
			// 清空代码列表，准备新一批次
			vCodes.clear();
            
            //TRACE("处理第%d批次，索引范围: %d-%d\n", (int)batchIndex + 1, (int)startIndex, (int)endIndex - 1);
            
            // 遍历当前批次的股票列表
            for (size_t i = startIndex; i < endIndex; i++)
            {
                const StockData& stock = m_pDoc->m_vecStocks[i];
                
				// 获取市场前缀
				std::string prefix;
				switch (stock._MarketType) {
				case MARKET_SH_A:
				case MARKET_STAR:
					prefix = "sh";
					break;
				case MARKET_SZ_A:
				case MARKET_GEM:
					prefix = "sz";
					break;
				case MARKET_BJ_A:
					prefix = "bj";
					break;
				default:
					prefix = "";
				}

				// 拼接完整代码
				vCodes.push_back(prefix + stock._Code);
            }
            
            // 如果当前批次没有股票代码，跳过
            if (vCodes.empty())
            {
                TRACE("当前批次没有有效的股票代码\n");
                continue;
            }

            // 构建URL，请求格式：http://qt.gtimg.cn/q=sh600000,sz000001
            std::string strUrl = "http://qt.gtimg.cn/q=";
            
            // 添加股票代码，用逗号分隔
            for (size_t i = 0; i < vCodes.size(); ++i)
            {
                if (i > 0)
                    strUrl += ",";
                strUrl += vCodes[i];
            }
            
            if (!m_pRealtimeHttp->IsConnect())
            {
                // 连接到服务器 - 修正连接地址格式
                if (!m_pRealtimeHttp->ConnectHttpServer("http://qt.gtimg.cn"))
                {
                   // TRACE("连接服务器失败\n");
                    overallResult = false;
                    continue;  // 继续处理下一批次
                }
            }
            
            // 发送HTTP GET请求
            CString response;
            for (int n = 0; n < 5; n++)     // 重试5次
            {
				BOOL ret = m_pRealtimeHttp->Request(strUrl.c_str(), HttpGet, response);
				if ((!ret)||(response.IsEmpty()))
				{
					//TRACE("HTTP请求失败，错误码: %d\n", m_pRealtimeHttp->GetLastError());
					overallResult = false;
                    Sleep(100);
					continue;  // 继续处理下一批次
				}
                else
                {
                    break;
                }
            }

            
            // 解析响应数据
            bool batchResult = this->ParseRealtimeData(response.GetBuffer(0));
            if (!batchResult)
            {
                //TRACE("解析第%d批次实时数据失败\n", (int)batchIndex + 1);
                overallResult = false;
            }
             
            // 线程安全的消息发送
            CWnd* pMainWnd = AfxGetMainWnd();
            if (pMainWnd && ::IsWindow(pMainWnd->GetSafeHwnd())) {
                pMainWnd->PostMessage(WM_USER_DATA_UPDATED, 0, 0);
            }
            
            // 添加适当的延迟，避免请求过快
            if (batchIndex < batchCount - 1)
            {
                //TRACE("等待100毫秒后请求下一批次...\n");
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
        
        //TRACE("实时数据下载完成，总体结果: %s\n", overallResult ? "成功" : "部分失败");
        return overallResult;
    }
    catch (const std::exception& e)
    {
        TRACE("下载实时数据异常: %s\n", e.what());
        return false;
    }
}

bool CNetData::ParseRealtimeData(const std::string& data)
{
	if (!m_pDoc || data.empty())
	{
		TRACE("解析实时数据失败：文档指针为空或数据为空\n");
		return false;
	}

	try
	{
		// 构建代码到索引的映射
		std::unordered_map<std::string, int> codeToIndexMap;
		for (size_t i = 0; i < m_pDoc->m_vecStocks.size(); ++i) {
			codeToIndexMap[m_pDoc->m_vecStocks[i]._Code] = static_cast<int>(i);
		}

		// 预分配容器
		const size_t expectedLineCount = m_pDoc->m_vecStocks.size();
		std::vector<std::string> lines;
		lines.reserve(expectedLineCount);

		// 快速分割数据行
		size_t pos = 0;
		size_t nextPos = 0;
		while ((nextPos = data.find('\n', pos)) != std::string::npos) {
			std::string line = data.substr(pos, nextPos - pos);
			if (!line.empty() && line.find('=') != std::string::npos) {
				lines.push_back(line);
			}
			pos = nextPos + 1;
		}
		// 处理最后一行
		if (pos < data.length()) {
			std::string line = data.substr(pos);
			if (!line.empty() && line.find('=') != std::string::npos) {
				lines.push_back(line);
			}
		}

		// 跟踪结果
		bool updated = false;
		int stockCount = 0;

		// 创建复用的字段向量
		std::vector<std::string> fields;
		fields.reserve(60);
		std::vector<std::string> tradeFields;
		tradeFields.reserve(3);

		// 解析每一行数据
		for (const auto& line : lines)
		{
			// 快速找到等号位置
			size_t equalPos = line.find('=');
			if (equalPos == std::string::npos || equalPos + 2 >= line.length())
				continue;

			// 高效提取前缀和数据部分，避免复制
			const char* prefixStart = line.c_str();
			const char* dataStart = line.c_str() + equalPos + 2;
			size_t prefixLen = equalPos;
			size_t dataLen = line.length() - equalPos - 2;

			// 调整边界去除引号
			if (dataLen > 0 && dataStart[0] == '"') {
				dataStart++;
				dataLen--;
			}
			if (dataLen > 0) {
				char lastChar = dataStart[dataLen - 1];
				if (lastChar == '"' || lastChar == ';') {
					dataLen--;
				}
			}

			// 创建数据字符串用于分割
			std::string dataStr(dataStart, dataLen);

			// 快速分割字段
			fields.clear();
			size_t fieldStart = 0;
			size_t fieldEnd = 0;
			while ((fieldEnd = dataStr.find('~', fieldStart)) != std::string::npos) {
				fields.push_back(dataStr.substr(fieldStart, fieldEnd - fieldStart));
				fieldStart = fieldEnd + 1;
			}
			fields.push_back(dataStr.substr(fieldStart));  // 添加最后一个字段

			// 确保字段数量足够
			if (fields.size() < 33)
				continue;

			// 高效提取股票代码
			std::string stockCode;
			if (prefixLen > 2 && prefixStart[0] == 'v' && prefixStart[1] == '_') {
				// 前缀格式为 v_sh601933，提取股票代码部分
				const char* codeStart = prefixStart + 2;
				size_t codeLen = prefixLen - 2;

				// 去除市场前缀(sh/sz/bj)
				if (codeLen > 2) {
					const char prefix2Chars[3] = { codeStart[0], codeStart[1], '\0' };
					const std::string prefixStr(prefix2Chars);

					if (prefixStr == "sh" || prefixStr == "sz" || prefixStr == "bj") {
						stockCode = std::string(codeStart + 2, codeLen - 2);
					}
					else {
						stockCode = std::string(codeStart, codeLen);
					}
				}
			}

			if (stockCode.empty())
				continue;

			// 使用哈希表快速查找股票索引
			auto it = codeToIndexMap.find(stockCode);
			if (it == codeToIndexMap.end())
				continue;

			int stockIndex = it->second;
			if (stockIndex < 0 || stockIndex >= static_cast<int>(m_pDoc->m_vecStocks.size()))
				continue;

			// 开始填充数据
			StockData& stock = m_pDoc->m_vecStocks[stockIndex];

			// 自定义安全转换函数
			auto safeStof = [](const std::string& str, float defaultValue = 0.0f) -> float {
				if (str.empty() || str == "-") return defaultValue;
				try { return static_cast<float>(atof(str.c_str())); }
				catch (...) { return defaultValue; }
				};

			auto safeStoi = [](const std::string& str, int defaultValue = 0) -> int {
				if (str.empty() || str == "-") return defaultValue;
				try { return atoi(str.c_str()); }
				catch (...) { return defaultValue; }
				};

			auto safeStod = [](const std::string& str, double defaultValue = 0.0) -> double {
				if (str.empty() || str == "-") return defaultValue;
				try { return atof(str.c_str()); }
				catch (...) { return defaultValue; }
				};

			try {
				// 基础价格数据 - 使用安全转换，降低异常风险
				stock._Close = safeStof(fields.size() > 3 ? fields[3] : "");
				stock._preClose = safeStof(fields.size() > 4 ? fields[4] : "");
				stock._Open = safeStof(fields.size() > 5 ? fields[5] : "");

				// 确保昨收价有效
				if (stock._preClose <= 0.0f) {
					if (stock._Open > 0.0f)
						stock._preClose = stock._Open;
					else if (stock._Close > 0.0f)
						stock._preClose = stock._Close;
					else
						stock._preClose = 10.0f;
				}

				// 成交量相关数据
				stock._Volume = safeStod(fields.size() > 6 ? fields[6] : "");
				stock._buyVolume = safeStoi(fields.size() > 7 ? fields[7] : "");
				stock._sellVolume = safeStoi(fields.size() > 8 ? fields[8] : "");

				// 五档买盘数据 - 批量处理
				if (fields.size() > 18) {
					for (int i = 0; i < 5; i++) {
						stock._Level2._bidPrice[i] = safeStof(fields[9 + i * 2]);
						stock._Level2._bidVolume[i] = safeStoi(fields[10 + i * 2]);
					}
				}

				// 五档卖盘数据 - 批量处理
				if (fields.size() > 28) {
					for (int i = 0; i < 5; i++) {
						stock._Level2._askPrice[i] = safeStof(fields[19 + i * 2]);
						stock._Level2._askVolume[i] = safeStoi(fields[20 + i * 2]);
					}
				}

				// 最高价、最低价
				if (fields.size() > 34) {
					stock._High = safeStof(fields[33]);
					stock._Low = safeStof(fields[34]);
				}

				// 成交信息 - 高效解析复合字段 "5.18/8674275/4485032085"
				if (fields.size() > 35) {
					const std::string& tradeInfo = fields[35];

					// 快速分割
					tradeFields.clear();
					size_t tradeStart = 0;
					size_t tradeEnd = 0;
					while ((tradeEnd = tradeInfo.find('/', tradeStart)) != std::string::npos) {
						tradeFields.push_back(tradeInfo.substr(tradeStart, tradeEnd - tradeStart));
						tradeStart = tradeEnd + 1;
					}
					tradeFields.push_back(tradeInfo.substr(tradeStart));

					if (tradeFields.size() >= 3) {
						// 已在前面设置了 _Close，这里跳过第一个字段
						stock._Volume = safeStod(tradeFields[1]) * 100;  // 转换为股
						stock._Amount = safeStod(tradeFields[2]);     // 成交额
					}
				}

				// 换手率与量比
				if (fields.size() > 38 && !fields[38].empty() && fields[38] != "-")
					stock._Turnover = safeStof(fields[38]);

				if (fields.size() > 49 && !fields[49].empty() && fields[49] != "-")
					stock._VolumeRatio = safeStof(fields[49]);

				// 更新成功
				updated = true;
				stockCount++;

//#ifdef _DEBUG
//				// 减少日志输出频率
//				if (stockCount % 100 == 0) {
//					TRACE("已更新 %d 只股票数据\n", stockCount);
//				}
//#endif
			}
			catch (const std::exception& e) {
				TRACE("处理股票%s数据出错: %s\n", stockCode.c_str(), e.what());
			}
		}

		//if (updated) {
		//	TRACE("共更新 %d 只股票数据\n", stockCount);
		//}

		return updated;
	}
	catch (const std::exception& e) {
		TRACE("解析实时数据出现异常: %s\n", e.what());
		return false;
	}
}

//bool CNetData::ParseRealtimeData(const std::string& data)
//{
//    if (!m_pDoc || data.empty())
//    {
//        TRACE("解析实时数据失败：文档指针为空或数据为空\n");
//        return false;
//    }
//
//    try
//    {
//        // 分割数据行
//        std::vector<std::string> lines;
//        std::istringstream iss(data);
//        std::string line;
//        while (std::getline(iss, line))
//        {
//            if (line.empty() || line.find('=') == std::string::npos)
//                continue;
//            
//            lines.push_back(line);
//        }
//
//        // 跟踪结果
//        bool updated = false;
//        int stockCount = 0;
//
//        // 解析每一行数据
//        for (const auto& line : lines)
//        {
//            // 找到等号位置
//            size_t equalPos = line.find('=');
//            if (equalPos == std::string::npos || equalPos + 2 >= line.length())
//                continue;
//
//            // 提取前缀和数据部分
//            std::string prefix = line.substr(0, equalPos);
//            std::string dataStr = line.substr(equalPos + 2);
//            
//            // 删除前后的引号
//            if (dataStr.front() == '"')
//                dataStr = dataStr.substr(1);
//            if (dataStr.back() == '"' || dataStr.back() == ';')
//                dataStr = dataStr.substr(0, dataStr.length() - (dataStr.back() == ';' ? 2 : 1));
//
//            // 分割数据字段
//            std::vector<std::string> fields;
//            std::istringstream fieldStream(dataStr);
//            std::string field;
//            while (std::getline(fieldStream, field, '~'))
//            {
//                fields.push_back(field);
//            }
//
//            // 确保字段数量足够
//            if (fields.size() < 33)
//            {
//                //TRACE("字段数量不足，跳过解析: %s\n", line.c_str());
//                continue;
//            }
//
//            // 提取股票代码
//            std::string stockCode;
//            if (prefix.length() > 2 && prefix.substr(0, 2) == "v_")
//            {
//                // 前缀格式为 v_sh601933，提取股票代码部分
//                std::string codeWithPrefix = prefix.substr(2);
//                
//                // 去除市场前缀(sh/sz/bj)
//                if (codeWithPrefix.substr(0, 2) == "sh" || 
//                    codeWithPrefix.substr(0, 2) == "sz" ||
//                    codeWithPrefix.substr(0, 2) == "bj")
//                {
//                    stockCode = codeWithPrefix.substr(2);
//                }
//                else
//                {
//                    stockCode = codeWithPrefix;
//                }
//            }
//            
//            if (stockCode.empty())
//            {
//                //TRACE("无法从前缀提取有效股票代码: %s\n", prefix.c_str());
//                continue;
//            }
//
//            // 查找股票索引
//            int stockIndex = m_pDoc->GetStockIndex(stockCode);
//            if (stockIndex < 0 || stockIndex >= static_cast<int>(m_pDoc->m_vecStocks.size()))
//            {
//                //TRACE("未找到股票或索引越界: %s\n", stockCode.c_str());
//                continue;
//            }
//
//            // 开始填充数据
//            StockData& stock = m_pDoc->m_vecStocks[stockIndex];
//            
//            try {
//                // 基础价格数据
//                stock._Close = std::stof(fields[3]);        // 当前价格/最新价 (第4个字段)
//                stock._preClose = std::stof(fields[4]);     // 昨收价 (第5个字段)
//                stock._Open = std::stof(fields[5]);         // 开盘价 (第6个字段)
//                
//                // 确保昨收价有效，如果无效则尝试使用合理的替代值
//                if (stock._preClose <= 0.0f)
//                {
//                    TRACE("股票[%s]昨收价无效(%.2f)，尝试使用开盘价作为替代\n", 
//                        stockCode.c_str(), stock._preClose);
//                    
//                    // 优先使用开盘价作为昨收价的替代
//                    if (stock._Open > 0.0f)
//                    {
//                        stock._preClose = stock._Open;
//                        TRACE("使用开盘价(%.2f)作为昨收价替代值\n", stock._preClose);
//                    }
//                    // 如果开盘价也无效，使用当前价
//                    else if (stock._Close > 0.0f)
//                    {
//                        stock._preClose = stock._Close;
//                        TRACE("使用当前价(%.2f)作为昨收价替代值\n", stock._preClose);
//                    }
//                    // 如果都无效，设置一个默认值
//                    else
//                    {
//                        stock._preClose = 10.0f; // 假设一个合理的昨收价
//                        TRACE("使用默认值(10.00)作为昨收价替代值\n");
//                    }
//                }
//                
//                // 成交量相关数据
//                stock._Volume = std::stod(fields[6]);       // 成交量(手) (第7个字段)
//                stock._buyVolume = std::stoi(fields[7]);    // 内盘(手) (第8个字段)
//                stock._sellVolume = std::stoi(fields[8]);   // 外盘(手) (第9个字段)
//                
//                // 五档买盘数据
//                stock._Level2._bidPrice[0] = std::stof(fields[9]);   // 买一价
//                stock._Level2._bidVolume[0] = std::stoi(fields[10]); // 买一量
//                stock._Level2._bidPrice[1] = std::stof(fields[11]);  // 买二价
//                stock._Level2._bidVolume[1] = std::stoi(fields[12]); // 买二量
//                stock._Level2._bidPrice[2] = std::stof(fields[13]);  // 买三价
//                stock._Level2._bidVolume[2] = std::stoi(fields[14]); // 买三量
//                stock._Level2._bidPrice[3] = std::stof(fields[15]);  // 买四价
//                stock._Level2._bidVolume[3] = std::stoi(fields[16]); // 买四量
//                stock._Level2._bidPrice[4] = std::stof(fields[17]);  // 买五价
//                stock._Level2._bidVolume[4] = std::stoi(fields[18]); // 买五量
//                
//                // 五档卖盘数据
//                stock._Level2._askPrice[0] = std::stof(fields[19]);  // 卖一价
//                stock._Level2._askVolume[0] = std::stoi(fields[20]); // 卖一量
//                stock._Level2._askPrice[1] = std::stof(fields[21]);  // 卖二价
//                stock._Level2._askVolume[1] = std::stoi(fields[22]); // 卖二量
//                stock._Level2._askPrice[2] = std::stof(fields[23]);  // 卖三价
//                stock._Level2._askVolume[2] = std::stoi(fields[24]); // 卖三量
//                stock._Level2._askPrice[3] = std::stof(fields[25]);  // 卖四价
//                stock._Level2._askVolume[3] = std::stoi(fields[26]); // 卖四量
//                stock._Level2._askPrice[4] = std::stof(fields[27]);  // 卖五价
//                stock._Level2._askVolume[4] = std::stoi(fields[28]); // 卖五量
//                
//                // 最高价、最低价
//                stock._High = std::stof(fields[33]);        // 最高价
//                stock._Low = std::stof(fields[34]);         // 最低价
//                
//                // 成交信息 - 解析复合字段 "5.18/8674275/4485032085"
//                std::string tradeInfo = fields[35];
//                std::vector<std::string> tradeFields;
//                std::istringstream tradeStream(tradeInfo);
//                std::string tradeField;
//                while (std::getline(tradeStream, tradeField, '/'))
//                {
//                    tradeFields.push_back(tradeField);
//                }
//                
//                if (tradeFields.size() >= 3)
//                {
//                    // 已在前面设置了 _Close，这里跳过第一个字段
//                    stock._Volume = std::stod(tradeFields[1]) * 100;  // 转换为股
//                    stock._Amount = std::stod(tradeFields[2]);     // 成交额
//                }
//                
//                // 换手率与量比
//                if (fields.size() > 38 && !fields[38].empty() && fields[38] != "-")
//                    stock._Turnover = std::stof(fields[38]);    // 换手率(%)
//                
//                if (fields.size() > 49 && !fields[49].empty() && fields[49] != "-")
//                    stock._VolumeRatio = std::stof(fields[49]); // 量比 (通常在第50个字段)
//                
//              
//                // 更新成功
//                updated = true;
//                stockCount++;
//                
//                // 打印成功更新的股票名称和价格（每50个打印一次）
//                if (stockCount % 50 == 0)
//                {
//                    TRACE("已更新 %d 只股票数据，最新：%s %.2f\n", 
//                        stockCount, stock._Name.c_str(), stock._Close);
//                }
//            }
//            catch (const std::exception& e) {
//                TRACE("处理股票%s数据出错: %s\n", stockCode.c_str(), e.what());
//            }
//        }
//        
//        // 如果有更新，通知视图刷新（使用线程安全的方法）
//        // 在MFC中，UI更新需要在主线程中进行，而不能从工作线程直接调用。
//        if (updated)
//        {
//            TRACE("共更新 %d 只股票数据，刷新视图\n", stockCount);
//           // m_pDoc->SafeUpdateAllViews();
//        }
//        
//        return updated;
//    }
//    catch (const std::exception& e)
//    {
//        TRACE("解析实时数据出现异常: %s\n", e.what());
//        return false;
//    }
//}



// 原有的按股票代码列表下载实时数据方法
bool CNetData::DownloadRealtimeData(const std::vector<std::string>& codes)
{
    if (!m_pRealtimeHttp || !m_pDoc || codes.empty())
    {
        TRACE("实时行情HTTP对象、文档指针为空或股票代码列表为空\n");
        return false;
    }
    
    try
    {
        // 构建URL，请求格式：http://qt.gtimg.cn/q=sh600000,sz000001
        std::string strUrl = "http://qt.gtimg.cn/q=";
        
        // 添加股票代码，用逗号分隔
        for (size_t i = 0; i < codes.size(); ++i)
        {
            if (i > 0)
                strUrl += ",";
            strUrl += codes[i];
        }
        
        // 添加参数以请求额外的字段（包括主力资金流向）
        strUrl += "&r=" + std::to_string(time(nullptr)); // 添加时间戳防止缓存
        strUrl += "&offset=2,3,4,5,6,7,8,9,10,30,31"; // 添加需要的字段
        
        TRACE("请求URL: %s\n", strUrl.c_str());
        
        // 添加请求头，模拟浏览器
        m_pRealtimeHttp->AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        m_pRealtimeHttp->AddHeader("Accept", "*/*");
        m_pRealtimeHttp->AddHeader("Connection", "keep-alive");
        
        // 连接到服务器
        if (!m_pRealtimeHttp->ConnectHttpServer("http://qt.gtimg.cn"))
        {
            TRACE("连接服务器失败\n");
            return false;
        }
        
        // 发送HTTP GET请求
        CString response;
        BOOL ret = m_pRealtimeHttp->Request(strUrl.c_str(), HttpGet, response);
        if (!ret)
        {
            TRACE("HTTP请求失败，错误码: %d\n", m_pRealtimeHttp->GetLastError());
            return false;
        }

        if (response.IsEmpty())
        {
            TRACE("HTTP响应为空\n");
            return false;
        }
        
        // 解析响应数据
        return this->ParseRealtimeData(response.GetBuffer(0));
    }
    catch (const std::exception& e)
    {
        TRACE("下载实时数据异常: %s\n", e.what());
        return false;
    }
}

// 下载个股分时数据
bool CNetData::DownloadTimelineData(const std::string& stockCode)
{
    if (!m_pTimelineHttp || !m_pDoc || stockCode.empty())
    {
        TRACE("分时数据HTTP对象、文档指针为空或股票代码为空\n");
        return false;
    }
    
    try
    {
        // 获取股票的市场前缀
        std::string market = GetMarketPrefix(stockCode);
        if (market.empty())
        {
            TRACE("无法确定股票 %s 的市场前缀\n", stockCode.c_str());
            return false;
        }
        
        // 构建完整的股票代码（带市场前缀）
        std::string fullCode = market + stockCode;
        
        // 构建URL，请求格式：https://web.ifzq.gtimg.cn/appstock/app/minute/query?code=sh600000
        std::string strUrl = "https://web.ifzq.gtimg.cn/appstock/app/minute/query?code=" + fullCode;
        
        // 添加时间戳防止缓存
        strUrl += "&r=" + std::to_string(time(nullptr));
        
        TRACE("请求分时数据URL: %s\n", strUrl.c_str());
        
        // 添加请求头，模拟浏览器
        m_pTimelineHttp->AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        m_pTimelineHttp->AddHeader("Accept", "*/*");
        m_pTimelineHttp->AddHeader("Referer", "https://stockapp.finance.qq.com/");
        
        // 连接到服务器
        if (!m_pTimelineHttp->ConnectHttpServer("https://web.ifzq.gtimg.cn"))
        {
            TRACE("连接分时数据服务器失败\n");
            return false;
        }
        
        // 发送HTTP GET请求
        CString response;
        BOOL ret = m_pTimelineHttp->Request(strUrl.c_str(), HttpGet, response);
        if (!ret)
        {
            TRACE("HTTP请求失败，错误码: %d\n", m_pTimelineHttp->GetLastError());
            return false;
        }

        if (response.IsEmpty())
        {
            TRACE("HTTP响应为空\n");
            return false;
        }
        
        // 解析响应数据
        return this->ParseTimelineData(stockCode, response.GetBuffer(0));
    }
    catch (const std::exception& e)
    {
        TRACE("下载分时数据异常: %s\n", e.what());
        return false;
    }
}

// 解析分时数据
bool CNetData::ParseTimelineData(const std::string& stockCode, const std::string& data)
{
    if (data.empty() || !m_pDoc)
    {
        TRACE("数据为空或文档指针为空\n");
        return false;
    }
    
    try
    {
        // 打印接收到的数据前50个字符
        TRACE("接收分时数据: %s...\n", data.substr(0, 50).c_str());
        
        // 检查是否包含错误信息
        if (data.find("\"code\":-1") != std::string::npos)
        {
            TRACE("分时数据请求错误: %s\n", data.c_str());
            return false;
        }
        
        // 查找数据部分
        size_t dataPos = data.find("\"data\":");
        if (dataPos == std::string::npos)
        {
            TRACE("未找到分时数据起始标记\n");
            return false;
        }
        
        // 查找分时数据段
        size_t timelinePos = data.find("\"data\":[", dataPos);
        if (timelinePos == std::string::npos)
        {
            TRACE("未找到分时数据数组\n");
            return false;
        }
        
        // 查找数据段结束位置
        size_t endPos = data.find("]", timelinePos);
        if (endPos == std::string::npos)
        {
            TRACE("未找到分时数据结束标记\n");
            return false;
        }
        
        // 提取分时数据数组
        std::string timelineData = data.substr(timelinePos + 8, endPos - timelinePos - 8);
        
        // 查找当前股票
        bool stockFound = false;
        
        // 使用GetStockIndex方法快速查找股票索引
        int stockIndex = m_pDoc->GetStockIndex(stockCode);
        
        if (stockIndex >= 0 && stockIndex < static_cast<int>(m_pDoc->m_vecStocks.size()))
        {
            stockFound = true;
            StockData& stock = m_pDoc->m_vecStocks[stockIndex];
            
            // 清空原有分时数据
            stock._vecTimeLine.clear();
            
            // 解析分时数据
            std::istringstream ss(timelineData);
            std::string item;
            
            // 用于累计计算总成交量和总成交额
            double totalVolume = 0.0;
            double totalAmount = 0.0;
            
            // 分割数据项
            while (std::getline(ss, item, ','))
            {
                // 去除多余的字符
                item.erase(std::remove(item.begin(), item.end(), '\"'), item.end());
                item.erase(std::remove(item.begin(), item.end(), '['), item.end());
                item.erase(std::remove(item.begin(), item.end(), ']'), item.end());
                
                // 分隔数据
                std::istringstream itemSS(item);
                std::string value;
                std::vector<std::string> values;
                
                while (std::getline(itemSS, value, ' '))
                {
                    if (!value.empty())
                        values.push_back(value);
                }
                
                // 分时数据格式通常为: [时间,价格,均价,成交量,...]
				if (values.size() == 3)
				{
					try
					{
						TLINE_DATA timelineItem;

						// 解析时间 (格式通常为 HH:MM)
						std::string timeStr = values[0];
						size_t colonPos = timeStr.find(':');
						if (colonPos != std::string::npos)
						{
							int hour = std::stoi(timeStr.substr(0, colonPos));
							int minute = std::stoi(timeStr.substr(colonPos + 1));
							timelineItem._Time = hour * 100 + minute;
						}
						else
						{
							timelineItem._Time = std::stoi(timeStr);
						}

						// 北证A股无成交额 "0930 27.48 535","0931 27.98 2729"

						if (timelineItem._Time == 1300)
							continue;
						// 688分时数据到15:30
						if (timelineItem._Time > 1500)
							break;

						// 解析价格和成交量
						timelineItem._Price = std::stof(values[1]);
						timelineItem._Volume = std::stod(values[2]);
					

						// 累计计算总成交量和总成交额
						totalVolume += timelineItem._Volume;
						

						//// 计算当前时间点的均价 (总成交额除以总成交量)
						//if (totalVolume > 0)
						//{
						//	timelineItem._avgPrice = static_cast<float>(totalAmount / totalVolume);
						//}
						//else
						//{
						//	// 如果成交量为0，均价取当前价格
						//	timelineItem._avgPrice = timelineItem._Price;
						//}
						//timelineItem._avgPrice = timelineItem._avgPrice / 100;
						// 添加到分时数据列表
						stock._vecTimeLine.push_back(timelineItem);
					}
					catch (const std::exception& e)
					{
						TRACE("解析分时数据项异常: %s\n", e.what());
						// 继续解析下一项
					}
				}
                else if (values.size() == 4)
                {
                    try
                    {
                        TLINE_DATA timelineItem;
                        
                        // 解析时间 (格式通常为 HH:MM)
                        std::string timeStr = values[0];
                        size_t colonPos = timeStr.find(':');
                        if (colonPos != std::string::npos)
                        {
                            int hour = std::stoi(timeStr.substr(0, colonPos));
                            int minute = std::stoi(timeStr.substr(colonPos + 1));
                            timelineItem._Time = hour * 100 + minute;
                        }
                        else
                        {
                            timelineItem._Time = std::stoi(timeStr);
                        }

                        // 北证A股无成交额 "0930 27.48 535","0931 27.98 2729"

                        if(timelineItem._Time == 1300)
                            continue;
                        // 688分时数据到15:30
                        if (timelineItem._Time > 1500)
                            break;
                        
                        // 解析价格和成交量
                        timelineItem._Price = std::stof(values[1]);
                        timelineItem._Volume = std::stod(values[2]);        // 688股票单位为股，其他为手
                        timelineItem._Amount = std::stod(values[3]);
                        
                        // 累计计算总成交量和总成交额
                        totalVolume += timelineItem._Volume;
                        totalAmount += timelineItem._Amount;
                        
                        // 计算当前时间点的均价 (总成交额除以总成交量)
                        if (totalVolume > 0)
                        {
                            timelineItem._avgPrice = static_cast<float>(totalAmount / totalVolume);
                        }
                        else
                        {
                            // 如果成交量为0，均价取当前价格
                            timelineItem._avgPrice = timelineItem._Price;
                        }

                        if (stockCode.substr(0, 2) != "68")
                             timelineItem._avgPrice = timelineItem._avgPrice / 100;

                        // 添加到分时数据列表
                        stock._vecTimeLine.push_back(timelineItem);
                    }
                    catch (const std::exception& e)
                    {
                        TRACE("解析分时数据项异常: %s\n", e.what());
                        // 继续解析下一项
                    }
                }
            }
            
            TRACE("成功解析 %s 的分时数据，共 %d 项\n", 
                stockCode.c_str(), (int)stock._vecTimeLine.size());
        }
        
        if (!stockFound)
        {
            TRACE("未找到股票: %s\n", stockCode.c_str());
            return false;
        }
        
        // 通知视图刷新??????
        // m_pDoc->UpdateAllViews(NULL);
        m_pDoc->SafeUpdateAllViews();
        return true;
    }
    catch (const std::exception& e)
    {
        TRACE("解析分时数据异常: %s\n", e.what());
        return false;
    }
}

// 下载大盘分时数据
bool CNetData::DownloadMarketTimelineData()
{
    if (!m_pDoc || !m_timerRunning)
    {
        TRACE("文档指针为空或线程已停止运行\n");
        return false;
    }
    
    // 使用原子操作检查标志
    if (!m_pMarketTimelineHttp)
    {
        TRACE("大盘分时数据HTTP对象为空\n");
        return false;
    }
    
    bool result = true;
    
    // 下载各个大盘指数的分时数据
    if (!DownloadSingleMarketTimelineData("1A0001", INDEX_SH))
        result = false;
    
    // 每次调用前检查线程是否仍在运行
    if (!m_timerRunning) return false;
    
    if (!DownloadSingleMarketTimelineData("399001", INDEX_SZ))
        result = false;
    
    if (!m_timerRunning) return false;
    
    if (!DownloadSingleMarketTimelineData("399006", INDEX_GEM))
        result = false;
    
    if (!m_timerRunning) return false;
    
    if (!DownloadSingleMarketTimelineData("1B0688", INDEX_STAR))
        result = false;
    
    if (!m_timerRunning) return false;
    
    if (!DownloadSingleMarketTimelineData("899050", INDEX_BJ))
        result = false;
    
    return result;
}
// http://d.10jqka.com.cn/v6/time/hs_1A0001/last.js               行情数据
// http://d.10jqka.com.cn/v6/time/hs_399001/last.js
// http://d.10jqka.com.cn/v6/time/hs_399006/last.js
// 下载单个大盘指数的分时数据
bool CNetData::DownloadSingleMarketTimelineData(const std::string& marketCode, MarketIndexType indexType)
{
    if (!m_pMarketTimelineHttp)
    {
        TRACE("大盘分时数据HTTP对象为空\n");
        return false;
    }
    
    if (!m_timerRunning)
    {
        TRACE("线程已停止运行，不再下载数据\n");
        return false;
    }
    
    try
    {
        // 构建URL，请求格式根据不同市场有所不同
        std::string strUrl;
        std::string prefix;
        
        // 再次检查运行状态，防止线程在URL构建过程中被终止
        if (!m_timerRunning)
        {
            TRACE("线程在URL构建过程中被终止\n");
            return false;
        }
        
        // 根据指数类型构建URL
        switch (indexType)
        {
        case INDEX_SH:
        case INDEX_SZ:
        case INDEX_GEM:
        case INDEX_STAR:
            prefix = "hs_";
            strUrl = "https://d.10jqka.com.cn/v6/time/" + prefix + marketCode + "/last.js";
            break;
        case INDEX_BJ:
            prefix = "151_";
            strUrl = "https://d.10jqka.com.cn/v6/time/" + prefix + marketCode + "/last.js";
            break;
        default:
            TRACE("未知的市场指数类型: %d\n", (int)indexType);
            return false;
        }
        
       
        TRACE("请求大盘分时数据URL: %s\n", strUrl.c_str());
        
        // 最后一次检查HTTP对象和线程状态，防止在连接过程中被终止
        if (!m_pMarketTimelineHttp || !m_timerRunning)
        {
            TRACE("HTTP对象已释放或线程已停止运行\n");
            return false;
        }
        
        // 连接到服务器
        if (!m_pMarketTimelineHttp->ConnectHttpServer("https://d.10jqka.com.cn"))
        {
            HttpErrorCode errorCode = m_pMarketTimelineHttp->GetLastError();
            TRACE("连接大盘分时数据服务器失败，错误码: %d\n", errorCode);
            return false;
        }
        
        // 在发送请求前再次检查HTTP对象和线程状态
        if (!m_pMarketTimelineHttp || !m_timerRunning)
        {
            TRACE("HTTP对象已释放或线程已停止运行\n");
            return false;
        }
        
         // 发送HTTP GET请求
        CString response;
        BOOL ret = m_pMarketTimelineHttp->Request(strUrl.c_str(), HttpGet, response);
        if (!ret)
        {
            // 仅当HTTP对象仍然有效时才获取错误码
            if (m_pMarketTimelineHttp)
            {
                HttpErrorCode errorCode = m_pMarketTimelineHttp->GetLastError();
                TRACE("HTTP请求失败，错误码: %d\n", errorCode);
            }
            else
            {
                TRACE("HTTP请求失败，HTTP对象已释放\n");
            }
            return false;
        }

        if (response.IsEmpty())
        {
            TRACE("HTTP响应为空\n");
            return false;
        }
        
        // 最后一次检查
        if (!m_timerRunning || !m_pDoc)
        {
            TRACE("线程已停止或文档对象已释放，不再解析数据\n");
            return false;
        }
        
        // 解析响应数据
        return this->ParseMarketTimelineData(response.GetBuffer(0), indexType);
    }
    catch (const std::exception& e)
    {
        TRACE("下载大盘分时数据异常: %s\n", e.what());
        return false;
    }
}

// 解析大盘分时数据
bool CNetData::ParseMarketTimelineData(const std::string& data, MarketIndexType indexType)
{
    if (data.empty() || !m_pDoc)
    {
        TRACE("数据为空或文档指针为空\n");
        return false;
    }
    
    try
    {
        // 打印接收到的数据前50个字符
        TRACE("接收大盘分时数据: %s...\n", data.substr(0, 50).c_str());
        
        // 同花顺的数据格式: quotebridge_v6_time_hs_1A0001_last({"hs_1A0001":{...}})
        // 首先去除jsonp前缀和后缀括号
        size_t dataStart = data.find('(');
        size_t dataEnd = data.rfind(')');
        
        if (dataStart == std::string::npos || dataEnd == std::string::npos)
        {
            TRACE("无法找到大盘分时数据的JSON部分\n");
            return false;
        }
        
        // 提取JSON部分
        std::string jsonData = data.substr(dataStart + 1, dataEnd - dataStart - 1);
        
        // 查找数据字段
        std::string dataField = "\"data\":\"";
        size_t dataFieldPos = jsonData.find(dataField);
        if (dataFieldPos == std::string::npos)
        {
            TRACE("无法找到大盘分时数据的data字段\n");
            return false;
        }
        
        // 查找data字段结束位置（以双引号结束）
        dataFieldPos += dataField.length();
        size_t dataFieldEnd = jsonData.find("\"", dataFieldPos);
        if (dataFieldEnd == std::string::npos)
        {
            TRACE("无法找到大盘分时数据的data字段结束位置\n");
            return false;
        }
        
        // 提取分时数据字符串
        std::string timelineDataStr = jsonData.substr(dataFieldPos, dataFieldEnd - dataFieldPos);
        
        // 读取昨收价，用于计算涨跌幅
        float preClose = 0.0f;
        std::string preField = "\"pre\":\"";
        size_t preFieldPos = jsonData.find(preField);
        if (preFieldPos != std::string::npos)
        {
            preFieldPos += preField.length();
            size_t preFieldEnd = jsonData.find("\"", preFieldPos);
            if (preFieldEnd != std::string::npos)
            {
                std::string preCloseStr = jsonData.substr(preFieldPos, preFieldEnd - preFieldPos);
                try
                {
                    preClose = std::stof(preCloseStr);
                    TRACE("昨收价: %f\n", preClose);
                }
                catch (...)
                {
                    TRACE("解析昨收价异常\n");
                }
            }
        }
        
        // 根据大盘指数类型获取对应的数据列表
        std::vector<MarketIndexData>* pIndexData = nullptr;
        
        switch (indexType)
        {
        case INDEX_SH:
            pIndexData = &m_pDoc->m_MarketData._vecIndexSH;
            break;
        case INDEX_SZ:
            pIndexData = &m_pDoc->m_MarketData._vecIndexSZ;
            break;
        case INDEX_GEM:
            pIndexData = &m_pDoc->m_MarketData._vecIndexGEM;
            break;
        case INDEX_STAR:
            pIndexData = &m_pDoc->m_MarketData._vecIndexSTAR;
            break;
        case INDEX_BJ:
            pIndexData = &m_pDoc->m_MarketData._vecIndexBJ;
            break;
        default:
            TRACE("未知的市场指数类型: %d\n", (int)indexType);
            return false;
        }
        
        // 清空原有数据
        pIndexData->clear();
        
        // 按分号分割时间点数据
        std::vector<std::string> timePoints;
        size_t pos = 0;
        while ((pos = timelineDataStr.find(';')) != std::string::npos)
        {
            std::string timePoint = timelineDataStr.substr(0, pos);
            timelineDataStr.erase(0, pos + 1);
            
            if (!timePoint.empty())
            {
                timePoints.push_back(timePoint);
            }
        }
        // 处理最后一个时间点
        if (!timelineDataStr.empty()) {
            timePoints.push_back(timelineDataStr);
        }
        
        TRACE("解析到 %d 个时间点数据\n", (int)timePoints.size());
        
        // 遍历处理每个时间点的数据
        for (const auto& timePoint : timePoints) {
            // 按逗号分割字段
            std::vector<std::string> fields;
            std::stringstream ss(timePoint);
            std::string field;
            
            while (getline(ss, field, ',')) {
                fields.push_back(field);
            }
            
            // 检查字段数量是否足够
            if (fields.size() < 5) {
                TRACE("字段数量不足，跳过: %s\n", timePoint.c_str());
                continue;
            }
            
            try {
                MarketIndexData indexItem;
                
                // 解析时间 (格式为 "0930")
                std::string timeStr = fields[0];
                indexItem._Time = std::stoi(timeStr);
              
                // 昨收指数
                indexItem._preIndex = preClose;

                // 当前指数
                indexItem._currIndex = std::stof(fields[1]);
                
                // 成交量
                indexItem._Volume = std::stod(fields[2]);
                
                // 领先指数
                indexItem._leadIndex = std::stof(fields[3]);
               
                // 成交额（如果有）
                if (fields.size() > 4) {
                    indexItem._Amount = std::stod(fields[4]);
                }
                
                // 添加到列表
                pIndexData->push_back(indexItem);
            }
            catch (const std::exception& e) {
                TRACE("解析时间点数据异常: %s, 错误: %s\n", timePoint.c_str(), e.what());
                continue;
            }
        }
        
        TRACE("成功解析大盘指数 %d 的分时数据，共 %d 项\n", (int)indexType, (int)pIndexData->size());
        
        // 使用消息机制通知UI更新，而不是直接调用SafeUpdateAllViews
        // m_pDoc->SafeUpdateAllViews(); // 原代码，可能导致启动错误
        
        // 获取主窗口句柄并发送消息
        if (AfxGetMainWnd() && ::IsWindow(AfxGetMainWnd()->GetSafeHwnd()))
        {
            // 使用PostMessage发送异步消息，避免阻塞数据下载线程
            ::PostMessage(AfxGetMainWnd()->GetSafeHwnd(), WM_USER_DATA_UPDATED, 0, 0);
            TRACE("已发送数据更新消息 WM_USER_DATA_UPDATED 到主窗口\n");
        }
        else
        {
            TRACE("警告：无法获取主窗口句柄，无法通知UI更新\n");
        }
        
        return true;
    }
    catch (const std::exception& e)
    {
        TRACE("解析大盘分时数据异常: %s\n", e.what());
        return false;
    }
}

// 下载主力资金流向数据
bool CNetData::DownloadMainFundFlowData()
{
    if (!m_pMainFundFlowHttp || !m_pDoc)
    {
        TRACE("主力资金流HTTP对象或文档指针为空\n");
        return false;
    }
    
    try
    {
        std::string strUrl = "https://apphq.longhuvip.com/w1/api/index.php?Order=9&st=6000&a=KanPanNew&c=YiDongKanPan&apiv=w29&Type=1";

        // 连接到服务器
        if (!m_pMainFundFlowHttp->IsConnect())
        {
            if (!m_pMainFundFlowHttp->ConnectHttpServer("https://apphq.longhuvip.com"))
            {
                TRACE("连接主力资金流服务器失败\n");
                return false;
            }
        }
        
        // 发送HTTP GET请求
        CString response;
        BOOL ret = m_pMainFundFlowHttp->Request(strUrl.c_str(), HttpGet, response);
        if (!ret)
        {
            TRACE("HTTP请求失败，错误码: %d\n", m_pMainFundFlowHttp->GetLastError());
            return false;
        }

        if (response.IsEmpty())
        {
            TRACE("HTTP响应为空\n");
            return false;
        }
        
        // 解析响应数据
        return this->ParseMainFundFlowData(response.GetBuffer(0));
    }
    catch (const std::exception& e)
    {
        TRACE("下载主力资金流数据异常: %s\n", e.what());
        return false;
    }
}

// 解析主力资金流向数据
bool CNetData::ParseMainFundFlowData(const std::string& data)
{
    if (data.empty() || !m_pDoc)
    {
        TRACE("数据为空或文档指针为空\n");
        return false;
    }
    
    try
    {
        // 打印接收到的数据前100个字符
        TRACE("接收主力资金流向数据: %s...\n", data.substr(0, 100).c_str());
        
        // 解析JSON
        JSONValue* jsonValue = JSON::Parse(data.c_str());
        if (!jsonValue)
        {
            TRACE("解析JSON数据失败\n");
            return false;
        }

        // 确保是对象类型
        if (!jsonValue->IsObject())
        {
            TRACE("JSON数据不是一个对象\n");
            delete jsonValue;
            return false;
        }

        JSONObject root = jsonValue->AsObject();
        
        // 检查是否包含List字段
        if (root.find(L"List") == root.end() || !root[L"List"]->IsArray())
        {
            TRACE("JSON数据中未找到List数组\n");
            delete jsonValue;
            return false;
        }
        
        JSONArray stockList = root[L"List"]->AsArray();
        int count = 0;
        bool updated = false;
        
        // 遍历股票列表
        for (size_t i = 0; i < stockList.size(); i++)
        {
            if (!stockList[i]->IsObject())
                continue;
                
            JSONObject stockObj = stockList[i]->AsObject();
            
            // 获取股票代码
            if (stockObj.find(L"stock_code") == stockObj.end() || !stockObj[L"stock_code"]->IsString())
                continue;
                
            std::wstring wStockCode = stockObj[L"stock_code"]->AsString();
            std::string stockCode(wStockCode.begin(), wStockCode.end());
            
            // 获取主力资金流入
            double inflow = 0.0;
            if (stockObj.find(L"ZJB") != stockObj.end() && stockObj[L"ZJB"]->IsNumber())
            {
                inflow = stockObj[L"ZJB"]->AsNumber();
            }
            
            // 获取主力资金流出
            double outflow = 0.0;
            if (stockObj.find(L"ZJS") != stockObj.end() && stockObj[L"ZJS"]->IsNumber())
            {
                outflow = stockObj[L"ZJS"]->AsNumber();
            }
            
            // 更新到对应的股票
            bool stockFound = false;
            
                
            // 先尝试直接通过GetStockIndex查找股票
            int stockIndex = m_pDoc->GetStockIndex(stockCode);
            
            // 如果找到了股票索引，更新主力资金流向数据
            if (stockIndex >= 0 && stockIndex < static_cast<int>(m_pDoc->m_vecStocks.size()))
            {
                // 将金额转换为万元单位
                m_pDoc->m_vecStocks[stockIndex]._MainInflow = inflow;  // 主力流入金额（元）
                m_pDoc->m_vecStocks[stockIndex]._MainOutflow = outflow; // 主力流出金额（元）
                
                stockFound = true;
                updated = true;
                count++;
            }
        }
        
        // 释放JSON对象
        delete jsonValue;
        
        TRACE("共更新了 %d 只股票的主力资金流向数据\n", count);
        
        // 如果有更新，通知视图刷新????????
        if (updated)
        {
            m_pDoc->SafeUpdateAllViews();
            return true;
        }
        else
        {
            TRACE("没有任何股票的主力资金流向数据被更新\n");
            return false;
        }
    }
    catch (const std::exception& e)
    {
        TRACE("解析主力资金流向数据异常: %s\n", e.what());
        return false;
    }
}

// 下载集合竞价数据
bool CNetData::DownloadAuctionData()
{
    if (!m_pAuctionHttp || !m_pDoc)
    {
        TRACE("集合竞价HTTP对象或文档指针为空\n");
        return false;
    }
    
    try
    {
        // 获取所有股票代码
        std::vector<std::string> stockCodes;
        size_t stockCount = m_pDoc->GetStockCount();
        for (size_t i = 0; i < stockCount; ++i)
        {
            const StockData* pStock = m_pDoc->GetStock(i);
            if (pStock && !pStock->_Code.empty())
            {
                stockCodes.push_back(pStock->_Code);
            }
        }
        
        if (stockCodes.empty())
        {
            TRACE("股票列表为空，无法下载集合竞价数据\n");
            return false;
        }
        
        // 分批下载
        return DownloadBatchAuctionData(stockCodes);
    }
    catch (const std::exception& e)
    {
        TRACE("下载集合竞价数据异常: %s\n", e.what());
        return false;
    }
}

// 批量下载集合竞价数据
bool CNetData::DownloadBatchAuctionData(const std::vector<std::string>& stockCodes)
{
    if (!m_pAuctionHttp || !m_pDoc || stockCodes.empty())
    {
        TRACE("集合竞价HTTP对象、文档指针为空或股票代码列表为空\n");
        return false;
    }
    
    try
    {
        // 每批次最多请求50个股票
        const size_t batchSize = 50;
        size_t totalBatches = (stockCodes.size() + batchSize - 1) / batchSize;
        bool success = true;
        
        for (size_t batch = 0; batch < totalBatches; ++batch)
        {
            // 计算当前批次的起始和结束索引
            size_t startIdx = batch * batchSize;
            size_t endIdx = (startIdx + batchSize < stockCodes.size()) ? (startIdx + batchSize) : stockCodes.size();
            
            // 构建URL请求参数
            std::string codes;
            for (size_t i = startIdx; i < endIdx; ++i)
            {
                std::string stockCode = stockCodes[i];
                std::string market = GetMarketPrefix(stockCode);
                if (!market.empty())
                {
                    if (!codes.empty())
                    {
                        codes += ",";
                    }
                    codes += market + stockCode;
                }
            }
            
            if (codes.empty())
            {
                TRACE("当前批次没有有效的股票代码\n");
                continue;
            }
            
            // 构建URL
            std::string strUrl = "http://hq.sinajs.cn/list=" + codes;
            
            TRACE("请求集合竞价数据URL: %s\n", strUrl.c_str());
            
            // 添加请求头，模拟浏览器
            m_pAuctionHttp->AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            m_pAuctionHttp->AddHeader("Accept", "*/*");
            m_pAuctionHttp->AddHeader("Referer", "https://finance.sina.com.cn/");
            
            // 连接到服务器
            if (!m_pAuctionHttp->ConnectHttpServer("http://hq.sinajs.cn"))
            {
                TRACE("连接集合竞价数据服务器失败\n");
                success = false;
                continue;
            }
            
            // 发送HTTP GET请求
            CString response;
            BOOL ret = m_pAuctionHttp->Request(strUrl.c_str(), HttpGet, response);
            if (!ret)
            {
                TRACE("HTTP请求失败，错误码: %d\n", m_pAuctionHttp->GetLastError());
                success = false;
                continue;
            }

            if (response.IsEmpty())
            {
                TRACE("HTTP响应为空\n");
                success = false;
                continue;
            }
            
            // 解析响应数据
            if (!ParseAuctionData(response.GetBuffer(0)))
            {
                TRACE("解析集合竞价数据失败\n");
                success = false;
            }
            
            // 批次之间添加短暂延迟，避免请求过于频繁
            if (batch < totalBatches - 1)
            {
                Sleep(200);
            }
        }
        
        return success;
    }
    catch (const std::exception& e)
    {
        TRACE("下载批量集合竞价数据异常: %s\n", e.what());
        return false;
    }
}

// 解析集合竞价数据
bool CNetData::ParseAuctionData(const std::string& data)
{
    if (data.empty() || !m_pDoc)
    {
        TRACE("数据为空或文档指针为空\n");
        return false;
    }
    
    try
    {
        // 按行分割数据
        std::istringstream iss(data);
        std::string line;
        bool updated = false;
        size_t count = 0;
        
        while (std::getline(iss, line))
        {
            // 新浪API格式：var hq_str_sh600000="股票名称,开盘价,昨收价,当前价,...";
            // 查找股票代码
            size_t varPos = line.find("var hq_str_");
            if (varPos == std::string::npos)
                continue;
            
            size_t codeStartPos = varPos + 11; // 跳过 "var hq_str_"
            size_t codeEndPos = line.find("=", codeStartPos);
            if (codeEndPos == std::string::npos)
                continue;
            
            // 提取股票代码（包含市场前缀，如sh600000）
            std::string fullCode = line.substr(codeStartPos, codeEndPos - codeStartPos);
            
            // 检查是否有数据
            size_t dataStartPos = line.find("\"", codeEndPos);
            size_t dataEndPos = line.find_last_of("\"");
            if (dataStartPos == std::string::npos || dataEndPos == std::string::npos || dataStartPos == dataEndPos)
                continue;
            
            // 提取数据部分
            std::string stockData = line.substr(dataStartPos + 1, dataEndPos - dataStartPos - 1);
            
            // 分割数据字段
            std::vector<std::string> fields;
            std::istringstream fieldStream(stockData);
            std::string field;
            
            while (std::getline(fieldStream, field, ','))
            {
                fields.push_back(field);
            }
            
            // 新浪API返回的集合竞价数据格式可能不同，这里以常见的股票实时数据格式为例
            // 一般格式：股票名称,开盘价,昨收价,当前价,最高价,最低价,买一价,卖一价,成交量,成交额,买一量,买一价,买二量,买二价,...
            if (fields.size() < 32)
            {
                TRACE("集合竞价数据字段不足: %s\n", fullCode.c_str());
                continue;
            }
            
            // 提取纯股票代码（去除市场前缀）
            std::string stockCode = fullCode;
            if (stockCode.substr(0, 2) == "sh" || stockCode.substr(0, 2) == "sz" || stockCode.substr(0, 2) == "bj")
                stockCode = stockCode.substr(2);
            
            // 构建集合竞价数据
            AUC_DATA auctionData;
            
            try
            {
                // 获取当前时间并转换为HHMM格式（线程安全版本）
                time_t now = time(nullptr);
                tm timeinfo;
#ifdef _WIN32
                localtime_s(&timeinfo, &now);  // Windows安全版本
#else
                localtime_r(&now, &timeinfo);  // POSIX安全版本
#endif
                auctionData._Time = timeinfo.tm_hour * 100 + timeinfo.tm_min;
                
                // 设置集合竞价价格
                if (!fields[1].empty())
                    auctionData._Price = std::stof(fields[1]); // 开盘价通常为集合竞价价格
                else if (!fields[3].empty())
                    auctionData._Price = std::stof(fields[3]); // 当前价
                
                // 设置集合竞价量
                if (!fields[8].empty())
                {
                    double volume = std::stod(fields[8]); // 成交量
                    auctionData._MatchedVol = static_cast<float>(volume);
                }
                
                // 计算未匹配量（当前可用的API数据中可能无法准确获取，这里使用买一卖一的量之和作为参考）
                float unmatched = 0.0f;
                if (fields.size() > 10 && !fields[10].empty())
                    unmatched += std::stof(fields[10]); // 买一量
                if (fields.size() > 20 && !fields[20].empty())
                    unmatched += std::stof(fields[20]); // 卖一量
                auctionData._UnMatchedVol = unmatched;
                
                // 确定方向（根据买一卖一的量决定）
                float bidVol = 0.0f;
                float askVol = 0.0f;
                if (fields.size() > 10 && !fields[10].empty())
                    bidVol = std::stof(fields[10]); // 买一量
                if (fields.size() > 20 && !fields[20].empty())
                    askVol = std::stof(fields[20]); // 卖一量
                
                auctionData._Direction = (bidVol > askVol) ? 1 : 0; // 买入方向：1，卖出方向：0
            }
            catch (const std::exception& e)
            {
                TRACE("解析集合竞价数据字段异常: %s, %s\n", fullCode.c_str(), e.what());
                continue;
            }
            
            // 更新到对应的股票
            // 使用GetStockIndex方法快速查找股票索引
            int stockIndex = m_pDoc->GetStockIndex(stockCode);
            
            if (stockIndex >= 0 && stockIndex < static_cast<int>(m_pDoc->m_vecStocks.size()))
            {
                StockData& stock = m_pDoc->m_vecStocks[stockIndex];
                
                // 添加新的集合竞价数据
                stock._vecAucData.push_back(auctionData);
                
                // 如果集合竞价数据超过100条，则保留最新的100条
                if (stock._vecAucData.size() > 100)
                {
                    stock._vecAucData.erase(stock._vecAucData.begin(), stock._vecAucData.begin() + (stock._vecAucData.size() - 100));
                }
                
                updated = true;
                count++;
                
                // 每100个打印一次进度
                if (count % 100 == 0)
                {
                    TRACE("已更新 %d 只股票的集合竞价数据\n", (int)count);
                }
            }
        }
        
        TRACE("共更新了 %d 只股票的集合竞价数据\n", (int)count);
        
        // 如果有更新，通知视图刷新
        if (updated)
        {
            //m_pDoc->UpdateAllViews(NULL);
            m_pDoc->SafeUpdateAllViews();
            return true;
        }
        else
        {
            TRACE("没有任何股票的集合竞价数据被更新\n");
            return false;
        }
    }
    catch (const std::exception& e)
    {
        TRACE("解析集合竞价数据异常: %s\n", e.what());
        return false;
    }
}

bool CNetData::DownloadMarketTimeline(const std::string& market)
{
    if (!m_pMarketTimelineHttp || !m_pDoc)
    {
        TRACE("市场分时数据HTTP对象或文档指针为空\n");
        return false;
    }
    
    try
    {
        // 确认市场代码
        std::string marketCode;
        if (market == "sh")
            marketCode = "1";
        else if (market == "sz")
            marketCode = "2";
        else
        {
            TRACE("无效的市场代码: %s\n", market.c_str());
            return false;
        }
        
        // 构建URL，请求格式：https://web.ifzq.gtimg.cn/appstock/app/minute/market?code=1
        std::string strUrl = "https://web.ifzq.gtimg.cn/appstock/app/minute/market?code=" + marketCode;
        
        // 添加时间戳防止缓存
        strUrl += "&r=" + std::to_string(time(nullptr));
        
        TRACE("请求市场分时数据URL: %s\n", strUrl.c_str());
        
        // 添加请求头，模拟浏览器
        m_pMarketTimelineHttp->AddHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        m_pMarketTimelineHttp->AddHeader("Accept", "*/*");
        m_pMarketTimelineHttp->AddHeader("Referer", "https://stockapp.finance.qq.com/");
        
        // 连接到服务器
        if (!m_pMarketTimelineHttp->ConnectHttpServer("https://web.ifzq.gtimg.cn"))
        {
            TRACE("连接市场分时数据服务器失败\n");
            return false;
        }
        
        // 发送HTTP GET请求
        CString response;
        BOOL ret = m_pMarketTimelineHttp->Request(strUrl.c_str(), HttpGet, response);
        if (!ret)
        {
            TRACE("HTTP请求失败，错误码: %d\n", m_pMarketTimelineHttp->GetLastError());
            return false;
        }

        if (response.IsEmpty())
        {
            TRACE("HTTP响应为空\n");
            return false;
        }
        
        // 解析响应数据
        return this->ParseMarketTimeline(market, response.GetBuffer(0));
    }
    catch (const std::exception& e)
    {
        TRACE("下载市场分时数据异常: %s\n", e.what());
        return false;
    }
} 

// 解析市场分时数据
bool CNetData::ParseMarketTimeline(const std::string& market, const std::string& data)
{
    if (data.empty() || !m_pDoc)
    {
        TRACE("数据为空或文档指针为空\n");
        return false;
    }
    
    try
    {
        // 打印接收到的数据前50个字符
        TRACE("接收市场分时数据: %s...\n", data.substr(0, 50).c_str());
        
        // 查找数据部分
        size_t dataPos = data.find("\"data\":");
        if (dataPos == std::string::npos)
        {
            TRACE("未找到市场分时数据起始标记\n");
            return false;
        }
        
        // 查找时间数据段
        size_t timePos = data.find("\"data\":[[", dataPos);
        if (timePos == std::string::npos)
        {
            TRACE("未找到市场分时数据数组\n");
            return false;
        }
        
        // 查找数据段结束位置
        size_t endPos = data.find("]]", timePos);
        if (endPos == std::string::npos)
        {
            TRACE("未找到市场分时数据结束标记\n");
            return false;
        }
        
        // 提取分时数据数组
        std::string timelineData = data.substr(timePos + 8, endPos - timePos - 8);
        
        // 创建市场数据对象
        std::vector<MarketIndexData>* pMarketData = nullptr;
        if (market == "sh")
            pMarketData = &m_pDoc->m_MarketData._vecIndexSHA;
        else if (market == "sz")
            pMarketData = &m_pDoc->m_MarketData._vecIndexSZA;
        else
        {
            TRACE("未知的市场类型: %s\n", market.c_str());
            return false;
        }
        
        // 清空原有数据
        pMarketData->clear();
        
        // 解析数据项
        std::istringstream ss(timelineData);
        std::string item;
        while (std::getline(ss, item, ','))
        {
            if (item.empty())
                continue;
                
            // 去除中括号和引号
            item.erase(std::remove(item.begin(), item.end(), '['), item.end());
            item.erase(std::remove(item.begin(), item.end(), ']'), item.end());
            item.erase(std::remove(item.begin(), item.end(), '\"'), item.end());
            
            // 分割子项
            std::istringstream itemSS(item);
            std::string subItem;
            std::vector<std::string> values;
            while (std::getline(itemSS, subItem, ','))
            {
                values.push_back(subItem);
            }
            
            // 分时数据格式通常为: [时间,价格,成交量,成交额]
            if (values.size() >= 3)
            {
                try
                {
                    MarketIndexData timelineItem;
                    
                    // 解析时间 (格式通常为 HH:MM)
                    std::string timeStr = values[0];
                    size_t colonPos = timeStr.find(':');
                    if (colonPos != std::string::npos)
                    {
                        int hour = std::stoi(timeStr.substr(0, colonPos));
                        int minute = std::stoi(timeStr.substr(colonPos + 1));
                        timelineItem._Time = hour * 100 + minute;
                    }
                    else
                    {
                        timelineItem._Time = std::stoi(timeStr);
                    }
                    
                    // 解析价格、成交量
                    timelineItem._currIndex = std::stof(values[1]);
                    timelineItem._Volume = std::stod(values[2]);
                    
                    // 如果有成交额数据
                    if (values.size() > 3)
                    {
                        timelineItem._Amount = std::stod(values[3]);
                    }
                    
                    // 添加到分时数据列表
                    pMarketData->push_back(timelineItem);
                }
                catch (const std::exception& e)
                {
                    TRACE("解析市场分时数据项异常: %s\n", e.what());
                    // 继续解析下一项
                }
            }
        }
        
        TRACE("成功解析 %s 市场的分时数据，共 %d 项\n", 
            market.c_str(), (int)pMarketData->size());
            
        // 通知视图刷新
        m_pDoc->SafeUpdateAllViews();
        return true;
    }
    catch (const std::exception& e)
    {
        TRACE("解析市场分时数据异常: %s\n", e.what());
        return false;
    }
}

