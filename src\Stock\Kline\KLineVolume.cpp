﻿// KLine1View.cpp: CKLineVolume 类的实现
//

#include "pch.h"
#include "..\framework.h"
#include <time.h>  // 添加time.h头文件
// SHARED_HANDLERS 可以在实现预览、缩略图和搜索筛选器句柄的
// ATL 项目中进行定义，并允许与该项目共享文档代码。
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "..\MainFrm.h"  // 添加对MainFrm.h的引用
#include "KLineVolume.h"
#include "KLineView.h"  // 添加对KLineView的引用

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// 均线菜单ID定义
#define ID_MA_BASE      (WM_USER + 100)
#define ID_MA5          (ID_MA_BASE + 5)
#define ID_MA10         (ID_MA_BASE + 10)
#define ID_MA20         (ID_MA_BASE + 20)
#define ID_MA30         (ID_MA_BASE + 30)
#define ID_MA60         (ID_MA_BASE + 60)
#define ID_MA120        (ID_MA_BASE + 120)
#define ID_MA240        (ID_MA_BASE + 240)
#define ID_MA360        (ID_MA_BASE + 360)



// CKLineVolume

IMPLEMENT_DYNCREATE(CKLineVolume, CView)

BEGIN_MESSAGE_MAP(CKLineVolume, CView)
	// 标准打印命令
	ON_COMMAND(ID_FILE_PRINT, &CView::OnFilePrint)
	ON_COMMAND(ID_FILE_PRINT_DIRECT, &CView::OnFilePrint)
	ON_COMMAND(ID_FILE_PRINT_PREVIEW, &CView::OnFilePrintPreview)
	ON_WM_ERASEBKGND()
	ON_WM_SIZE()
	ON_WM_LBUTTONDOWN()
	ON_WM_MOUSEMOVE()
	ON_WM_LBUTTONUP()
	ON_WM_CONTEXTMENU()
	ON_WM_MOUSEWHEEL()
	ON_WM_KEYDOWN()
	
	// 均线菜单响应
	ON_COMMAND_RANGE(ID_MA5, ID_MA360, OnMAMenuSelect)
	ON_UPDATE_COMMAND_UI_RANGE(ID_MA5, ID_MA360, OnUpdateMAMenu)
END_MESSAGE_MAP()

// CKLineVolume 构造/析构

CKLineVolume::CKLineVolume() noexcept
{
	// 初始化成员变量
	m_nRightAreaWidth = 76;  // 右侧坐标区宽度为100像素
	m_nInfoBarHeight = 30;    // 信息条高度为30像素
	m_nVolumeInfoBarHeight = 30; // 成交量信息条高度为30像素
	m_nButtonBarHeight = 30;  // 按钮区高度为30像素
	m_nButtonWidth = 80;      // 按钮宽度为80像素
	m_bShowButtonBar = TRUE;  // 默认显示按钮区
	
	// K线相关参数初始化
	m_nKLineCount = 0;
	m_fMaxPrice = 0.0;
	m_fMinPrice = 0.0;
	m_fMaxVolume = 0.0;
	m_fDisplayMaxPrice = 0.0;  // 初始化显示区域的最高价
	m_fDisplayMinPrice = 0.0;  // 初始化显示区域的最低价
	m_nBaseKLineWidth = 11;    // 基准K线宽度为11像素
	m_nBaseKLineGap = 1;      // 基准K线间隔为1像素
	m_nKLineWidth = m_nBaseKLineWidth;  // 初始K线宽度
	m_nKLineGap = m_nBaseKLineGap;      // 初始K线间隔
	m_fScaleFactor = 1.0;     // 初始缩放系数为1.0
	m_nDisplayOffset = 0;     // 默认从第一个K线开始显示
	m_nDisplayCount = 0;      // 初始值，会在DrawKLine中计算实际值
	
	// 初始化十字光标
	m_bShowCrossCursor = false;
	m_ptCrossCursor = CPoint(0, 0);
	m_nCursorIndex = -1;
	m_bMouseDown = false;  // 初始化鼠标左键状态为未按下
	
	// 初始化按钮
	InitButtons();
	
	// 初始化均线 - 此时还没有数据，仅创建空结构
	InitMA();
	
	// 初始化成交量均线 - 放在生成数据后，确保m_nKLineCount已正确设置
	InitVolumeMA();
	
	// 计算均线 - 放在数据生成和初始化均线结构后
	CalcMA();
	
	// 计算成交量均线
	CalcVolumeMA();
	
	// 窗口创建完成后，稍后切换到技术指标视图（在PostNcDestroy中处理，确保所有窗口都已创建）
	// 此处不能立即执行切换，因为此时窗口层次结构尚未完全建立
}

CKLineVolume::~CKLineVolume()
{
}

BOOL CKLineVolume::PreCreateWindow(CREATESTRUCT& cs)
{
	// 修改窗口类或样式实现无边框
	cs.style &= ~(WS_BORDER | WS_DLGFRAME | WS_THICKFRAME | WS_CAPTION);
	cs.dwExStyle &= ~(WS_EX_DLGMODALFRAME | WS_EX_CLIENTEDGE | WS_EX_WINDOWEDGE);

	return CView::PreCreateWindow(cs);
}

// CKLineVolume 绘图

void CKLineVolume::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;

	// 获取视图客户区尺寸
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 创建内存DC和位图实现双缓冲
	CDC memDC;
	CBitmap memBitmap;
	memDC.CreateCompatibleDC(pDC);
	memBitmap.CreateCompatibleBitmap(pDC, rectClient.Width(), rectClient.Height());
	CBitmap* pOldBitmap = memDC.SelectObject(&memBitmap);
	
	// 清除背景 - 使用深邃的蓝色替代黑色
	memDC.FillSolidRect(&rectClient, RGB(0, 13, 37)); // 深邃蓝色背景
	
	// 计算左侧K线区域和右侧坐标区域
	int nSplitLineX = rectClient.right - m_nRightAreaWidth;
	
	m_rectKLineArea = CRect(rectClient.left, rectClient.top, nSplitLineX, rectClient.bottom);
	m_rectCoordArea = CRect(nSplitLineX, rectClient.top, rectClient.right, rectClient.bottom);
	
	// 绘制深红色分割线
	CPen penSplitLine(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细实线
	CPen* pOldPen = memDC.SelectObject(&penSplitLine);
	
	memDC.MoveTo(nSplitLineX, rectClient.top);
	memDC.LineTo(nSplitLineX, rectClient.bottom);
	
	// 绘制左侧四个区域
	// 1. 信息条区域
	DrawInfoBar(&memDC);
	
	// 2. K线主图区域
	DrawMainChart(&memDC);
	
	// 3. 成交量信息条区域
	DrawVolumeInfoBar(&memDC);
	
	// 4. 成交量区域
	DrawVolumeChart(&memDC);
	
	// 5. 按钮区域（仅当m_bShowButtonBar为true时绘制）
	DrawButtonBar(&memDC);
	
	// 绘制十字光标 - 确保m_bShowCrossCursor为true且鼠标在有效区域内
	if (m_bShowCrossCursor && 
	    (m_rectMainChart.PtInRect(m_ptCrossCursor) || m_rectVolume.PtInRect(m_ptCrossCursor)))
	{
		DrawCrossCursor(&memDC);
	}
	
	// 将内存DC的内容复制到屏幕
	pDC->BitBlt(0, 0, rectClient.Width(), rectClient.Height(), &memDC, 0, 0, SRCCOPY);
	
	// 恢复原来的画笔和位图
	memDC.SelectObject(pOldPen);
	memDC.SelectObject(pOldBitmap);
	
	// 释放资源
	memBitmap.DeleteObject();
	memDC.DeleteDC();
}

// 设置黑色背景
BOOL CKLineVolume::OnEraseBkgnd(CDC* pDC)
{
	// 返回TRUE表示已处理擦除背景消息
	// 我们在OnDraw中使用双缓冲绘制，不需要这里擦除背景
	return TRUE;
}

// 设置股票代码
void CKLineVolume::SetStockCode(const CString& strCode)
{
	m_strCode = strCode;
	
	// 加载新的K线数据
	m_vecKLine.clear();
	LoadKLineDataFromLocalFile(m_strCode, m_vecKLine);
	
	// 更新K线数量
	m_nKLineCount = m_vecKLine.size();
	
	// 安全检查：如果没有加载到K线数据，则直接返回
	if (m_nKLineCount <= 0)
	{
		// 重置关键变量
		m_nDisplayCount = 0;
		m_nDisplayOffset = 0;
		m_fMaxPrice = 0.0;
		m_fMinPrice = 0.0;
		m_fMaxVolume = 0.0;
		
		// 记录日志
		TRACE(_T("SetStockCode: 未能加载股票[%s]的K线数据\n"), strCode);
		
		// 重绘视图
		Invalidate();
		return;
	}
	
	// 计算最高价、最低价和最大成交量
	CalcMinMaxValue();
	
	// 计算可显示的K线数量和显示偏移量
	// 检查区域是否已初始化
	int nDisplayCount = 0;
	
	if (m_rectMainChart.Width() > 0)
	{
		// 正常情况：区域已初始化，可以计算实际可显示的K线数量
		nDisplayCount = (m_rectMainChart.Width() - 10) / (m_nKLineWidth + m_nKLineGap);
	}
	else
	{
		// 第一次切换K线视图时，区域可能还未初始化
		// 获取当前客户区大小来估算可显示的K线数量
		CRect rectClient;
		GetClientRect(&rectClient);
		
		// 如果能获取客户区大小，则使用它估算
		if (rectClient.Width() > 0)
		{
			// 预估K线区域宽度（总宽度减去右侧坐标区宽度）
			int estimatedWidth = rectClient.Width() - m_nRightAreaWidth - 10;
			nDisplayCount = estimatedWidth / (m_nKLineWidth + m_nKLineGap);
		}
		
		// 如果仍然无法估算，则使用默认值
		if (nDisplayCount <= 0)
		{
			nDisplayCount = 60; // 默认显示约60根K线
			TRACE(_T("SetStockCode: 无法确定区域大小，使用默认显示数量: %d\n"), nDisplayCount);
		}
	}
	
	// 确保显示数量合理
	nDisplayCount = max(1, min(nDisplayCount, m_nKLineCount));
	
	// 设置偏移量，显示最近周期的K线
	m_nDisplayOffset = max(0, m_nKLineCount - nDisplayCount);
	m_nDisplayCount = nDisplayCount;
	
	// 安全检查：确保索引有效
	if (m_nDisplayOffset + m_nDisplayCount > m_nKLineCount)
	{
		// 调整显示数量，不允许超出数组范围
		m_nDisplayCount = m_nKLineCount - m_nDisplayOffset;
		TRACE(_T("SetStockCode: 调整显示数量 - 偏移量=%d, 新显示数量=%d\n"), 
			m_nDisplayOffset, m_nDisplayCount);
	}
	
	// 添加日志输出
	TRACE(_T("SetStockCode: 股票[%s] 总K线数量=%d, 可显示数量=%d, 偏移量=%d, 区域宽度=%d\n"), 
		strCode, m_nKLineCount, m_nDisplayCount, m_nDisplayOffset, m_rectMainChart.Width());
	
	// 初始化均线结构（确保均线数组大小与K线数量匹配）
	InitMA();
	
	// 初始化成交量均线结构（确保均线数组大小与K线数量匹配）
	InitVolumeMA();
	
	// 计算均线
	CalcMA();
	
	// 计算成交量均线
	CalcVolumeMA();
	
	// 重置十字光标状态，避免切换股票后光标位置不正确
	m_bShowCrossCursor = false;
	m_nCursorIndex = -1;
	m_bMouseDown = false;

	// 重绘视图
	Invalidate();
}


CStockDoc* CKLineVolume::GetDocument() const 
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 获取父视图CKLineView
CKLineView* CKLineVolume::GetParentKLineView() const
{
	// 获取父窗口
	CWnd* pParentWnd = GetParent();
	while (pParentWnd != NULL)
	{
		// 检查父窗口是否是CKLineView
		if (pParentWnd->IsKindOf(RUNTIME_CLASS(CKLineView)))
		{
			return (CKLineView*)pParentWnd;
		}
		
		// 继续向上查找
		pParentWnd = pParentWnd->GetParent();
	}
	
	return NULL;  // 未找到CKLineView父视图
}



// CKLineVolume 消息处理程序

void CKLineVolume::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);

	// 窗口大小改变时重新计算各区域
	if (cx > 0 && cy > 0)
	{
		// 计算主区域和坐标区
		int nSplitLineX = cx - m_nRightAreaWidth;
		m_rectKLineArea = CRect(0, 0, nSplitLineX, cy);
		m_rectCoordArea = CRect(nSplitLineX, 0, cx, cy);
		
		// 强制重绘
		Invalidate();
	}
}

// 初始化按钮
void CKLineVolume::InitButtons()
{
	m_arrButtons.clear();
	
	// 添加技术指标按钮（作为切换按钮）
	ButtonInfo btnTech;
	btnTech.nID = 1;
	btnTech.strText = _T("技术指标");
	btnTech.clrText = RGB(255, 255, 255);
	btnTech.clrBk = RGB(0, 0, 0);
	btnTech.bToggle = true;
	btnTech.nGroupID = 1;  // 设置组ID为1
	btnTech.bSelected = true;  // 默认选中技术指标
	m_arrButtons.push_back(btnTech);
	
	// 添加信号指标按钮（作为切换按钮）
	ButtonInfo btnSignal;
	btnSignal.nID = 2;
	btnSignal.strText = _T("信号指标");
	btnSignal.clrText = RGB(255, 255, 255);
	btnSignal.clrBk = RGB(0, 0, 0);
	btnSignal.bToggle = true;
	btnSignal.nGroupID = 1;  // 设置组ID为1
	btnSignal.bSelected = false;  // 默认不选中
	m_arrButtons.push_back(btnSignal);

	// 添加历史分时按钮（作为切换按钮）
	ButtonInfo btnHistory;
	btnHistory.nID = 3;
	btnHistory.strText = _T("历史分时");
	btnHistory.clrText = RGB(255, 255, 255);
	btnHistory.clrBk = RGB(0, 0, 0);
	btnHistory.bToggle = true;
	btnHistory.nGroupID = 1;  // 设置组ID为1
	btnHistory.bSelected = false;  // 默认不选中
	m_arrButtons.push_back(btnHistory);
}

// 绘制信息条
void CKLineVolume::DrawInfoBar(CDC* pDC)
{
	// 信息条位于左侧区域的最上方
	m_rectInfoBar = CRect(
		m_rectKLineArea.left,
		m_rectKLineArea.top,
		m_rectKLineArea.right,
		m_rectKLineArea.top + m_nInfoBarHeight
	);
	
	// 绘制分隔线
	CPen penSplitLine(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细实线
	CPen* pOldPen = pDC->SelectObject(&penSplitLine);
	
	pDC->MoveTo(m_rectInfoBar.left, m_rectInfoBar.bottom);
	pDC->LineTo(m_rectInfoBar.right, m_rectInfoBar.bottom);
	
	// 绘制信息文本 - 修改字体与成交量信息条一致
	CFont font;
	font.CreateFont(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
		ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
		DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&font);
	int nOldBkMode = pDC->SetBkMode(TRANSPARENT);
	COLORREF oldTextColor = pDC->SetTextColor(RGB(255, 255, 255));
	
	// 如果十字光标处于活动状态且光标索引有效，显示对应K线数据
	if (m_bShowCrossCursor && m_nCursorIndex >= 0 && m_nCursorIndex < m_nKLineCount)
	{
		UpdateInfoBarWithCursorData(pDC);
	}
	else
	{
		// 显示默认信息
		CString strInfo = _T("日期: 2024-03-19  涨幅: +2.15%  成交量: 2.3亿手  成交额: 358.6亿");
		pDC->TextOut(m_rectInfoBar.left + 5, m_rectInfoBar.top + 5, strInfo);
	}
	
	// 恢复设置
	pDC->SelectObject(pOldFont);
	pDC->SetBkMode(nOldBkMode);
	pDC->SetTextColor(oldTextColor);
	pDC->SelectObject(pOldPen);
}

// 绘制K线主图
void CKLineVolume::DrawMainChart(CDC* pDC)
{
	// 计算K线主图区域，位于信息条下方
	// 计算实际可用高度：总高度减去各个固定高度区域
	int nEffectiveHeight = m_rectKLineArea.Height() - m_nInfoBarHeight - m_nVolumeInfoBarHeight;
	if (m_bShowButtonBar) {
		nEffectiveHeight -= m_nButtonBarHeight;
	}
	
	// 确保有效高度为正值
	nEffectiveHeight = max(10, nEffectiveHeight);
	
	// 主图占可用高度的3/4
	int nMainHeight = nEffectiveHeight * 3 / 4;
	
	// 确保主图高度至少有一定的值，便于查看
	nMainHeight = max(100, nMainHeight);
	
	m_rectMainChart = CRect(
		m_rectKLineArea.left,
		m_rectInfoBar.bottom,
		m_rectKLineArea.right,
		m_rectInfoBar.bottom + nMainHeight
	);
	
	// 安全检查区域宽度
	if (m_rectMainChart.Width() <= 0)
	{
		TRACE(_T("DrawMainChart: 警告 - 主图区域宽度为0\n"));
		return;
	}
	
	// 绘制深红色分割线
	CPen penSplitLine(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细实线
	CPen* pOldPen = pDC->SelectObject(&penSplitLine);
	
	pDC->MoveTo(m_rectMainChart.left, m_rectMainChart.bottom);
	pDC->LineTo(m_rectMainChart.right, m_rectMainChart.bottom);
	
	// 移除水平虚线绘制代码 - 避免与DrawPriceScale中的水平线重复
	
	// 如果有K线数据，绘制K线和均线
	if (m_nKLineCount > 0)
	{
		// 先绘制价格坐标和水平分割线（放在底层）
		DrawPriceScale(pDC);
		
		// 然后绘制K线和均线（放在上层）
		DrawKLine(pDC);
		DrawMA(pDC);
	}
	
	// 恢复之前的画笔
	pDC->SelectObject(pOldPen);
}

// 绘制成交量图表
void CKLineVolume::DrawVolumeChart(CDC* pDC)
{
	// 成交量图表位于K线主图和按钮区域之间
	// 计算实际可用高度：总高度减去各个固定高度区域
	int nEffectiveHeight = m_rectKLineArea.Height() - m_nInfoBarHeight - m_nVolumeInfoBarHeight;
	if (m_bShowButtonBar) {
		nEffectiveHeight -= m_nButtonBarHeight;
	}
	
	// 确保可用高度为正值
	nEffectiveHeight = max(10, nEffectiveHeight);
	
	// 成交量图表占可用高度的1/4
	int nVolumeHeight = nEffectiveHeight / 4;
	
	// 确保成交量图表高度至少有一定的值，便于查看
	nVolumeHeight = max(50, nVolumeHeight);
	
	// 计算成交量图表区域
	m_rectVolume = CRect(
		m_rectKLineArea.left,
		m_rectVolumeInfoBar.bottom,
		m_rectKLineArea.right,
		m_rectVolumeInfoBar.bottom + nVolumeHeight
	);
	
	// 安全检查区域宽度
	if (m_rectVolume.Width() <= 0)
	{
		TRACE(_T("DrawVolumeChart: 警告 - 成交量区域宽度为0\n"));
		return;
	}
	
	// 绘制深红色分割线
	CPen penSplitLine(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细实线
	CPen* pOldPen = pDC->SelectObject(&penSplitLine);
	
	pDC->MoveTo(m_rectVolume.left, m_rectVolume.bottom);
	pDC->LineTo(m_rectVolume.right, m_rectVolume.bottom);
	
	// 绘制成交量
	DrawVolume(pDC);
	
	// 恢复先前的画笔
	pDC->SelectObject(pOldPen);
}

// 绘制按钮区
void CKLineVolume::DrawButtonBar(CDC* pDC)
{
	// 计算按钮区域，位于成交量图下方
	m_rectButtonBar = CRect(
		m_rectKLineArea.left,
		m_rectVolume.bottom,
		m_rectKLineArea.right,
		m_rectKLineArea.bottom
	);
	
	// 绘制深红色分割线
	CPen penSplitLine(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细实线
	CPen* pOldPen = pDC->SelectObject(&penSplitLine);
	
	// 绘制按钮
	CFont font;
	// 增大字体大小，从14增加到18，并设置为粗体
	font.CreateFont(18, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
		ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
		DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&font);
	int nOldBkMode = pDC->SetBkMode(TRANSPARENT);
	
	// 更新按钮位置
	int nButtonX = m_rectButtonBar.left + 5;
	for (int i = 0; i < m_arrButtons.size(); i++)
	{
		ButtonInfo& btn = m_arrButtons[i];
		
		// 设置按钮区域，占满按钮区高度
		btn.rect = CRect(
			nButtonX,
			m_rectButtonBar.top + 2,
			nButtonX + m_nButtonWidth,
			m_rectButtonBar.bottom - 2
		);
		
		// 绘制按钮背景（选中状态）
		if (btn.bSelected)
		{
			// 选中状态使用深蓝色背景（使用FillSolidRect不显示边框）
			pDC->FillSolidRect(btn.rect, RGB(0, 0, 128));
		}
		
		// 绘制按钮文本
		if (btn.bHover)
		{
			// 鼠标悬停时使用高亮颜色
			pDC->SetTextColor(RGB(255, 255, 0));
		}
		else if (btn.bSelected)
		{
			// 选中状态使用白色文本
			pDC->SetTextColor(RGB(255, 255, 255));
		}
		else
		{
			pDC->SetTextColor(btn.clrText);
		}
		
		// 使用DrawText实现文本居中显示
		CRect textRect = btn.rect;
		pDC->DrawText(btn.strText, textRect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
		
		// 更新下一个按钮的起始位置
		nButtonX += m_nButtonWidth;
		
		// 绘制按钮后的分隔线（包括最后一个按钮）
		pDC->MoveTo(nButtonX, m_rectButtonBar.top + 2);
		pDC->LineTo(nButtonX, m_rectButtonBar.bottom - 2);
		nButtonX += 2;
	}
	
	// 恢复设置
	pDC->SelectObject(pOldFont);
	pDC->SetBkMode(nOldBkMode);
	pDC->SelectObject(pOldPen);
}

// 按钮点击测试
int CKLineVolume::HitTestButton(CPoint point)
{
	for (int i = 0; i < m_arrButtons.size(); i++)
	{
		if (m_arrButtons[i].rect.PtInRect(point))
		{
			return i;
		}
	}
	
	return -1; // 没有按钮被点击
}

void CKLineVolume::OnLButtonDown(UINT nFlags, CPoint point)
{
	// 设置鼠标左键按下状态
	m_bMouseDown = true;

	// 检查是否点击了按钮
	int nButtonIndex = HitTestButton(point);
	if (nButtonIndex >= 0)
	{
		// 处理按钮点击
		ButtonInfo& btn = m_arrButtons[nButtonIndex];
		
		// 如果是切换按钮，则改变选中状态
		if (btn.bToggle)
		{
			// 如果按钮属于一个组，需要先取消同组其他按钮的选中状态
			if (btn.nGroupID > 0)
			{
				for (int i = 0; i < m_arrButtons.size(); i++)
				{
					if (i != nButtonIndex && m_arrButtons[i].nGroupID == btn.nGroupID)
					{
						m_arrButtons[i].bSelected = false;
					}
				}
			}
			
			// 切换当前按钮的选中状态
			btn.bSelected = !btn.bSelected;
		}
		
		// 获取父视图CKLineView
		CKLineView* pKLineView = GetParentKLineView();
		
		// 根据按钮ID执行不同操作
		switch (btn.nID)
		{
		case 1: // 技术指标
			if (pKLineView != NULL && pKLineView->m_nIndicatorViewID > 0)
			{
				// 获取父视图的客户区大小
				CRect rcClient;
				pKLineView->GetClientRect(&rcClient);
				
				// 获取左侧区域的矩形
				CRect rcLeft;
				pKLineView->m_wndSplitter1.GetPane(0, 0)->GetWindowRect(&rcLeft);
				pKLineView->ScreenToClient(&rcLeft);
				
				// 恢复默认的7:3分割比例
				int topHeight = rcLeft.Height() * 7 / 10;
				int bottomHeight = rcLeft.Height() - topHeight;
				pKLineView->m_wndSplitter2.SetRowInfo(0, topHeight, 100);
				pKLineView->m_wndSplitter2.SetRowInfo(1, bottomHeight, 100);
				
				// 切换到技术指标视图
				pKLineView->m_wndSplitter2.SwitchView(pKLineView->m_nIndicatorViewID);
				
				// 重新计算布局
				pKLineView->m_wndSplitter2.RecalcLayout();
			}
			break;
		case 2: // 信号指标
			if (pKLineView != NULL && pKLineView->m_nKLineSignalID > 0)
			{
				// 获取父视图的客户区大小
				CRect rcClient;
				pKLineView->GetClientRect(&rcClient);
				
				// 获取左侧区域的矩形
				CRect rcLeft;
				pKLineView->m_wndSplitter1.GetPane(0, 0)->GetWindowRect(&rcLeft);
				pKLineView->ScreenToClient(&rcLeft);
				
				// 恢复默认的7:3分割比例
				int topHeight = rcLeft.Height() * 7 / 10;
				int bottomHeight = rcLeft.Height() - topHeight;
				pKLineView->m_wndSplitter2.SetRowInfo(0, topHeight, 100);
				pKLineView->m_wndSplitter2.SetRowInfo(1, bottomHeight, 100);
				
				// 切换到信号指标视图
				pKLineView->m_wndSplitter2.SwitchView(pKLineView->m_nKLineSignalID);
				
				// 重新计算布局
				pKLineView->m_wndSplitter2.RecalcLayout();
			}
			break;
		case 3: // 历史分时
			if (pKLineView != NULL && pKLineView->m_nKLineTimeID > 0)
			{
				// 获取父视图的客户区大小
				CRect rcClient;
				pKLineView->GetClientRect(&rcClient);
				
				// 获取左侧区域的矩形
				CRect rcLeft;
				pKLineView->m_wndSplitter1.GetPane(0, 0)->GetWindowRect(&rcLeft);
				pKLineView->ScreenToClient(&rcLeft);
				
				// 调整上下分割比例为1:1（各占50%）
				int height = rcLeft.Height() / 2;
				pKLineView->m_wndSplitter2.SetRowInfo(0, height, 100);
				pKLineView->m_wndSplitter2.SetRowInfo(1, height, 100);
				
				// 切换到历史分时视图
				pKLineView->m_wndSplitter2.SwitchView(pKLineView->m_nKLineTimeID);
				
				// 重新计算布局
				pKLineView->m_wndSplitter2.RecalcLayout();
			}
			break;
		case ID_BTN_SHOW_BUTTONBAR: // 显示/隐藏按钮区
			// 更新显示状态变量
			m_bShowButtonBar = btn.bSelected;
			// 重新计算各个区域大小并重绘整个视图
			Invalidate();
			break;
		}
		
		// 重绘按钮区域
		InvalidateRect(m_rectButtonBar, FALSE);
	}
	
	// 在这里处理鼠标按下后的十字光标显示
	// 需要判断鼠标是否在K线主图或成交量图区域内
	bool bCursorInMainChart = m_rectMainChart.PtInRect(point);
	bool bCursorInVolumeChart = m_rectVolume.PtInRect(point);
	
	if (bCursorInMainChart || bCursorInVolumeChart)
	{
		// 如果十字光标已经显示，则点击时隐藏它
		if (m_bShowCrossCursor)
		{
			m_bShowCrossCursor = false;
		}
		else
		{
			// 否则显示十字光标
			m_bShowCrossCursor = true;
			m_ptCrossCursor = point;
			m_nCursorIndex = GetKLineIndexFromPoint(point);
			
			// 确保光标索引合法
			if (m_nCursorIndex < 0 || m_nCursorIndex >= m_nKLineCount)
			{
				m_bShowCrossCursor = false;
			}
		}
		
		// 无论是显示还是隐藏十字光标，都需要重绘视图
		Invalidate(FALSE);
	}
	
	CView::OnLButtonDown(nFlags, point);
}

void CKLineVolume::OnMouseMove(UINT nFlags, CPoint point)
{
	// 检查鼠标悬停在哪个按钮上，更新按钮的悬停状态
	bool bNeedRedraw = false;
	
	for (int i = 0; i < m_arrButtons.size(); i++)
	{
		bool bOldHover = m_arrButtons[i].bHover;
		m_arrButtons[i].bHover = m_arrButtons[i].rect.PtInRect(point);
		
		if (bOldHover != m_arrButtons[i].bHover)
			bNeedRedraw = true;
	}
	
	// 如果按钮悬停状态有变化，重绘按钮区域
	if (bNeedRedraw && !m_rectButtonBar.IsRectEmpty())
	{
		InvalidateRect(m_rectButtonBar, FALSE);
	}
	
	// 判断鼠标是否在K线主图区域内
	bool bCursorInMainChart = m_rectMainChart.PtInRect(point);
	bool bCursorInVolumeChart = m_rectVolume.PtInRect(point);
	
	// 更新十字光标状态和位置
	bool bOldShowCursor = m_bShowCrossCursor;
	CPoint ptOldCursor = m_ptCrossCursor;
	int nOldCursorIndex = m_nCursorIndex;
	
	// 判断鼠标是否在有效区域内
	bool bCursorInValidArea = bCursorInMainChart || bCursorInVolumeChart;
	
	// 不要根据鼠标状态改变十字光标的显示状态，仅当鼠标在有效区域内时更新位置
	// 十字光标的显示/隐藏只在鼠标点击时切换
	if (m_bShowCrossCursor && bCursorInValidArea)
	{
		// 更新十字光标位置
		m_ptCrossCursor = point;
		
		// 计算当前鼠标位置对应的K线索引
		m_nCursorIndex = GetKLineIndexFromPoint(point);
		
		// 确保光标索引合法
		if (m_nCursorIndex < 0 || m_nCursorIndex >= m_nKLineCount)
		{
			// 索引无效，但仍保持十字光标显示状态
			// 只是不显示当前位置的数据
		}
	}
	
	// 判断是否需要重绘
	if (bOldShowCursor != m_bShowCrossCursor || 
		(m_bShowCrossCursor && nOldCursorIndex != m_nCursorIndex))
	{
		// 需要重绘整个视图（现在使用了双缓冲，所以不会闪烁）
		Invalidate(FALSE); // FALSE表示不擦除背景，减少闪烁
	}
	else if (m_bShowCrossCursor && ptOldCursor != m_ptCrossCursor)
	{
		// 只有光标位置变了，可以只重绘涉及的区域
		// 清除旧光标位置
		CRect rectOldH(m_rectKLineArea.left, ptOldCursor.y - 1, m_rectKLineArea.right, ptOldCursor.y + 1);
		CRect rectOldV(ptOldCursor.x - 1, m_rectMainChart.top, ptOldCursor.x + 1, m_rectVolume.bottom);
		
		// 绘制新光标位置
		CRect rectNewH(m_rectKLineArea.left, m_ptCrossCursor.y - 1, m_rectKLineArea.right, m_ptCrossCursor.y + 1);
		CRect rectNewV(m_ptCrossCursor.x - 1, m_rectMainChart.top, m_ptCrossCursor.x + 1, m_rectVolume.bottom);
		
		// 合并矩形确保所有需要重绘的区域都被覆盖
		CRect rectRedraw;
		rectRedraw.UnionRect(rectOldH, rectOldV);
		CRect rectRedraw2;
		rectRedraw2.UnionRect(rectNewH, rectNewV);
		rectRedraw.UnionRect(rectRedraw, rectRedraw2);
		
		// 增加右侧坐标区域的重绘，确保价格显示更新
		// 创建右侧坐标区域的重绘区域（水平方向上的部分）
		CRect rectOldPrice(m_rectCoordArea.left, ptOldCursor.y - 10, m_rectCoordArea.right, ptOldCursor.y + 10);
		CRect rectNewPrice(m_rectCoordArea.left, m_ptCrossCursor.y - 10, m_rectCoordArea.right, m_ptCrossCursor.y + 10);
		
		// 将右侧价格区域合并到重绘区域
		rectRedraw.UnionRect(rectRedraw, rectOldPrice);
		rectRedraw.UnionRect(rectRedraw, rectNewPrice);
		
		// 同时需要重绘信息条
		InvalidateRect(m_rectInfoBar, FALSE);
		// 添加重绘成交量信息条
		InvalidateRect(m_rectVolumeInfoBar, FALSE);
		InvalidateRect(rectRedraw, FALSE);
	}
	else if (bNeedRedraw)
	{
		// 仅需要重绘按钮区
		InvalidateRect(m_rectButtonBar, FALSE);
	}
	
	CView::OnMouseMove(nFlags, point);
}

void CKLineVolume::OnLButtonUp(UINT nFlags, CPoint point)
{
	// 重置鼠标左键状态
	m_bMouseDown = false;
	
	// 不在鼠标左键弹起时隐藏十字光标，而是保持其状态
	// 十字光标将在下次点击左键时才会被隐藏或更新位置
	
	CView::OnLButtonUp(nFlags, point);
}

// 计算最大最小值
void CKLineVolume::CalcMinMaxValue()
{
	if (m_nKLineCount <= 0)
		return;
	
	// 初始值设为第一根K线的数据
	m_fMaxPrice = m_vecKLine[0]._High;
	m_fMinPrice = m_vecKLine[0]._Low;
	m_fMaxVolume = m_vecKLine[0]._Volume;
	
	// 遍历所有K线数据
	for (int i = 0; i < m_nKLineCount; i++)
	{
		// 更新最高价
		if (m_vecKLine[i]._High > m_fMaxPrice)
			m_fMaxPrice = m_vecKLine[i]._High;
		
		// 更新最低价
		if (m_vecKLine[i]._Low < m_fMinPrice)
			m_fMinPrice = m_vecKLine[i]._Low;
		
		// 更新最大成交量
		if (m_vecKLine[i]._Volume > m_fMaxVolume)
			m_fMaxVolume = m_vecKLine[i]._Volume;
	}
}


// 绘制K线
void CKLineVolume::DrawKLine(CDC* pDC)
{
	// 安全检查，如果K线数量为0，直接返回
	if (m_nKLineCount <= 0)
		return;
		
	// 计算可显示的K线数量
	int nDisplayAreaWidth = m_rectMainChart.Width();
	
	// 检查区域是否有效
	if (nDisplayAreaWidth <= 0)
	{
		// 区域无效时，使用一个合理的默认值
		TRACE(_T("DrawKLine: 警告 - 显示区域宽度为0，使用默认宽度\n"));
		nDisplayAreaWidth = 800; // 使用一个合理的默认宽度值
	}
	
	// 计算可显示的K线数量
	m_nDisplayCount = (nDisplayAreaWidth - 10) / (m_nKLineWidth + m_nKLineGap);
	
	// 确保至少显示一根K线
	m_nDisplayCount = max(1, m_nDisplayCount);
	
	// 如果可显示数量大于K线总数，调整为K线总数
	if (m_nDisplayCount > m_nKLineCount)
		m_nDisplayCount = m_nKLineCount;
	
	// 确保偏移量不会导致显示超出范围
	if (m_nDisplayOffset + m_nDisplayCount > m_nKLineCount)
		m_nDisplayOffset = m_nKLineCount - m_nDisplayCount;
	
	if (m_nDisplayOffset < 0)
		m_nDisplayOffset = 0;
	
	// 查找当前显示范围内的最高价和最低价
	double fDisplayMaxPrice = -DBL_MAX;
	double fDisplayMinPrice = DBL_MAX;
	int nMaxPriceIndex = -1;
	int nMinPriceIndex = -1;
	int nMaxPriceX = 0, nMaxPriceY = 0;
	int nMinPriceX = 0, nMinPriceY = 0;
	
	// 先循环一次找出当前可见区域的最大值和最小值
	for (int i = 0; i < m_nDisplayCount; i++)
	{
		// 获取当前K线数据
		KLINE_DATA& data = m_vecKLine[m_nDisplayOffset + i];
		
		// 更新最高价
		if (data._High > fDisplayMaxPrice)
		{
			fDisplayMaxPrice = data._High;
			nMaxPriceIndex = i;
		}
		
		// 更新最低价
		if (data._Low < fDisplayMinPrice)
		{
			fDisplayMinPrice = data._Low;
			nMinPriceIndex = i;
		}
	}
	
	// 增加上下边距（顶部和底部各预留5%的空间）
	double fPriceRange = fDisplayMaxPrice - fDisplayMinPrice;
	double fPadding = fPriceRange * 0.05;
	fDisplayMaxPrice += fPadding;
	fDisplayMinPrice -= fPadding;
	
	// 计算价格到Y坐标的转换比例 - 使用当前可见范围的价格
	fPriceRange = fDisplayMaxPrice - fDisplayMinPrice;
	double fYScale = 0.0;
	
	if (fPriceRange > 0.0001) // 避免除以0
		fYScale = (double)m_rectMainChart.Height() / fPriceRange;
	
	// 创建画笔
	CPen penRise(PS_SOLID, 1, RGB(255, 0, 0));     // 上涨为红色
	CPen penFall(PS_SOLID, 1, RGB(116, 245, 240/*0, 191, 255*/));   // 下跌为天蓝色
	CPen penLimit(PS_SOLID, 1, RGB(255, 255, 0));  // 涨停为黄色
	// 一字板粗画笔，宽度为2
	CPen penOneWordUp(PS_SOLID, 2, RGB(255, 0, 0));     // 一字板上涨为红色粗线
	CPen penOneWordDown(PS_SOLID, 2, RGB(116, 245, 240)); // 一字板下跌为天蓝色粗线
	CPen* pOldPen = NULL;
	
	// 创建画刷
	CBrush brushFall(RGB(116, 245, 240/*0, 191, 255*/));            // 下跌为天蓝色实心画刷
	CBrush brushLimit(RGB(255, 255, 0));           // 涨停为黄色实心画刷
	CBrush nullBrush;
	nullBrush.CreateStockObject(NULL_BRUSH);       // 上涨为空心画刷
	CBrush blackBrush(RGB(0, 0, 0));              // 创建黑色实心画刷，用于空心K线的背景
	CBrush* pOldBrush = NULL;
	
	// 绘制每一根K线
	for (int i = 0; i < m_nDisplayCount; i++)
	{
		// 获取当前K线数据
		KLINE_DATA& data = m_vecKLine[m_nDisplayOffset + i];
		
		// 计算K线的X坐标
		int x = m_rectMainChart.left + 5 + i * (m_nKLineWidth + m_nKLineGap) + m_nKLineWidth / 2;
		
		// 计算开盘价、收盘价、最高价和最低价对应的Y坐标 - 使用当前可见范围的价格
		int yOpen  = m_rectMainChart.bottom - (int)((data._Open - fDisplayMinPrice) * fYScale);
		int yClose = m_rectMainChart.bottom - (int)((data._Close - fDisplayMinPrice) * fYScale);
		int yHigh  = m_rectMainChart.bottom - (int)((data._High - fDisplayMinPrice) * fYScale);
		int yLow   = m_rectMainChart.bottom - (int)((data._Low - fDisplayMinPrice) * fYScale);
		
		// 如果是最高价所在K线，记录位置
		if (i == nMaxPriceIndex)
		{
			nMaxPriceX = x;
			nMaxPriceY = yHigh;
		}
		
		// 如果是最低价所在K线，记录位置
		if (i == nMinPriceIndex)
		{
			nMinPriceX = x;
			nMinPriceY = yLow;
		}
		
		// 计算涨幅（相对于前收价）
		float fChangePercent = 0.0f;
		if (data._PreClose > 0.0001f) // 避免除以0
			fChangePercent = (data._Close - data._PreClose) / data._PreClose * 100.0f;
		                  
		bool bIsLimitUp = IsLimitUp(m_strCode, data._PreClose, data._Close);
		bool bIsLimitDown = IsLimitDown(m_strCode, data._PreClose, data._Close); // 添加跌停检测
		
		// 检测是否为一字板（开盘价、收盘价、最高价和最低价相同）
		bool bIsOneWord = false;
		// 检测开盘价和收盘价是否相同
		bool bIsOpenEqualClose = false;
		// 使用浮点数比较的容差值
		const double EPSILON = 0.0001;
		
		// 判断是否一字板
		if (fabs(data._Open - data._Close) < EPSILON && 
			fabs(data._Open - data._High) < EPSILON && 
			fabs(data._Open - data._Low) < EPSILON)
		{
			bIsOneWord = true;
		}
		// 判断开盘价和收盘价是否相等
		else if (fabs(data._Open - data._Close) < EPSILON)
		{
			bIsOpenEqualClose = true;
		}
		
		// 选择画笔和画刷
		if (bIsLimitUp)
		{
			// 涨停K线 - 黄色实心
			pOldPen = pDC->SelectObject(&penLimit);
			pOldBrush = pDC->SelectObject(&brushLimit);
		}
		else if (bIsLimitDown && bIsOneWord) // 一字跌停的情况
		{
			// 一字跌停K线 - 天蓝色粗线
			pOldPen = pDC->SelectObject(&penOneWordDown);
			pOldBrush = pDC->SelectObject(&brushFall);
		}
		else if (data._Close >= data._Open) // 修改：使用开盘价与收盘价比较，而非前收价比较
		{
			// 上涨K线 - 红色空心
			if (bIsOneWord)
				pOldPen = pDC->SelectObject(&penOneWordUp); // 一字板上涨用红色粗线
			else
				pOldPen = pDC->SelectObject(&penRise);
			pOldBrush = pDC->SelectObject(&blackBrush); // 使用黑色实心画刷代替透明画刷
		}
		else
		{
			// 下跌K线 - 天蓝色实心
			if (bIsOneWord)
				pOldPen = pDC->SelectObject(&penOneWordDown); // 一字板下跌用天蓝色粗线
			else
				pOldPen = pDC->SelectObject(&penFall);
			pOldBrush = pDC->SelectObject(&brushFall);
		}
		
		// 绘制K线
		if (bIsOneWord)
		{
			// 一字板只绘制一条水平线
			int y = m_rectMainChart.bottom - (int)((data._Close - fDisplayMinPrice) * fYScale);
			int lineLength = m_nKLineWidth + 2; // 线条比普通K线稍长一些
			pDC->MoveTo(x - lineLength / 2, y);
			pDC->LineTo(x + lineLength / 2, y);
		}
		else if (bIsOpenEqualClose) // 开盘价等于收盘价的情况（十字星）
		{
			// 绘制开盘收盘处的水平线
			int y = m_rectMainChart.bottom - (int)((data._Close - fDisplayMinPrice) * fYScale);
			int lineLength = m_nKLineWidth; // 使用与K线宽度相同的长度
			pDC->MoveTo(x - lineLength / 2, y);
			pDC->LineTo(x + lineLength / 2, y);
			
			// 绘制上影线
			if (yHigh < y)
			{
				pDC->MoveTo(x, yHigh);
				pDC->LineTo(x, y);
			}
			
			// 绘制下影线
			if (yLow > y)
			{
				pDC->MoveTo(x, y);
				pDC->LineTo(x, yLow);
			}
		}
		else if (bIsLimitUp && !bIsOneWord) // 非一字涨停的特殊处理
		{
			// 绘制K线的矩形主体（开盘价和收盘价之间）
			CRect rectBody(x - m_nKLineWidth / 2, yOpen, x + m_nKLineWidth / 2, yClose);
			
			// 使用FillSolidRect和FrameRect替代Rectangle，避免产生多余像素
			// 先填充矩形区域
			pDC->FillSolidRect(rectBody, RGB(255, 255, 0)); // 黄色填充
			
			// 再绘制矩形边框
			pDC->FrameRect(rectBody, &brushLimit);
			
			// 顶部影线
			if (yHigh < yClose)
			{
				pDC->MoveTo(x, yHigh);
				pDC->LineTo(x, yClose);
				
				// 涨停K线在最高点总是绘制短横线
				int lineHalfLength = m_nKLineWidth / 6; // 短横线长度为K线宽度的1/3
				pDC->MoveTo(x - lineHalfLength, yHigh);
				pDC->LineTo(x + lineHalfLength, yHigh);
			}
			else if (yHigh == yClose) // 如果最高价等于收盘价，也绘制横线
			{
				// 涨停K线在最高点总是绘制短横线
				int lineHalfLength = m_nKLineWidth / 6; // 短横线长度为K线宽度的1/3
				pDC->MoveTo(x - lineHalfLength, yHigh);
				pDC->LineTo(x + lineHalfLength, yHigh);
			}
			
			// 底部影线
			if (yLow > yOpen)
			{
				// 使用精确的像素坐标，避免产生多余像素
				pDC->MoveTo(x, rectBody.bottom);
				pDC->LineTo(x, yLow);
			}
		}
		else if (bIsLimitDown && !bIsOneWord) // 非一字跌停的特殊处理
		{
			// 绘制K线的矩形主体（开盘价和收盘价之间）
			CRect rectBody(x - m_nKLineWidth / 2, yOpen, x + m_nKLineWidth / 2, yClose);
			
			// 使用FillSolidRect和FrameRect替代Rectangle，避免产生多余像素
			// 先填充矩形区域
			pDC->FillSolidRect(rectBody, RGB(116, 245, 240)); // 天蓝色填充
			
			// 再绘制矩形边框
			pDC->FrameRect(rectBody, &brushFall);
			
			// 确保使用天蓝色画笔绘制影线
			pDC->SelectObject(&penFall);
			
			// 顶部影线
			if (yHigh < yOpen)
			{
				pDC->MoveTo(x, yHigh);
				pDC->LineTo(x, yOpen);
			}

			// 底部影线
			if (yLow > yClose)
			{
				// 使用精确的像素坐标，避免产生多余像素
				pDC->MoveTo(x, rectBody.bottom);
				pDC->LineTo(x, yLow);
			}
			else if (yLow == yClose) // 如果最低价等于收盘价，也绘制横线
			{
				// 跌停K线在最低点总是绘制短横线
				int lineHalfLength = m_nKLineWidth / 6; // 短横线长度为K线宽度的1/3
				pDC->MoveTo(x - lineHalfLength, yLow);
				pDC->LineTo(x + lineHalfLength, yLow);
			}
		}
		else
		{
			// 绘制K线的矩形主体（开盘价和收盘价之间）
			CRect rectBody(x - m_nKLineWidth / 2, yOpen, x + m_nKLineWidth / 2, yClose);
			
			// 根据K线类型绘制不同样式
			if (data._Close >= data._Open) // 上涨K线
			{
				// 上涨K线 - 红色边框，黑色填充
				pDC->Rectangle(rectBody);
				
				// 绘制顶部影线
				if (yHigh < yClose)
				{
					pDC->MoveTo(x, yHigh);
					pDC->LineTo(x, yClose);
					
				}
				
				// 底部影线
				if (yLow > yOpen)
				{
					pDC->MoveTo(x, yOpen);
					pDC->LineTo(x, yLow);
					
				}
			}
			else // 下跌K线
			{
				// 下跌K线 - 天蓝色实心
				pDC->Rectangle(rectBody);
				
				// 顶部影线
				pDC->MoveTo(x, yHigh);
				pDC->LineTo(x, yOpen);

				// 底部影线
				pDC->MoveTo(x, yClose);
				pDC->LineTo(x, yLow);
			}
		}
	}
	
	// 绘制最高价和最低价标记
	if (nMaxPriceIndex >= 0 && nMinPriceIndex >= 0)
	{
		// 创建字体
		CFont font;
		// 增大字体大小，使其更加明显
		font.CreateFont(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
			ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
			DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
		
		CFont* pOldFont = pDC->SelectObject(&font);
		int nOldBkMode = pDC->SetBkMode(TRANSPARENT);
		
		// 绘制最高价标记 - 红色圆圈和文本
		CPen penMaxPrice(PS_SOLID, 1, RGB(255, 0, 0));  // 红色实线
		pDC->SelectObject(&penMaxPrice);
		pDC->Ellipse(nMaxPriceX - 3, nMaxPriceY - 3, nMaxPriceX + 3, nMaxPriceY + 3);  // 绘制红色圆圈
		
		// 格式化最高价文本
		CString strMaxPrice;
		strMaxPrice.Format(_T("%.2f"), fDisplayMaxPrice - fPadding);  // 去掉添加的边距
		
		// 计算文本位置
		CSize textSize;
		GetTextExtentPoint32(pDC->GetSafeHdc(), strMaxPrice, strMaxPrice.GetLength(), &textSize);
		
		// 绘制最高价文本，放在K线上方
		CRect rectMax(nMaxPriceX - textSize.cx / 2 - 2, nMaxPriceY - textSize.cy - 4,
			nMaxPriceX + textSize.cx / 2 + 2, nMaxPriceY - 4);
		
		// 直接设置文本颜色为红色，不使用背景色
		pDC->SetTextColor(RGB(255, 0, 0));  // 红色文本
		pDC->DrawText(strMaxPrice, rectMax, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
		
		// 绘制最低价标记 - 蓝色圆圈和文本
		CPen penMinPrice(PS_SOLID, 1, RGB(0, 191, 255));  // 蓝色实线
		pDC->SelectObject(&penMinPrice);
		pDC->Ellipse(nMinPriceX - 3, nMinPriceY - 3, nMinPriceX + 3, nMinPriceY + 3);  // 绘制蓝色圆圈
		
		// 格式化最低价文本
		CString strMinPrice;
		strMinPrice.Format(_T("%.2f"), fDisplayMinPrice + fPadding);  // 去掉添加的边距
		
		// 计算文本位置
		GetTextExtentPoint32(pDC->GetSafeHdc(), strMinPrice, strMinPrice.GetLength(), &textSize);
		
		// 绘制最低价文本，放在K线下方
		CRect rectMin(nMinPriceX - textSize.cx / 2 - 2, nMinPriceY + 4,
			nMinPriceX + textSize.cx / 2 + 2, nMinPriceY + textSize.cy + 4);
		
		// 直接设置文本颜色为蓝色，不使用背景色
		pDC->SetTextColor(RGB(0, 191, 255));  // 蓝色文本
		pDC->DrawText(strMinPrice, rectMin, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
		
		// 恢复设置
		pDC->SelectObject(pOldFont);
		pDC->SetBkMode(nOldBkMode);
	}
	
	// 保存当前可见范围的最高价和最低价，供其他函数使用
	m_fDisplayMaxPrice = fDisplayMaxPrice;
	m_fDisplayMinPrice = fDisplayMinPrice;
	
	// 恢复之前的画笔和画刷
	if (pOldPen)
		pDC->SelectObject(pOldPen);
	
	if (pOldBrush)
		pDC->SelectObject(pOldBrush);
}

// 绘制成交量
void CKLineVolume::DrawVolume(CDC* pDC)
{
	// 如果没有成交量数据，直接返回
	if (m_fMaxVolume <= 0.0)
		return;
		
	// 安全检查：确保数据索引有效
	if (m_nDisplayCount <= 0 || m_vecKLine.empty() || 
		m_nDisplayOffset < 0 || 
		m_nDisplayOffset + m_nDisplayCount > (int)m_vecKLine.size())
	{
		// 数据无效或索引越界，不进行绘制
		TRACE(_T("DrawVolume: 无效的数据索引 - 偏移量=%d, 显示数量=%d, 总数量=%d\n"), 
			m_nDisplayOffset, m_nDisplayCount, (int)m_vecKLine.size());
		return;
	}
	
	// 计算成交量到Y坐标的转换比例
	double fVolumeScale = (double)m_rectVolume.Height() / m_fMaxVolume;
	
	// 创建画笔
	CPen penRise(PS_SOLID, 1, RGB(255, 0, 0));     // 上涨为红色
	CPen penFall(PS_SOLID, 1, RGB(116, 245, 240/*0, 191, 255*/));   // 下跌为天蓝色
	CPen penLimit(PS_SOLID, 1, RGB(255, 255, 0));  // 涨停为黄色
	CPen* pOldPen = NULL;
	
	// 创建画刷
	CBrush brushFall(RGB(116, 245, 240/*0, 191, 255*/));            // 下跌为天蓝色实心画刷
	CBrush brushLimit(RGB(255, 255, 0));           // 涨停为黄色实心画刷
	CBrush nullBrush;
	nullBrush.CreateStockObject(NULL_BRUSH);       // 上涨为空心画刷
	CBrush* pOldBrush = NULL;
	
	// 绘制每一个成交量柱状图
	for (int i = 0; i < m_nDisplayCount; i++)
	{
		// 获取当前K线数据
		KLINE_DATA& data = m_vecKLine[m_nDisplayOffset + i];
		
		// 计算成交量柱状图的X坐标
		int x = m_rectMainChart.left + 5 + i * (m_nKLineWidth + m_nKLineGap) + m_nKLineWidth / 2;
		
		// 计算成交量高度
		int volumeHeight = (int)(data._Volume * fVolumeScale);
		
		// 使用Common.cpp中的函数判断涨停/跌停状态
		bool bIsLimitUp = IsLimitUp(m_strCode, data._PreClose, data._Close);
		bool bIsLimitDown = IsLimitDown(m_strCode, data._PreClose, data._Close);
		
		// 选择画笔和画刷
		if (bIsLimitUp)
		{
			// 涨停 - 黄色实心
			pOldPen = pDC->SelectObject(&penLimit);
			pOldBrush = pDC->SelectObject(&brushLimit);
		}
		else if (bIsLimitDown)
		{
			// 跌停 - 天蓝色实心，但使用特殊边框颜色标识
			pOldPen = pDC->SelectObject(&penFall);
			pOldBrush = pDC->SelectObject(&brushFall);
		}
		else if (data._Close >= data._Open) // 修改：使用开盘价与收盘价比较，而非前收价比较
		{
			// 上涨 - 红色空心
			pOldPen = pDC->SelectObject(&penRise);
			pOldBrush = pDC->SelectObject(&nullBrush);
		}
		else
		{
			// 下跌 - 天蓝色实心
			pOldPen = pDC->SelectObject(&penFall);
			pOldBrush = pDC->SelectObject(&brushFall);
		}
		
		// 绘制成交量柱状图
		CRect rectVolume(
			x - m_nKLineWidth / 2,
			m_rectVolume.bottom - volumeHeight,
			x + m_nKLineWidth / 2,
			m_rectVolume.bottom
		);
		
		pDC->Rectangle(rectVolume);
	}
	
	// 绘制成交量均线
	DrawVolumeMA(pDC);
	
	// 恢复之前的画笔和画刷
	if (pOldPen)
		pDC->SelectObject(pOldPen);
	
	if (pOldBrush)
		pDC->SelectObject(pOldBrush);
}

// 初始化均线
void CKLineVolume::InitMA()
{
	// 清空原有数据
	m_arrMA.clear();
	
	// 定义均线周期和颜色
	struct {
		int nPeriod;
		COLORREF color;
	} maInfo[] = {
		{5,   RGB(255, 255, 255)}, // 白色 MA5
		{10,  RGB(255, 255, 0)},   // 黄色 MA10
		{20,  RGB(255, 0, 255)},   // 紫色 MA20
		{30,  RGB(152, 251, 152)},  // 淡绿 MA30
		{60,  RGB(0, 255, 255)},   // 青色 MA60
		{120, RGB(255, 215, 0)},   // 金色 MA120
		{240, RGB(255, 192, 203)}, // 粉色 MA240
		{360, RGB(173, 216, 230)}  // 淡蓝 MA360
	};
	
	// 添加均线
	for (int i = 0; i < sizeof(maInfo) / sizeof(maInfo[0]); i++)
	{
		MAData ma;
		ma.nPeriod = maInfo[i].nPeriod;
		ma.clrLine = maInfo[i].color;
		ma.bShow = (i < 5); // 默认只显示前三种均线
		
		// 只有在K线数据存在时才设置数组大小
		// 否则数组大小为0，会在CalcMA函数中进行设置
		if (m_nKLineCount > 0)
		{
			ma.arrMA.resize(m_nKLineCount);
		}
		
		m_arrMA.push_back(ma);
	}
}

// 计算均线
void CKLineVolume::CalcMA()
{
	// 如果没有K线数据，直接返回
	if (m_nKLineCount <= 0)
		return;
	
	// 计算每种均线
	for (int i = 0; i < m_arrMA.size(); i++)
	{
		MAData& ma = m_arrMA[i];
		int nPeriod = ma.nPeriod;
		
		// 如果数组大小不匹配，重新设置
		if (ma.arrMA.size() != m_nKLineCount)
		{
			ma.arrMA.clear();
			ma.arrMA.resize(m_nKLineCount);
		}
		
		// 对于每个时间点计算均线值
		for (int j = 0; j < m_nKLineCount; j++)
		{
			// 时间点j的均线值是前nPeriod个收盘价的平均值
			double fSum = 0.0;
			int nCount = 0;
			
			// 向前查找nPeriod个点，但不超过数组边界
			for (int k = 0; k < nPeriod && j - k >= 0; k++)
			{
				if (j - k >= 0 && j - k < m_nKLineCount)
				{
					fSum += m_vecKLine[j - k]._Close;
					nCount++;
				}
			}
			
			// 计算平均值
			double fMA = 0.0;
			if (nCount > 0)
				fMA = fSum / nCount;
			
			// 设置均线值
			if (j < ma.arrMA.size())
			{
				ma.arrMA[j] = fMA;
			}
		}
	}
}

// 绘制均线
void CKLineVolume::DrawMA(CDC* pDC)
{
	// 如果没有均线数据，直接返回
	if (m_arrMA.empty() || m_nKLineCount <= 0)
		return;
	
	// 计算价格到Y坐标的转换比例 - 使用当前可见范围的价格
	double fPriceRange = m_fDisplayMaxPrice - m_fDisplayMinPrice;
	double fYScale = 0.0;
	
	if (fPriceRange > 0.0001) // 避免除以0
		fYScale = (double)m_rectMainChart.Height() / fPriceRange;
	
	// 绘制每种均线
	for (int i = 0; i < m_arrMA.size(); i++)
	{
		MAData& ma = m_arrMA[i];
		
		// 如果不显示该均线，则跳过
		if (!ma.bShow)
			continue;
		
		// 确保均线数组有效
		if (ma.arrMA.empty())
			continue;
		
		// 创建均线画笔
		CPen pen(PS_SOLID, 1, ma.clrLine);
		CPen* pOldPen = pDC->SelectObject(&pen);
		
		// 绘制均线
		bool bFirstPoint = true;
		CPoint ptLast;
		
		// 只绘制可视范围内的均线
		for (int j = 0; j < m_nDisplayCount; j++)
		{
			int nIndex = m_nDisplayOffset + j;
			
			// 如果当前索引超出范围，跳过
			if (nIndex < 0 || nIndex >= m_nKLineCount || nIndex >= ma.arrMA.size())
				continue;
			
			// 计算均线点的X坐标
			int x = m_rectMainChart.left + 5 + j * (m_nKLineWidth + m_nKLineGap) + m_nKLineWidth / 2;
			
			// 计算均线点的Y坐标 - 使用当前可见范围的价格
			double fMA = ma.arrMA[nIndex];
			int y = m_rectMainChart.bottom - (int)((fMA - m_fDisplayMinPrice) * fYScale);
			
			// 限制Y坐标在图表区域内
			y = max(m_rectMainChart.top, min(y, m_rectMainChart.bottom));
			
			// 如果是第一个点，只记录位置，不绘制
			if (bFirstPoint)
			{
				ptLast = CPoint(x, y);
				bFirstPoint = false;
			}
			else
			{
				// 绘制线段
				pDC->MoveTo(ptLast);
				pDC->LineTo(x, y);
				ptLast = CPoint(x, y);
			}
		}
		
		// 恢复之前的画笔
		pDC->SelectObject(pOldPen);
	}
}

// 右键菜单
void CKLineVolume::OnContextMenu(CWnd* pWnd, CPoint point)
{
	// 检查点击位置是否在K线主图区域
	CPoint ptClient = point;
	ScreenToClient(&ptClient);
	
	if (!m_rectMainChart.PtInRect(ptClient))
		return;
	
	// 创建弹出菜单
	CMenu menu;
	menu.CreatePopupMenu();
	
	// 添加均线菜单项
	menu.AppendMenu(MF_STRING, ID_MA5, _T("MA5"));
	menu.AppendMenu(MF_STRING, ID_MA10, _T("MA10"));
	menu.AppendMenu(MF_STRING, ID_MA20, _T("MA20"));
	menu.AppendMenu(MF_STRING, ID_MA30, _T("MA30"));
	menu.AppendMenu(MF_STRING, ID_MA60, _T("MA60"));
	menu.AppendMenu(MF_STRING, ID_MA120, _T("MA120"));
	menu.AppendMenu(MF_STRING, ID_MA240, _T("MA240"));
	menu.AppendMenu(MF_STRING, ID_MA360, _T("MA360"));
	
	// 根据当前状态设置菜单项选中状态
	for (int i = 0; i < m_arrMA.size(); i++)
	{
		MAData& ma = m_arrMA[i];
		UINT nID = ID_MA_BASE + ma.nPeriod;
		
		// 设置选中状态
		if (ma.bShow)
			menu.CheckMenuItem(nID, MF_CHECKED);
	}
	
	// 显示弹出菜单
	menu.TrackPopupMenu(TPM_LEFTALIGN | TPM_RIGHTBUTTON, point.x, point.y, this);
}

// 均线菜单选择响应
void CKLineVolume::OnMAMenuSelect(UINT nID)
{
	// 计算周期
	int nPeriod = nID - ID_MA_BASE;
	
	// 查找并切换均线显示状态
	for (int i = 0; i < m_arrMA.size(); i++)
	{
		MAData& ma = m_arrMA[i];
		
		if (ma.nPeriod == nPeriod)
		{
			// 切换显示状态
			ma.bShow = !ma.bShow;
			
			// 重绘视图
			Invalidate();
			break;
		}
	}
}

// 更新均线菜单UI
void CKLineVolume::OnUpdateMAMenu(CCmdUI* pCmdUI)
{
	// 计算周期
	int nPeriod = pCmdUI->m_nID - ID_MA_BASE;
	
	// 查找均线并设置菜单项状态
	for (int i = 0; i < m_arrMA.size(); i++)
	{
		MAData& ma = m_arrMA[i];
		
		if (ma.nPeriod == nPeriod)
		{
			// 设置菜单项选中状态
			pCmdUI->SetCheck(ma.bShow);
			break;
		}
	}
}

// 获取鼠标位置对应的K线索引
int CKLineVolume::GetKLineIndexFromPoint(CPoint point)
{
	// 确保有足够的K线数据和显示区域
	if (m_nKLineCount <= 0 || m_nDisplayCount <= 0 || m_rectMainChart.Width() <= 0)
		return -1;
	
	// 计算鼠标X坐标对应的K线索引
	int nOffsetX = point.x - m_rectMainChart.left - 5;
	int nKLineFullWidth = m_nKLineWidth + m_nKLineGap;
	
	if (nOffsetX < 0)
		return -1;
	
	int nIndex = nOffsetX / nKLineFullWidth;
	
	// 检查计算出的索引是否有效
	if (nIndex >= 0 && nIndex < m_nDisplayCount)
	{
		int actualIndex = m_nDisplayOffset + nIndex;
		// 再次确认实际索引在K线数据范围内
		if (actualIndex >= 0 && actualIndex < m_nKLineCount)
			return actualIndex;
	}
	
	return -1;
}

// 绘制十字光标
void CKLineVolume::DrawCrossCursor(CDC* pDC)
{
	// 确保十字光标显示标志为true且鼠标位置在有效区域内
	if (!m_bShowCrossCursor || 
	    (!m_rectMainChart.PtInRect(m_ptCrossCursor) && !m_rectVolume.PtInRect(m_ptCrossCursor)))
		return;
	
	// 创建白色实线画笔
	CPen penCursor(PS_SOLID, 1, RGB(255, 255, 255));
	CPen* pOldPen = pDC->SelectObject(&penCursor);
	
	// 绘制水平线，但只到右侧垂直线为止
	pDC->MoveTo(m_rectKLineArea.left, m_ptCrossCursor.y);
	pDC->LineTo(m_rectKLineArea.right, m_ptCrossCursor.y);
	
	// 绘制垂直线，但仅在K线主图和成交量图区域内
	// 计算垂直线的顶部和底部
	int nTop = m_rectMainChart.top;
	int nBottom = m_rectVolume.bottom;
	
	pDC->MoveTo(m_ptCrossCursor.x, nTop);
	pDC->LineTo(m_ptCrossCursor.x, nBottom);
	
	// 在右侧坐标区显示当前价格
	if (m_nKLineCount > 0)
	{
		// 计算当前光标Y坐标对应的价格
		double fPrice = 0.0;
		
		if (m_rectMainChart.PtInRect(m_ptCrossCursor))
		{
			// 在主图区域内，计算对应的价格
			double fPriceRange = m_fDisplayMaxPrice - m_fDisplayMinPrice;
			if (fPriceRange > 0.0001) // 避免除以0
			{
				double fYRatio = (double)(m_rectMainChart.bottom - m_ptCrossCursor.y) / m_rectMainChart.Height();
				fPrice = m_fDisplayMinPrice + fYRatio * fPriceRange;
			}
		}
		else if (m_rectVolume.PtInRect(m_ptCrossCursor))
		{
			// 在成交量区域内，计算对应的成交量
			double fVolumeRatio = (double)(m_rectVolume.bottom - m_ptCrossCursor.y) / m_rectVolume.Height();
			fPrice = fVolumeRatio * m_fMaxVolume / 10000.0; // 转换为万手单位
		}
		
		// 创建字体
		CFont font;
		font.CreateFont(22, 0, 0, 0, FW_NORMAL, FALSE, FALSE, 0, 
			ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
			DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
		
		CFont* pOldFont = pDC->SelectObject(&font);
		int nOldBkMode = pDC->SetBkMode(TRANSPARENT);
		COLORREF oldTextColor = pDC->SetTextColor(RGB(255, 255, 0)); // 使用黄色显示价格
		
		// 格式化价格文本
		CString strPrice;
		if (m_rectMainChart.PtInRect(m_ptCrossCursor))
		{
			strPrice.Format(_T("%.2f"), fPrice);
		}
		else
		{
			strPrice.Format(_T("%.2f万"), fPrice);
		}
		
		// 计算文本大小
		CSize textSize;
		GetTextExtentPoint32(pDC->GetSafeHdc(), strPrice, strPrice.GetLength(), &textSize);
		
		// 绘制背景矩形
		CRect rectText(
			m_rectCoordArea.left + 5,
			m_ptCrossCursor.y - textSize.cy / 2,
			m_rectCoordArea.left + textSize.cx + 10,
			m_ptCrossCursor.y + textSize.cy / 2
		);
		
		pDC->FillSolidRect(rectText, RGB(0, 0, 128)); // 深蓝色背景
		
		// 绘制价格文本
		pDC->TextOut(m_rectCoordArea.left + 8, m_ptCrossCursor.y - textSize.cy / 2, strPrice);
		
		// 恢复设置
		pDC->SelectObject(pOldFont);
		pDC->SetBkMode(nOldBkMode);
		pDC->SetTextColor(oldTextColor);
	}
	
	// 恢复原来的画笔
	pDC->SelectObject(pOldPen);
}

// 在信息条显示光标位置对应的K线数据
void CKLineVolume::UpdateInfoBarWithCursorData(CDC* pDC)
{
	// 确保光标索引有效且十字光标显示
	if (!m_bShowCrossCursor || m_nCursorIndex < 0 || m_nCursorIndex >= m_nKLineCount)
		return;
	
	// 获取当前K线数据
	KLINE_DATA& data = m_vecKLine[m_nCursorIndex];
	
	// 格式化日期（从整数形式转换为YYYY-MM-DD格式）
	CString strDate;
	CTime t(data._Date);

	strDate = t.Format(_T("%Y-%m-%d"));
	
	// 计算涨跌幅（使用前收价）
	float fChange = 0.0f;
	if (data._PreClose > 0.0001f) // 避免除以0
	{
		fChange = (data._Close - data._PreClose) / data._PreClose * 100.0f;
	}
	
	// 确定颜色：上涨红色，下跌蓝色，持平白色
	COLORREF clrRising = RGB(255, 0, 0);    // 上涨红色
	COLORREF clrFalling = RGB(0, 191, 255); // 下跌天蓝色
	COLORREF clrFlat = RGB(255, 255, 255);  // 持平白色
	COLORREF clrValue;                      // 价格和涨幅显示颜色
	
	// 根据收盘价与前收盘价比较确定颜色
	if (data._Close > data._PreClose)
		clrValue = clrRising;
	else if (data._Close < data._PreClose)
		clrValue = clrFalling;
	else
		clrValue = clrFlat;
	
	// 设置标签文本颜色为白色
	COLORREF clrLabel = RGB(255, 255, 255);
	
	// 绘制日期标签和值(白色)
	pDC->SetTextColor(clrLabel);
	CString strDateLabel = _T("日期: ");
	pDC->TextOut(m_rectInfoBar.left + 5, m_rectInfoBar.top + 5, strDateLabel);
	
	// 计算日期值的位置
	CSize sizeLabel = pDC->GetTextExtent(strDateLabel);
	int nDateX = m_rectInfoBar.left + 5 + sizeLabel.cx;
	
	// 绘制日期值(白色，日期总是白色)
	pDC->TextOut(nDateX, m_rectInfoBar.top + 5, strDate);
	
	// 计算"开盘:"标签位置
	CSize sizeDateValue = pDC->GetTextExtent(strDate);
	int nOpenLabelX = nDateX + sizeDateValue.cx + 10;
	
	// 绘制"开盘:"标签(白色)
	CString strOpenLabel = _T("开盘: ");
	pDC->SetTextColor(clrLabel);
	pDC->TextOut(nOpenLabelX, m_rectInfoBar.top + 5, strOpenLabel);
	
	// 计算开盘价值的位置
	CSize sizeOpenLabel = pDC->GetTextExtent(strOpenLabel);
	int nOpenValueX = nOpenLabelX + sizeOpenLabel.cx;
	
	// 绘制开盘价值(根据涨跌设置颜色)
	CString strOpenValue;
	strOpenValue.Format(_T("%.2f"), data._Open);
	pDC->SetTextColor(clrValue);
	pDC->TextOut(nOpenValueX, m_rectInfoBar.top + 5, strOpenValue);
	
	// 计算"最高:"标签位置
	CSize sizeOpenValue = pDC->GetTextExtent(strOpenValue);
	int nHighLabelX = nOpenValueX + sizeOpenValue.cx + 10;
	
	// 绘制"最高:"标签(白色)
	CString strHighLabel = _T("最高: ");
	pDC->SetTextColor(clrLabel);
	pDC->TextOut(nHighLabelX, m_rectInfoBar.top + 5, strHighLabel);
	
	// 计算最高价值的位置
	CSize sizeHighLabel = pDC->GetTextExtent(strHighLabel);
	int nHighValueX = nHighLabelX + sizeHighLabel.cx;
	
	// 绘制最高价值(根据涨跌设置颜色)
	CString strHighValue;
	strHighValue.Format(_T("%.2f"), data._High);
	pDC->SetTextColor(clrValue);
	pDC->TextOut(nHighValueX, m_rectInfoBar.top + 5, strHighValue);
	
	// 计算"最低:"标签位置
	CSize sizeHighValue = pDC->GetTextExtent(strHighValue);
	int nLowLabelX = nHighValueX + sizeHighValue.cx + 10;
	
	// 绘制"最低:"标签(白色)
	CString strLowLabel = _T("最低: ");
	pDC->SetTextColor(clrLabel);
	pDC->TextOut(nLowLabelX, m_rectInfoBar.top + 5, strLowLabel);
	
	// 计算最低价值的位置
	CSize sizeLowLabel = pDC->GetTextExtent(strLowLabel);
	int nLowValueX = nLowLabelX + sizeLowLabel.cx;
	
	// 绘制最低价值(根据涨跌设置颜色)
	CString strLowValue;
	strLowValue.Format(_T("%.2f"), data._Low);
	pDC->SetTextColor(clrValue);
	pDC->TextOut(nLowValueX, m_rectInfoBar.top + 5, strLowValue);
	
	// 计算"收盘:"标签位置
	CSize sizeLowValue = pDC->GetTextExtent(strLowValue);
	int nCloseLabelX = nLowValueX + sizeLowValue.cx + 10;
	
	// 绘制"收盘:"标签(白色)
	CString strCloseLabel = _T("收盘: ");
	pDC->SetTextColor(clrLabel);
	pDC->TextOut(nCloseLabelX, m_rectInfoBar.top + 5, strCloseLabel);
	
	// 计算收盘价值的位置
	CSize sizeCloseLabel = pDC->GetTextExtent(strCloseLabel);
	int nCloseValueX = nCloseLabelX + sizeCloseLabel.cx;
	
	// 绘制收盘价值(根据涨跌设置颜色)
	CString strCloseValue;
	strCloseValue.Format(_T("%.2f"), data._Close);
	pDC->SetTextColor(clrValue);
	pDC->TextOut(nCloseValueX, m_rectInfoBar.top + 5, strCloseValue);
	
	// 计算"涨幅:"标签位置
	CSize sizeCloseValue = pDC->GetTextExtent(strCloseValue);
	int nChangeLabelX = nCloseValueX + sizeCloseValue.cx + 10;
	
	// 绘制"涨幅:"标签(白色)
	CString strChangeLabel = _T("涨幅: ");
	pDC->SetTextColor(clrLabel);
	pDC->TextOut(nChangeLabelX, m_rectInfoBar.top + 5, strChangeLabel);
	
	// 计算涨幅值的位置
	CSize sizeChangeLabel = pDC->GetTextExtent(strChangeLabel);
	int nChangeValueX = nChangeLabelX + sizeChangeLabel.cx;
	
	// 绘制涨幅值(根据涨跌设置颜色)
	CString strChangeValue;
	strChangeValue.Format(_T("%+.2f%%"), fChange);
	pDC->SetTextColor(clrValue);
	pDC->TextOut(nChangeValueX, m_rectInfoBar.top + 5, strChangeValue);
}



// 设置按钮选中状态（通过索引）
void CKLineVolume::SetButtonSelected(int nIndex, bool bSelected)
{
	if (nIndex >= 0 && nIndex < m_arrButtons.size())
	{
		ButtonInfo& btn = m_arrButtons[nIndex];
		
		// 如果状态没有变化，直接返回
		if (btn.bSelected == bSelected)
			return;
		
		// 如果是选中状态且按钮属于一个组，需要先取消同组其他按钮的选中状态
		if (bSelected && btn.nGroupID > 0)
		{
			for (int i = 0; i < m_arrButtons.size(); i++)
			{
				if (i != nIndex && m_arrButtons[i].nGroupID == btn.nGroupID)
				{
					m_arrButtons[i].bSelected = false;
				}
			}
		}
		
		// 设置按钮选中状态
		btn.bSelected = bSelected;
		
		// 重绘按钮区域
		if (!m_rectButtonBar.IsRectEmpty())
		{
			InvalidateRect(m_rectButtonBar, FALSE);
		}
	}
}

// 设置按钮选中状态（通过ID）
void CKLineVolume::SetButtonSelectedByID(int nID, bool bSelected)
{
	for (int i = 0; i < m_arrButtons.size(); i++)
	{
		if (m_arrButtons[i].nID == nID)
		{
			SetButtonSelected(i, bSelected);
			break;
		}
	}
}

// 获取按钮选中状态（通过索引）
bool CKLineVolume::IsButtonSelected(int nIndex) const
{
	if (nIndex >= 0 && nIndex < m_arrButtons.size())
	{
		return m_arrButtons[nIndex].bSelected;
	}
	return false;
}

// 获取按钮选中状态（通过ID）
bool CKLineVolume::IsButtonSelectedByID(int nID) const
{
	for (int i = 0; i < m_arrButtons.size(); i++)
	{
		if (m_arrButtons[i].nID == nID)
		{
			return m_arrButtons[i].bSelected;
		}
	}
	return false;
}

// 获取组内选中的按钮索引
int CKLineVolume::GetSelectedButtonInGroup(int nGroupID) const
{
	for (int i = 0; i < m_arrButtons.size(); i++)
	{
		if (m_arrButtons[i].nGroupID == nGroupID && m_arrButtons[i].bSelected)
		{
			return i;
		}
	}
	return -1;
}

// 绘制价格坐标
void CKLineVolume::DrawPriceScale(CDC* pDC)
{
	// 如果没有K线数据，直接返回
	if (m_nKLineCount <= 0)
		return;
	
	// 创建字体和画笔
	CFont font;
	font.CreateFont(22, 0, 0, 0, FW_NORMAL, FALSE, FALSE, 0, 
		ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
		1, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&font);
	int nOldBkMode = pDC->SetBkMode(TRANSPARENT);
	COLORREF oldTextColor = pDC->SetTextColor(RGB(255, 0, 0));  // 将文本颜色改为红色
	
	// 创建深红色实线画笔用于绘制水平参考线
	CPen penDashLine(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细实线
	CPen* pOldPen = pDC->SelectObject(&penDashLine);
	
	// 计算主图区域的价格刻度 - 使用当前可见范围的价格
	double fPriceRange = m_fDisplayMaxPrice - m_fDisplayMinPrice;
	if (fPriceRange <= 0.0001) // 避免除以0或近似为0的情况
		return;
	
	// 修改为5条水平分割线（分为6个区域）
	int nDivisions = 6;
	double fPriceStep = fPriceRange / (nDivisions - 1);
	int nStep = m_rectMainChart.Height() / (nDivisions - 1);
	
	// 绘制主图区域的价格刻度
	for (int i = 0; i < nDivisions; i++)
	{
		// 计算价格和对应的Y坐标
		double fPrice = m_fDisplayMinPrice + fPriceStep * i;
		int y = m_rectMainChart.bottom - i * nStep;
		
		// 格式化价格文本
		CString strPrice;
		strPrice.Format(_T("%.2f"), fPrice);
		
		// 计算文本位置
		CSize textSize;
		GetTextExtentPoint32(pDC->GetSafeHdc(), strPrice, strPrice.GetLength(), &textSize);
		
		// 绘制价格文本（靠近分割线）
		int x = m_rectCoordArea.left + 5;
		pDC->TextOut(x, y - textSize.cy / 2, strPrice);
		
		// 绘制对应的水平参考线
		// 不绘制最顶部和最底部的线，所以只绘制中间的4条线
		if (i > 0 && i < nDivisions - 1)
		{
			// 使用SetPixel方式手动绘制虚线，解决PS_DOT在某些环境下不显示的问题
			COLORREF lineColor = RGB(128, 0, 0); // 深红色
			int lineStart = m_rectMainChart.left;
			int lineEnd = m_rectMainChart.right;
			
			// 每隔3个像素点绘制一个点，形成虚线效果
			for (int pixelX = lineStart; pixelX < lineEnd; pixelX += 3)
			{
				pDC->SetPixel(pixelX, y, lineColor);
			}
		}
	}
	
	// 如果有成交量数据，绘制成交量区域的刻度
	if (m_fMaxVolume > 0.0001)
	{
		double fVolume = m_fMaxVolume;
		
		// 计算成交量文本大小（用于垂直居中显示）
		CString strVolume;
		strVolume.Format(_T("%.1f万"), fVolume / 10000.0);
		CSize textSize;
		GetTextExtentPoint32(pDC->GetSafeHdc(), strVolume, strVolume.GetLength(), &textSize);
		
		// 仅在1/3和2/3处显示成交量数值，移除顶部和底部显示
		int nOneThird = m_rectVolume.Height() / 3;
		
		// 1. 2/3成交量位置（1/3处）
		int y = m_rectVolume.top + nOneThird;
		strVolume.Format(_T("%.1f万"), fVolume * 2 / 3 / 10000.0);
		pDC->TextOut(m_rectCoordArea.left + 5, y - textSize.cy / 2, strVolume);
		
		// 2. 1/3成交量位置（2/3处）
		y = m_rectVolume.top + nOneThird * 2;
		strVolume.Format(_T("%.1f万"), fVolume / 3 / 10000.0);
		pDC->TextOut(m_rectCoordArea.left + 5, y - textSize.cy / 2, strVolume);
	}
	
	// 恢复设置
	pDC->SelectObject(pOldFont);
	pDC->SetBkMode(nOldBkMode);
	pDC->SetTextColor(oldTextColor);
	pDC->SelectObject(pOldPen);
}

// 添加鼠标滚轮消息处理函数
BOOL CKLineVolume::OnMouseWheel(UINT nFlags, short zDelta, CPoint pt)
{
	// 在切换股票之前隐藏十字光标
	m_bShowCrossCursor = false;
	m_bMouseDown = false; // 重置鼠标左键状态
	
	// 如果按住Ctrl键，则调整K线缩放比例而不是切换股票
	if ((nFlags & MK_CONTROL) == MK_CONTROL)
	{
		// 放大/缩小K线图
		const double scaleStep = 0.1;  // 每次缩放的步长
		
		if (zDelta > 0)
		{
			// 放大
			m_fScaleFactor += scaleStep;
			if (m_fScaleFactor > 8.0)  // 限制最大缩放系数
				m_fScaleFactor = 8.0;
		}
		else
		{
			// 缩小
			m_fScaleFactor -= scaleStep;
			if (m_fScaleFactor < 0.1)  // 限制最小缩放系数
				m_fScaleFactor = 0.1;
		}
		
		// 根据缩放系数计算K线宽度和间隔
		m_nKLineWidth = max(1, (int)(m_nBaseKLineWidth * m_fScaleFactor));
		m_nKLineGap = max(1, (int)(m_nBaseKLineGap * m_fScaleFactor));
		
		// 计算旧的显示数量
		int nOldDisplayCount = m_nDisplayCount;
		
		// 重新计算可显示的K线数量
		m_nDisplayCount = (m_rectMainChart.Width() - 10) / (m_nKLineWidth + m_nKLineGap);
		
		// 如果可显示数量大于K线总数，调整为K线总数
		if (m_nDisplayCount > m_nKLineCount)
			m_nDisplayCount = m_nKLineCount;
		
		// 保存右侧K线的索引（最新K线的索引）
		int rightIndex = m_nDisplayOffset + nOldDisplayCount - 1;
		
		// 调整偏移量，使缩放后右侧K线保持不变
		m_nDisplayOffset = rightIndex - m_nDisplayCount + 1;
		
		// 调整偏移量，确保在有效范围内
		m_nDisplayOffset = max(0, min(m_nDisplayOffset, m_nKLineCount - m_nDisplayCount));
		
		// 重绘
		Invalidate();
		
		return TRUE;
	}
	
	// 获取文档对象
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return FALSE;
		
	// 防止滚轮事件传播导致的闪烁问题
	// 获取父视图以阻止重绘传播
	CKLineView* pParentView = GetParentKLineView();
	if (pParentView)
	{
		// 获取KLineInfo视图并阻止其重绘
		CKLineInfo* pInfoView = nullptr;
		if (pParentView->m_wndSplitter1.GetSafeHwnd())
		{
			pInfoView = (CKLineInfo*)pParentView->m_wndSplitter1.GetPane(0, 1);
			if (pInfoView)
			{
				// 设置标志以阻止不必要的重绘
				pInfoView->SetRedraw(FALSE);
			}
		}
		
		// 根据滚轮方向切换股票
		if (zDelta > 0)
		{
			// 向上滚动，切换到上一支股票
			pDoc->ShowPreviousStock();
		}
		else
		{
			// 向下滚动，切换到下一支股票
			pDoc->ShowNextStock();
		}
		
		// 恢复KLineInfo视图的重绘
		if (pInfoView)
		{
			pInfoView->SetRedraw(TRUE);
			pInfoView->Invalidate();
		}
		
		return TRUE; // 返回TRUE表示已处理该消息
	}
	
	// 如果未获取到父视图，则按原有方式处理
	if (zDelta > 0)
	{
		// 向上滚动，切换到上一支股票
		pDoc->ShowPreviousStock();
	}
	else
	{
		// 向下滚动，切换到下一支股票
		pDoc->ShowNextStock();
	}
	
	return TRUE; // 返回TRUE表示已处理该消息
}

// 添加键盘事件处理函数
void CKLineVolume::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags)
{
	// 处理键盘事件
	bool bNeedRedraw = false;
	
	switch (nChar)
	{
	case VK_RETURN: // 回车键 - 切换到股票列表视图
		{
			// 获取主框架指针
			CMainFrame* pMainFrame = (CMainFrame*)AfxGetMainWnd();
			if (pMainFrame)
			{
				// 切换到股票列表视图
				pMainFrame->SwitchView(pMainFrame->m_nStockListViewID);
				TRACE(_T("KLineVolume: 按回车键切换到股票列表视图\n"));
			}
		}
		break;
		
	case VK_LEFT:  // 左键 - 向左平移一日（显示更早的K线）
		{
			// 调整偏移量（每次只移动一个K线）
			m_nDisplayOffset--;
			
			// 确保偏移量不小于0
			if (m_nDisplayOffset < 0)
				m_nDisplayOffset = 0;
			
			bNeedRedraw = true;
		}
		break;
		
	case VK_RIGHT:  // 右键 - 向右平移一日（显示更新的K线）
		{
			// 调整偏移量（每次只移动一个K线）
			m_nDisplayOffset++;
			
			// 确保偏移量不会导致显示超出范围
			if (m_nDisplayOffset + m_nDisplayCount > m_nKLineCount)
				m_nDisplayOffset = m_nKLineCount - m_nDisplayCount;
			
			// 确保偏移量不小于0（以防万一）
			if (m_nDisplayOffset < 0)
				m_nDisplayOffset = 0;
			
			bNeedRedraw = true;
		}
		break;
		
	case VK_UP:  // 上键 - 放大K线图（与鼠标滚轮向上滚动效果相同）
		{
			const double scaleStep = 0.1;  // 每次缩放的步长
			
			// 放大
			m_fScaleFactor += scaleStep;
			if (m_fScaleFactor > 8.0)  // 限制最大缩放系数
				m_fScaleFactor = 8.0;
			
			// 根据缩放系数计算K线宽度和间隔
			m_nKLineWidth = max(1, (int)(m_nBaseKLineWidth * m_fScaleFactor));
			m_nKLineGap = max(1, (int)(m_nBaseKLineGap * m_fScaleFactor));
			
			// 计算旧的显示数量
			int nOldDisplayCount = m_nDisplayCount;
			
			// 重新计算可显示的K线数量
			m_nDisplayCount = (m_rectMainChart.Width() - 10) / (m_nKLineWidth + m_nKLineGap);
			
			// 如果可显示数量大于K线总数，调整为K线总数
			if (m_nDisplayCount > m_nKLineCount)
				m_nDisplayCount = m_nKLineCount;
			
			// 保存右侧K线的索引（最新K线的索引）
			int rightIndex = m_nDisplayOffset + nOldDisplayCount - 1;
			
			// 调整偏移量，使缩放后右侧K线保持不变
			m_nDisplayOffset = rightIndex - m_nDisplayCount + 1;
			
			// 调整偏移量，确保在有效范围内
			m_nDisplayOffset = max(0, min(m_nDisplayOffset, m_nKLineCount - m_nDisplayCount));
			
			bNeedRedraw = true;
		}
		break;
		
	case VK_DOWN:  // 下键 - 缩小K线图（与鼠标滚轮向下滚动效果相同）
		{
			const double scaleStep = 0.1;  // 每次缩放的步长
			
			// 缩小
			m_fScaleFactor -= scaleStep;
			if (m_fScaleFactor < 0.1)  // 限制最小缩放系数
				m_fScaleFactor = 0.1;
			
			// 根据缩放系数计算K线宽度和间隔
			m_nKLineWidth = max(1, (int)(m_nBaseKLineWidth * m_fScaleFactor));
			m_nKLineGap = max(1, (int)(m_nBaseKLineGap * m_fScaleFactor));
			
			// 计算旧的显示数量
			int nOldDisplayCount = m_nDisplayCount;
			
			// 重新计算可显示的K线数量
			m_nDisplayCount = (m_rectMainChart.Width() - 10) / (m_nKLineWidth + m_nKLineGap);
			
			// 如果可显示数量大于K线总数，调整为K线总数
			if (m_nDisplayCount > m_nKLineCount)
				m_nDisplayCount = m_nKLineCount;
			
			// 保存右侧K线的索引（最新K线的索引）
			int rightIndex = m_nDisplayOffset + nOldDisplayCount - 1;
			
			// 调整偏移量，使缩放后右侧K线保持不变
			m_nDisplayOffset = rightIndex - m_nDisplayCount + 1;
			
			// 调整偏移量，确保在有效范围内
			m_nDisplayOffset = max(0, min(m_nDisplayOffset, m_nKLineCount - m_nDisplayCount));
			
			bNeedRedraw = true;
		}
		break;
		
	case VK_HOME:  // Home键 - 跳转到最早的K线
		{
			// 设置偏移量为0，显示最早的K线
			m_nDisplayOffset = 0;
			TRACE(_T("按Home键 - 跳转到最早K线, 偏移量=%d\n"), m_nDisplayOffset);
			bNeedRedraw = true;
		}
		break;
		
	case VK_END:  // End键 - 跳转到最新的K线
		{
			// 计算最后一个K线的偏移量，确保显示最新的K线数据
			m_nDisplayOffset = max(0, m_nKLineCount - m_nDisplayCount);
			TRACE(_T("按End键 - 跳转到最新K线, 偏移量=%d\n"), m_nDisplayOffset);
			bNeedRedraw = true;
		}
		break;
	}
	
	// 如果需要重绘，刷新视图
	if (bNeedRedraw)
	{
		Invalidate();
	}
	
	CView::OnKeyDown(nChar, nRepCnt, nFlags);
}

// 初始化成交量均线
void CKLineVolume::InitVolumeMA()
{
	// 清空原有均线数据
	m_arrVolumeMA.clear();
	
	// 添加5日成交量均线
	MAData ma5;
	ma5.nPeriod = 5;
	ma5.clrLine = RGB(255, 255, 0);  // 黄色
	
	// 确保K线数据存在
	if (m_nKLineCount > 0)
	{
		ma5.arrMA.resize(m_nKLineCount);
	}
	m_arrVolumeMA.push_back(ma5);
	
	// 添加10日成交量均线
	MAData ma10;
	ma10.nPeriod = 10;
	ma10.clrLine = RGB(0, 255, 0);   // 绿色
	
	// 确保K线数据存在
	if (m_nKLineCount > 0)
	{
		ma10.arrMA.resize(m_nKLineCount);
	}
	m_arrVolumeMA.push_back(ma10);
}

// 计算成交量均线
void CKLineVolume::CalcVolumeMA()
{
	// 如果没有K线数据，直接返回
	if (m_nKLineCount <= 0)
		return;
		
	// 计算每条成交量均线
	for (int i = 0; i < m_arrVolumeMA.size(); i++)
	{
		MAData& ma = m_arrVolumeMA[i];
		int nPeriod = ma.nPeriod;
		
		// 确保数组大小与K线数量一致
		if (ma.arrMA.size() != m_nKLineCount)
		{
			ma.arrMA.clear();
			ma.arrMA.resize(m_nKLineCount);
		}
		
		// 计算每个点的均线值
		for (int j = 0; j < m_nKLineCount; j++)
		{
			// 如果数据点不足，均线值为0
			if (j < nPeriod - 1)
			{
				 ma.arrMA[j] = 0.0;
				 continue;
			}
			
			// 计算n日成交量平均值
			double fSum = 0.0;
			for (int k = 0; k < nPeriod; k++)
			{
				// 确保索引不越界
				int nIndex = j - k;
				if (nIndex >= 0 && nIndex < m_nKLineCount)
				{
					fSum += m_vecKLine[nIndex]._Volume;
				}
			}
			ma.arrMA[j] = fSum / nPeriod;
		}
	}
}

// 绘制成交量均线
void CKLineVolume::DrawVolumeMA(CDC* pDC)
{
	// 如果没有成交量数据或K线数据，直接返回
	if (m_fMaxVolume <= 0.0 || m_nKLineCount <= 0)
		return;
	
	// 计算成交量到Y坐标的转换比例
	double fVolumeScale = (double)m_rectVolume.Height() / m_fMaxVolume;
	
	// 创建画笔
	CPen* pOldPen = NULL;
	
	// 绘制每条成交量均线
	for (int i = 0; i < m_arrVolumeMA.size(); i++)
	{
		MAData& ma = m_arrVolumeMA[i];
		
		// 确保均线数组有效，且均线显示标志为true
		if (ma.arrMA.empty() || !ma.bShow)
			continue;
		
		// 创建均线画笔
		CPen penMA(PS_SOLID, 1, ma.clrLine);
		pOldPen = pDC->SelectObject(&penMA);
		
		// 绘制均线
		for (int j = 0; j < m_nDisplayCount - 1; j++)
		{
			int nIndex = m_nDisplayOffset + j;
			int nNextIndex = nIndex + 1;
			
			// 确保索引在有效范围内
			if (nIndex >= 0 && nIndex < m_nKLineCount && 
				nIndex < ma.arrMA.size() && 
				nNextIndex >= 0 && nNextIndex < m_nKLineCount && 
				nNextIndex < ma.arrMA.size())
			{
				// 计算当前点和下一个点的坐标
				int x1 = m_rectMainChart.left + 5 + j * (m_nKLineWidth + m_nKLineGap) + m_nKLineWidth / 2;
				int x2 = m_rectMainChart.left + 5 + (j + 1) * (m_nKLineWidth + m_nKLineGap) + m_nKLineWidth / 2;
				
				int y1 = m_rectVolume.bottom - (int)(ma.arrMA[nIndex] * fVolumeScale);
				int y2 = m_rectVolume.bottom - (int)(ma.arrMA[nNextIndex] * fVolumeScale);
				
				// 绘制线段
				pDC->MoveTo(x1, y1);
				pDC->LineTo(x2, y2);
			}
		}
	}
	
	// 恢复之前的画笔
	if (pOldPen)
		pDC->SelectObject(pOldPen);
}

// 绘制成交量信息条
void CKLineVolume::DrawVolumeInfoBar(CDC* pDC)
{
	// 计算成交量信息条区域，位于K线主图下方
	m_rectVolumeInfoBar = CRect(
		m_rectKLineArea.left,
		m_rectMainChart.bottom,
		m_rectKLineArea.right,
		m_rectMainChart.bottom + m_nVolumeInfoBarHeight
	);
	
	// 绘制深红色分割线
	CPen penSplitLine(PS_SOLID, 1, RGB(128, 0, 0)); // 深红色细实线
	CPen* pOldPen = pDC->SelectObject(&penSplitLine);
	
	// 上边界线
	pDC->MoveTo(m_rectVolumeInfoBar.left, m_rectVolumeInfoBar.top);
	pDC->LineTo(m_rectVolumeInfoBar.right, m_rectVolumeInfoBar.top);
	
	// 下边界线 - 改为使用深红色细虚线
	CPen penDashLine(PS_DOT, 1, RGB(128, 0, 0)); // 深红色细虚线
	pDC->SelectObject(&penDashLine);
	pDC->MoveTo(m_rectVolumeInfoBar.left, m_rectVolumeInfoBar.bottom);
	pDC->LineTo(m_rectVolumeInfoBar.right, m_rectVolumeInfoBar.bottom);
	
	// 如果有K线数据，显示当前成交量和成交额信息
	if (m_nKLineCount > 0 && m_nCursorIndex >= 0 && m_nCursorIndex < m_nKLineCount)
	{
		// 创建字体
		CFont font;
		font.CreateFont(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, 0, 
			ANSI_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
			DEFAULT_QUALITY, DEFAULT_PITCH | FF_SWISS, _T("微软雅黑"));
		
		CFont* pOldFont = pDC->SelectObject(&font);
		int nOldBkMode = pDC->SetBkMode(TRANSPARENT);
		
		// 获取当前K线数据
		KLINE_DATA& data = m_vecKLine[m_nCursorIndex];
		
		// 确定颜色：上涨红色，下跌蓝色，持平白色
		COLORREF clrRising = RGB(255, 0, 0);    // 上涨红色
		COLORREF clrFalling = RGB(0, 191, 255); // 下跌天蓝色
		COLORREF clrFlat = RGB(255, 255, 255);  // 持平白色
		COLORREF clrValue;                      // 实际数值显示颜色
		
		// 根据收盘价与前收盘价比较确定颜色
		if (data._Close > data._PreClose)
			clrValue = clrRising;
		else if (data._Close < data._PreClose)
			clrValue = clrFalling;
		else
			clrValue = clrFlat;
		
		// 设置标签文本颜色为白色
		COLORREF clrLabel = RGB(255, 255, 255);
		COLORREF oldTextColor = pDC->SetTextColor(clrLabel);
		
		// 格式化成交量（单位：万手）
		double fVolumeInWan = data._Volume / 10000.0;
		
		// 格式化成交额（单位：亿元）
		double fAmountInYi = data._Amount / 100000000.0;
		
		// 绘制成交量标签
		CString strVolumeLabel = _T("成交量: ");
		pDC->TextOut(m_rectVolumeInfoBar.left + 5, m_rectVolumeInfoBar.top + 5, strVolumeLabel);
		
		// 计算成交量值的位置
		CSize sizeVolumeLabel = pDC->GetTextExtent(strVolumeLabel);
		int nVolumeValueX = m_rectVolumeInfoBar.left + 5 + sizeVolumeLabel.cx;
		
		// 绘制成交量值(根据涨跌设置颜色)
		CString strVolumeValue;
		strVolumeValue.Format(_T("%.2f万手"), fVolumeInWan);
		pDC->SetTextColor(clrValue);
		pDC->TextOut(nVolumeValueX, m_rectVolumeInfoBar.top + 5, strVolumeValue);
		
		// 计算成交额标签位置
		CSize sizeVolumeValue = pDC->GetTextExtent(strVolumeValue);
		int nAmountLabelX = nVolumeValueX + sizeVolumeValue.cx + 20; // 增加一些间距
		
		// 绘制成交额标签(白色)
		CString strAmountLabel = _T("成交额: ");
		pDC->SetTextColor(clrLabel);
		pDC->TextOut(nAmountLabelX, m_rectVolumeInfoBar.top + 5, strAmountLabel);
		
		// 计算成交额值的位置
		CSize sizeAmountLabel = pDC->GetTextExtent(strAmountLabel);
		int nAmountValueX = nAmountLabelX + sizeAmountLabel.cx;
		
		// 绘制成交额值(根据涨跌设置颜色)
		CString strAmountValue;
		strAmountValue.Format(_T("%.2f亿元"), fAmountInYi);
		pDC->SetTextColor(clrValue);
		pDC->TextOut(nAmountValueX, m_rectVolumeInfoBar.top + 5, strAmountValue);
		
		// 恢复设置
		pDC->SelectObject(pOldFont);
		pDC->SetBkMode(nOldBkMode);
		pDC->SetTextColor(oldTextColor);
	}
	
	pDC->SelectObject(pOldPen);
}

/**
 * @brief 从本地二进制文件加载K线数据
 * @param code 股票代码
 * @param klineData 用于存储加载的K线数据的向量
 * @return bool 是否成功加载数据
 */
bool CKLineVolume::LoadKLineDataFromLocalFile(const CString& code, std::vector<KLINE_DATA>& klineData)
{
    // 清空原有数据
    klineData.clear();
    
    // 构建文件路径
    CString filePath;
    filePath.Format(_T("F:\\RedCow\\KLine\\%s.day"), code);
    
    // 使用CreateFile打开文件
    HANDLE hFile = CreateFile(
        filePath,                    // 文件路径
        GENERIC_READ,                // 读取访问权限
        FILE_SHARE_READ,             // 共享模式
        NULL,                        // 安全属性
        OPEN_EXISTING,               // 打开已存在的文件
        FILE_ATTRIBUTE_NORMAL,       // 文件属性
        NULL                         // 模板文件句柄
    );
    
    // 检查文件是否成功打开
    if (hFile == INVALID_HANDLE_VALUE)
    {
        TRACE(_T("无法打开K线数据文件: %s, 错误码: %d\n"), filePath, GetLastError());
        return false;
    }
    
    // 获取文件大小
    DWORD fileSize = GetFileSize(hFile, NULL);
    if (fileSize == INVALID_FILE_SIZE)
    {
        TRACE(_T("获取文件大小失败: %s, 错误码: %d\n"), filePath, GetLastError());
        CloseHandle(hFile);
        return false;
    }
    
    // 计算文件中包含的K线数据记录数
    DWORD recordCount = fileSize / sizeof(KLINE_DATA);
    if (recordCount == 0)
    {
        TRACE(_T("K线数据文件为空: %s\n"), filePath);
        CloseHandle(hFile);
        return false;
    }
    
    // 分配内存用于读取数据
	KLINE_DATA* pBuffer = new KLINE_DATA[recordCount];
    if (!pBuffer)
    {
        TRACE(_T("内存分配失败，无法读取K线数据\n"));
        CloseHandle(hFile);
        return false;
    }
    
    // 读取文件内容
    DWORD bytesRead;
    BOOL bResult = ReadFile(
        hFile,                       // 文件句柄
        pBuffer,                     // 缓冲区
        fileSize,                    // 要读取的字节数
        &bytesRead,                  // 实际读取的字节数
        NULL                         // 重叠结构
    );
    
    // 关闭文件句柄
    CloseHandle(hFile);
    
    // 检查读取是否成功
    if (!bResult || bytesRead != fileSize)
    {
        TRACE(_T("读取K线数据文件失败: %s, 错误码: %d\n"), filePath, GetLastError());
        delete[] pBuffer;
        return false;
    }
    
    // 将二进制数据转换为KLineData结构
    for (DWORD i = 0; i < recordCount; i++)
    {
		KLINE_DATA& rawData = pBuffer[i];
        
        // 添加到结果向量
        klineData.push_back(rawData);
    }
    
    // 释放缓冲区
    delete[] pBuffer;
    
    // 按时间排序（从早到晚）
    //std::sort(klineData.begin(), klineData.end(), [](const KLineData& a, const KLineData& b) {
    //    return a.time < b.time;
    //});
    
    TRACE(_T("成功从文件加载 %d 条K线数据: %s\n"), klineData.size(), filePath);
    return true;
}

// 响应文档数据更新
void CKLineVolume::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 调用基类的OnUpdate方法
	CView::OnUpdate(pSender, lHint, pHint);
	
	// 从文档获取当前股票代码
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
		
		// 如果当前显示的股票代码与文档中的不同，则更新
		if (m_strCode != strCurrentStock)
		{
			// 设置新的股票代码，这会触发数据加载和视图更新
			SetStockCode(strCurrentStock);
			
			TRACE(_T("CKLineVolume::OnUpdate - 股票代码已更新: %s\n"), strCurrentStock);
		}
	}
}
