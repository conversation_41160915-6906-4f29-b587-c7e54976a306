﻿#pragma once
#include <map>
using namespace std;


// CStockSplitter.h: CStockSplitter 类的接口
// 自定义分割窗口类，继承自CSplitterWnd，实现无边框，深红色分隔条

class CStockSplitter : public CSplitterWnd
{
	DECLARE_DYNAMIC(CStockSplitter)

// 构造
public:
	CStockSplitter();
	virtual ~CStockSplitter();

// 属性
public:
	COLORREF m_clrSplitterBar;  // 分隔条颜色
	BOOL m_bLocked;            // 分隔条锁定状态

// 操作
public:
	// 设置分隔条锁定状态
	void SetLocked(BOOL bLocked);
	// 获取分隔条锁定状态
	BOOL IsLocked() const;

// 重写
public:
	virtual void OnDrawSplitter(CDC* pDC, ESplitType nType, const CRect& rect);
	virtual BOOL CreateStatic(CWnd* pParentWnd, int nRows, int nCols, 
		DWORD dwStyle = WS_CHILD | WS_VISIBLE, UINT nID = AFX_IDW_PANE_FIRST);
	// 重写鼠标处理相关方法
	virtual void OnInvertTracker(const CRect& rect);
	virtual void StartTracking(int ht);
	virtual void StopTracking(BOOL bAccept);
	virtual int HitTest(CPoint pt) const;  // 重写HitTest方法，用于判断鼠标位置

	int AddView(int nRow, int nCol, CRuntimeClass* pViewClass, CCreateContext* pContext);

	void SwitchView(int nViewID);
	CWnd* GetView(int nViewID);

protected:
	map<int, long> m_mapViewPane;
	map<long, int> m_mapCurrentViews;
	map<int, CWnd*> m_mapIDViews;
	int m_nIDCounter;

	CWnd* GetCurrentView(int nRow, int nCol, int* nCurID);
	void SetCurrentView(int nRow, int nCol, int nViewID);
	int HideCurrentView(int nRow, int nCol);
	void GetPaneFromViewID(int nViewID, CPoint* pane);
// 实现
protected:
	DECLARE_MESSAGE_MAP()
	afx_msg BOOL OnSetCursor(CWnd* pWnd, UINT nHitTest, UINT message);
}; 