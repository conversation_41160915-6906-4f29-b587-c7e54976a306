﻿#pragma once
#include <vector>
#include <string>
#include <algorithm>  // 添加algorithm头文件，用于std::transform函数
#include <afxwin.h>  // 添加用于COLORREF的头文件


// 市场类型枚举
enum MarketType {
	MARKET_ALL = 0,			// 全部
	MARKET_SH_A,			// 上证A股
	MARKET_SZ_A,			// 深证A股
	MARKET_GEM,				// 创业板
	MARKET_STAR,			// 科创板
	MARKET_BJ_A				// 北证A股
};

// 大盘指数类型
enum MarketIndexType {
	INDEX_SH = 0,      // 上证指数
	INDEX_SZ,          // 深证成指
	INDEX_GEM,         // 创业板指
	INDEX_STAR,        // 科创50
	INDEX_BJ           // 北证50
};
// K线数据结构
struct KLINE_DATA
{
	DWORD		_Date;				// 日期
	float		_Open;				// 开盘
	float		_High;				// 最高
	float		_Low;				// 最低
	float		_Close;				// 收盘
	float		_PreClose;			// 昨收
	double		_Volume;			// 成交量(股)
	double		_Amount;			// 成交额(元)

	// 默认构造函数
	KLINE_DATA() :
		_Date(0),
		_Open(0.0f),
		_High(0.0f),
		_Low(0.0f),
		_Close(0.0f),
		_PreClose(0.0f),
		_Volume(0.0),
		_Amount(0.0)
	{
	}

	// 带参数构造函数
	KLINE_DATA(DWORD date, float open, float high, float low, float close,
			   float preClose = 0.0f, double volume = 0.0, double amount = 0.0) :
		_Date(date),
		_Open(open),
		_High(high),
		_Low(low),
		_Close(close),
		_PreClose(preClose),
		_Volume(volume),
		_Amount(amount)
	{
	}
};


// 分时数据结构
struct TLINE_DATA
{
	int		_Time;			// 时间
	float	_Price;			// 价格
	float   _avgPrice;      // 均价
	double	_Volume;		// 分时成交量
	double	_Amount;		// 分时成交额

    TLINE_DATA() : _Price(0.0f), _avgPrice(0.0f), _Volume(0.0), _Amount(0.0), _Time(0){}

    TLINE_DATA(const CTime& t, float p, double v, double amount, float avg)
		: _Time(t.GetHour() * 100 + t.GetMinute()), _Price(p), _Volume(v), _Amount(amount), _avgPrice(avg) {
	}
};


// 五档报价结构
struct Level2Data
{
	float	_bidPrice[5];     // 买一到买五价
	float	_askPrice[5];     // 卖一到卖五价
	DWORD	_bidVolume[5];    // 买一到买五量
	DWORD	_askVolume[5];    // 卖一到卖五量

	Level2Data()
	{
		for (int i = 0; i < 5; i++)
		{
			_bidPrice[i] = 0;
			_bidVolume[i] = 0;
			_askPrice[i] = 0;
			_askVolume[i] = 0;
		}
	}
};

//// 集合竞价分时数据结构
struct AUC_DATA
{
	int			_Time;				// 时间
	float		_Price;				// 匹配价(元)
	float		_MatchedVol;		// 匹配量(股)
	float		_UnMatchedVol;		// 未匹配量(股)
	byte		_Direction;			// 方向（买-1 卖-0）

	// 默认构造函数
	AUC_DATA() :
		_Time(0),
		_Price(0.0f),
		_MatchedVol(0.0f),
		_UnMatchedVol(0.0f),
		_Direction(0)
	{
	}

	// 带参数构造函数
	AUC_DATA(int time, float price, float matchedVol, float unmatchedVol, byte direction) :
		_Time(time),
		_Price(price),
		_MatchedVol(matchedVol),
		_UnMatchedVol(unmatchedVol),
		_Direction(direction)
	{
	}
};

// 基本股票数据结构
struct StockData
{
	int							_ID;                            // 数据库主键ID
	std::string					_Code;                          // 股票代码
	std::string					_Name;                          // 股票名称
	std::string					_Industry;                      // 所属行业
	std::string					_Plate;                         // 所属板块
	std::string					_Theme;                         // 题材
	std::string					_Style;                         // 风格
	double						_CirculatingValue;              // 流通值(亿元)
	double						_ActualCirculatingValue;        // 实际流通值(亿元)
	MarketType					_MarketType;                    // 市场类型

	DWORD						_Date;				// 日期
	float						_Open;				// 开盘价
	float						_Close;				// 最新价
	float						_preClose;			// 昨收价
	float						_High;				// 最高价
	float						_Low;				// 最低价
	double						_Volume;			// 成交量(股)
	double						_Amount;			// 成交额
	float						_Turnover;			// 换手率(%)
	float						_VolumeRatio;		// 量比
	int							_buyVolume;         // 内盘(手)
	int							_sellVolume;        // 外盘(手)
	Level2Data					_Level2;			// 五档报价

	double						_MainInflow;		// 主力买入(元)
	double						_MainOutflow;		// 主力卖出(元)

	std::vector<AUC_DATA>		_vecAucData;		// 集合竞价数据
	std::vector<TLINE_DATA>		_vecTimeLine;		// 分时数据
	std::vector<KLINE_DATA>		_vecKLine;			// K线数据

	// 默认构造函数
	StockData() :
		_ID(0),
		_Code(""),
		_Name(""),
		_Industry(""),
		_Plate(""),
		_Theme(""),
		_Style(""),
		_CirculatingValue(0.0),
		_ActualCirculatingValue(0.0),
		_MarketType(MARKET_ALL),
		_Date(0),
		_Open(0.0f),
		_Close(0.0f),
		_preClose(0.0f),
		_High(0.0f),
		_Low(0.0f),
		_Volume(0.0),
		_Amount(0.0),
		_Turnover(0.0f),
		_VolumeRatio(0.0f),
		_buyVolume(0),
		_sellVolume(0),
		_MainInflow(0.0),
		_MainOutflow(0.0)
	{
		// 向量成员变量(_vecAucData, _vecTimeLine, _vecKLine)已经默认初始化为空
		// Level2Data已有自己的构造函数进行初始化
	}

	// 带参数构造函数
	StockData(int id, const std::string& code, const std::string& name,
			  MarketType marketType = MARKET_ALL) :
		_ID(id),
		_Code(code),
		_Name(name),
		_Industry(""),
		_Plate(""),
		_Theme(""),
		_Style(""),
		_CirculatingValue(0.0),
		_ActualCirculatingValue(0.0),
		_MarketType(marketType),
		_Date(0),
		_Open(0.0f),
		_Close(0.0f),
		_preClose(0.0f),
		_High(0.0f),
		_Low(0.0f),
		_Volume(0.0),
		_Amount(0.0),
		_Turnover(0.0f),
		_VolumeRatio(0.0f),
		_buyVolume(0),
		_sellVolume(0),
		_MainInflow(0.0),
		_MainOutflow(0.0)
	{
		// 向量成员变量已经默认初始化为空
	}

	// 清除数据方法
	void Reset()
	{
		_ID = 0;
		_Code = "";
		_Name = "";
		_Industry = "";
		_Plate = "";
		_Theme = "";
		_Style = "";
		_CirculatingValue = 0.0;
		_ActualCirculatingValue = 0.0;
		_MarketType = MARKET_ALL;
		_Date = 0;
		_Open = 0.0f;
		_Close = 0.0f;
		_preClose = 0.0f;
		_High = 0.0f;
		_Low = 0.0f;
		_Volume = 0.0;
		_Amount = 0.0;
		_Turnover = 0.0f;
		_VolumeRatio = 0.0f;
		_buyVolume = 0;
		_sellVolume = 0;
		_MainInflow = 0.0;
		_MainOutflow = 0.0;

		// 清空向量数据
		_vecAucData.clear();
		_vecTimeLine.clear();
		_vecKLine.clear();

		// 重置Level2数据
		_Level2 = Level2Data();
	}
};



// 大盘分时数据
struct MarketIndexData
{
	int				_Time;							// 时间
	float			_preIndex;						// 昨收指数
	float			_currIndex;						// 当前指数
	float			_leadIndex;						// 领先指数
	double			_Volume;						// 成交量
	double			_Amount;						// 成交额
	std::string		_Code;							// 指数代码
	std::string		_Name;							// 指数名称

	// 默认构造函数
	MarketIndexData() :
		_Time(0),
		_preIndex(0.0f),
		_currIndex(0.0f),
		_leadIndex(0.0f),
		_Volume(0.0),
		_Amount(0.0),
		_Code(""),
		_Name("")
	{
	}

	// 带参数构造函数
	MarketIndexData(int time, float preIndex, float currIndex, float leadIndex,
					double volume = 0.0, double amount = 0.0,
					const std::string& code = "", const std::string& name = "") :
		_Time(time),
		_preIndex(preIndex),
		_currIndex(currIndex),
		_leadIndex(leadIndex),
		_Volume(volume),
		_Amount(amount),
		_Code(code),
		_Name(name)
	{
	}

	// 计算涨跌额
	float GetChange() const {
		return _currIndex - _preIndex;
	}

	// 计算涨跌幅
	float GetChangePercent() const {
		if (_preIndex != 0.0f) {
			return ((_currIndex - _preIndex) / _preIndex) * 100.0f;
		}
		return 0.0f;
	}
};

// 市场统计数据结构
struct MarketStatData
{
	double								_totalTurnover;			// 两市成交额（亿元）
	int									_upCount;				// 上涨家数
	int									_downCount;				// 下跌家数
	int									_limitUpCount;			// 涨停家数
	int									_limitDownCount;		// 跌停家数
	std::vector<MarketIndexData>		_vecIndexSH;			// 上证指数分时数据
	std::vector<MarketIndexData>		_vecIndexSZ;			// 深证成指分时数据
	std::vector<MarketIndexData>		_vecIndexGEM;			// 创业板分时数据
	std::vector<MarketIndexData>		_vecIndexSTAR;			// 科创板分时数据
	std::vector<MarketIndexData>		_vecIndexBJ;			// 北证分时数据
	std::vector<MarketIndexData>		_vecIndexSHA;			// 上证A股分时数据
	std::vector<MarketIndexData>		_vecIndexSZA;			// 深证A股分时数据

	// 默认构造函数
	MarketStatData() :
		_totalTurnover(0.0),
		_upCount(0),
		_downCount(0),
		_limitUpCount(0),
		_limitDownCount(0)
	{
		// 向量成员已默认初始化为空
	}

	// 清除数据方法
	void Reset()
	{
		_totalTurnover = 0.0;
		_upCount = 0;
		_downCount = 0;
		_limitUpCount = 0;
		_limitDownCount = 0;

		// 清空向量数据
		_vecIndexSH.clear();
		_vecIndexSZ.clear();
		_vecIndexGEM.clear();
		_vecIndexSTAR.clear();
		_vecIndexBJ.clear();
		_vecIndexSHA.clear();
		_vecIndexSZA.clear();
	}
};


// 预警信息结构
struct AlertInfo
{
	int nID;              // 编号
	CString strName;      // 股票名称
	CString strCode;      // 股票代码
	CString strReason;    // 预警原因
};

// 行业信息结构
struct IndustryData
{
	CString strType;       // 类型（行业/概念）
	CString strName;       // 名称
	CString strChange;     // 是否
	COLORREF clrChange;    // 是否颜色
};