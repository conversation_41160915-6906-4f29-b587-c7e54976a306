﻿/*************************************************************************
				Class Declaration : CUGCtrl
**************************************************************************
	Source file : UGCtrl.cpp
	Header file : UGCtrl.h
// This software along with its related components, documentation and files ("The Libraries")
// is ?1994-2007 The Code Project (1612916 Ontario Limited) and use of The Libraries is
// governed by a software license agreement ("Agreement").  Copies of the Agreement are
// available at The Code Project (www.codeproject.com), as part of the package you downloaded
// to obtain this file, or directly from our office.  For a copy of the license governing
// this software, you may contact <NAME_EMAIL>, or by calling 416-849-8900.

	Purpose
		This is the main grid's class.  It contains and
		controls all of the windows and classes that
		make up Ultimate Grid for MFC.

		This class is very important and the first class
		that any development with Ultimate Grid will encounter.
		
	Details
		- Generaly a class derived from this class
		  is used in applications. A derived class
		  is neccessary in order to handle any of the
		  notifications that the grid sends.
		- Being derived from CWnd allows the grid to be
		  used anywhere a CWnd can be used.
		- When this class is created, using CreateGrid or AttachGrid,
		  it also:
			- creates all grid's child windows
			  (CUGGrid, CUGTopHdg, CUGSideHdg, etc.)
			- creates a default memory manager datasource
			  and registers grid's standard cell types
*************************************************************************/
#ifndef _UGCtrl_H_
#define _UGCtrl_H_

// 网格功能开关宏定义 - 可以注释掉不需要的功能
// grid feature enable defines - rem out one or more of these defines 
// to remove one or more features
#define UG_ENABLE_MOUSEWHEEL     // 启用鼠标滚轮支持
#define UG_ENABLE_PRINTING       // 启用打印功能
#define UG_ENABLE_FINDDIALOG     // 启用查找对话框
#define UG_ENABLE_SCROLLHINTS    // 启用滚动提示

#ifdef __AFXOLE_H__  //OLE must be included
	#define UG_ENABLE_DRAGDROP   // 如果包含了OLE，则启用拖放功能
#endif

#ifndef WS_EX_LAYOUTRTL
	#define WS_EX_LAYOUTRTL		0x00400000L  // 从右到左布局扩展样式
#endif // WS_EX_LAYOUTRTL

// 标准网格类前向声明
//standard grid classes
class CUGGridInfo;      // 网格信息类
class CUGGrid;          // 主网格区域类
class CUGTopHdg;        // 顶部标题类
class CUGSideHdg;       // 侧边标题类
class CUGCnrBtn;        // 角落按钮类
class CUGVScroll;       // 垂直滚动条类
class CUGHScroll;       // 水平滚动条类
class CUGCtrl;          // 网格控制主类
class CUGCell;          // 单元格类
class CUGCellType;      // 单元格类型类
class CUGEdit;          // 编辑类
class CUGCellFormat;    // 单元格格式类

// 标准网格包含文件
//standard grid includes
#include "UGDefine.h"   // 常量和宏定义
#include "UGStruct.h"   // 数据结构定义
#include "UGPtrLst.h"   // 指针列表类
#include "UGDrwHnt.h"   // 绘制提示相关
#include "UGCell.h"     // 单元格类实现
#include "UGCnrBtn.h"   // 角落按钮实现
#include "UGGrid.h"     // 主网格实现
#include "UGHScrol.h"   // 水平滚动条实现
#include "UGSideHd.h"   // 侧边标题实现
#include "UGTopHdg.h"   // 顶部标题实现
#include "UGVScrol.h"   // 垂直滚动条实现
#include "UGDtaSrc.h"   // 数据源实现
#include "UGMemMan.h"   // 内存管理
#include "UGCelTyp.h"   // 单元格类型
#include "UGMultiS.h"   // 多选功能实现
#include "UGEditBase.h" // 编辑基类
#include "UGEdit.h"     // 编辑控件实现
#include "UGMEdit.h"    // 带掩码的编辑控件实现
#include "UGTab.h"      // 标签页实现
#include "UGGdInfo.h"   // 网格信息类实现
#include "UGFormat.h"   // 格式化实现
#include "UGXPThemes.h" // XP主题支持
#ifdef UG_ENABLE_SCROLLHINTS
	#include "ughint.h"  // 滚动提示实现（条件包含）
#endif 
#ifdef UG_ENABLE_PRINTING
	#include "UGPrint.h" // 打印功能实现（条件包含）
#endif


// 标准单元格类型
//standard cell types
#include "ugLstBox.h"    // 列表框单元格类型
#include "ugdltype.h"    // 下拉列表单元格类型
#include "ugcbtype.h"    // 复选框单元格类型
#include "ugctarrw.h"    // 箭头单元格类型

#ifdef UG_ENABLE_DRAGDROP
	#include "UGDrgDrp.h" // 拖放功能实现（条件包含）
#endif

/////////////////////////////////////////////////
// CUGCtrl类 - Ultimate Grid的主控制类
class UG_CLASS_DECL CUGCtrl : public CWnd
{
// Construction
public:
	CUGCtrl();


// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CUGCtrl)
	protected:
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	public:
	virtual CScrollBar* GetScrollBarCtrl(int nBar) const;
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CUGCtrl();

	// Generated message map functions
protected:
	//{{AFX_MSG(CUGCtrl)
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnDestroy();
	afx_msg void OnPaint();
	afx_msg void OnSetFocus(CWnd* pOldWnd);
	afx_msg void OnSysColorChange();
	afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg void OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg UINT OnGetDlgCode();
	afx_msg LRESULT OnCellTypeMessage(WPARAM, LPARAM);
	afx_msg BOOL OnEraseBkgnd( CDC* pDC );
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

public:
	// 设置初始单元格状态
	void SetInitialCellStates();

	int m_contructorResults;  // 构造函数结果代码

	// 重置网格的方法
	// Methods used to reset the grid
	void ResetCells(int startRow, int endRow, int startCol, int endCol);  // 重置特定区域的单元格
	void ResetAll(bool allSheets = true);  // 重置所有单元格（可选是否重置所有表格）
	void ResetSizes(int startRow, int endRow, int startCol, int endCol);  // 重置特定区域的单元格尺寸

protected:
	// 设置是否使用默认状态存储
	void UseDefaultStateStorage(bool use) { m_storeInitialStates = use; }

private:
	// 此方法将行列尺寸的初始状态保存在网格信息类中
	// This method stores the startup row and col sizes in the grid info class.
	void SetInitialSizes();
	bool m_storeInitialStates;  // 是否存储初始状态标志

protected:

	//***** 内部类 *****
	//***** internal classes *****
	
	// 数据源列表
	//data source list
	CUGDataSource		**m_dataSrcList;       // 数据源列表
	int					m_dataSrcListLength;   // 数据源列表长度
	int					m_dataSrcListMaxLength;  // 数据源列表最大长度

	CUGPtrList			*m_fontList;            // 字体列表
	CUGPtrList			*m_bitmapList;          // 位图列表
	CUGPtrList			*m_cellTypeList;        // 单元格类型列表
	CUGPtrList			*m_cellStylesList;      // 单元格样式列表
	CUGPtrList			*m_validateList;        // 验证列表
	CUGPtrList			*m_displayFormatList;   // 显示格式列表

	// 标准单元格类型
	//standard cell types
	CUGCellType			m_normalCellType;       // 普通单元格类型
	CUGDropListType		m_dropListType;         // 下拉列表类型
	CUGCheckBoxType		m_checkBoxType;         // 复选框类型
	CUGArrowType		m_arrowType;            // 箭头类型


	#ifdef UG_ENABLE_PRINTING
	CUGPrint*			m_CUGPrint;             // 打印对象指针
	#endif

	// 弹出菜单
	//popup menu
	CMenu				* m_menu;               // 弹出菜单
	int					m_menuCol;              // 菜单所在列
	long				m_menuRow;              // 菜单所在行
	int					m_menuSection;          // 菜单所在区域

	CUGCell m_cell;                              // 通用单元格


	// 网格信息列表/表格变量
	//grid info list / sheet variables
	CUGGridInfo ** m_GIList;                    // 网格信息列表
	int m_currentSheet;                         // 当前表格索引
	int m_numberSheets;                         // 表格数量


	// 更新启用/禁用标志
	//update enable/disable flag
	BOOL m_enableUpdate;                        // 是否允许更新


public:

	// 当前表格 
	//current sheet 
	CUGGridInfo * m_GI;                         // 当前活动表格信息指针

	// 子窗口类
	//child window classes
	CUGGrid				*m_CUGGrid;             // 主网格区域
	CUGTopHdg			*m_CUGTopHdg;           // 顶部标题
	CUGSideHdg			*m_CUGSideHdg;          // 侧边标题
	CUGCnrBtn			*m_CUGCnrBtn;           // 角落按钮

	CUGVScroll			*m_CUGVScroll;          // 垂直滚动条
	CUGHScroll			*m_CUGHScroll;          // 水平滚动条

	// 滚动提示窗口
	//scroll hint window
	#ifdef UG_ENABLE_SCROLLHINTS
	CUGHint				*m_CUGHint;             // 滚动提示窗口
	#endif
	
	// 标签页
	//tabs
	CUGTab				*m_CUGTab;              // 标签页
	// 标签页调整大小标志
	//tab sizing flag
	BOOL m_tabSizing;                           // 标签页大小调整标志

	// 顶层跟踪窗口
	//tracking topmost window
	CWnd				*m_trackingWnd;          // 跟踪窗口

	// 默认编辑控件
	//default edit control
	CUGEdit			m_defEditCtrl;              // 默认编辑控件
	CUGMaskedEdit	m_defMaskedEditCtrl;         // 默认带掩码的编辑控件

	// 编辑相关变量
	//editing
	BOOL	m_editInProgress;		//TRUE or FALSE  // 编辑进行中标志
	long	m_editRow;                           // 编辑行
	int		m_editCol;                           // 编辑列
	CWnd *	m_editCtrl;				//edit control currently being used  // 当前使用的编辑控件
	CUGCell m_editCell;                          // 编辑单元格
	CWnd *	m_editParent;                        // 编辑控件的父窗口

	//***** 其他变量 *****
	//***** other variables *****
	CPen m_threeDLightPen;                       // 3D亮色画笔
	CPen m_threeDDarkPen;                        // 3D暗色画笔


protected:

	//***** 内部函数 *****
	//***** internal functions *****
	void CalcTopRow();			//Calcs the max top row then adjusts the top row if needed
                                // 计算最大顶部行，并在需要时调整顶部行
	void CalcLeftCol();			//Calcs the max left col then adjusts the left col if needed
                                // 计算最大左侧列，并在需要时调整左侧列
	void AdjustTopRow();		//moves top row so that the current row is in view
                                // 移动顶部行，确保当前行可见
	void AdjustLeftCol();		//move the left col so that the current col is in view
                                // 移动左侧列，确保当前列可见
	void Update();				//updates all windows - also performs recalculations
                                // 更新所有窗口并重新计算
	void Moved();				//this function is called whenever a grid movement is made
								//this function then notifies the child windows
                                // 当网格移动时调用此函数，并通知子窗口
	
	BOOL CreateChildWindows();	//creates the child windows
                                // 创建子窗口
	void ToggleLayout( CWnd *pWnd );
                                // 切换布局方向
	void SetLockRowHeight();    // 设置锁定行高度
	void SetLockColWidth();     // 设置锁定列宽度

	void MoveTrackingWindow();  // 移动跟踪窗口

public:

	// 检查新位置是否有效
	//check to see if the new position is valid
	int VerifyTopRow(long* newRow);          // 验证新的顶部行是否有效
	int VerifyCurrentRow(long* newRow);      // 验证新的当前行是否有效
	int VerifyLeftCol(int* newCol);          // 验证新的左侧列是否有效
	int VerifyCurrentCol(int* newCol);       // 验证新的当前列是否有效


	//*************** 创建/设置 *****************
	//*************** creation/setup *****************
	// 窗口创建
	//window creation
	BOOL CreateGrid(DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID);  // 创建网格

	// 对话框资源函数
	//dialog resource functions
	BOOL AttachGrid(CWnd * wnd,UINT ID);     // 附加网格到对话框资源
	// v7.2 - update 01 - see note ugctrl.cpp
	BOOL DetachGrid();	                     // 分离网格


	void AdjustComponentSizes();	// 调整和定位子窗口

	//*************** 编辑功能 *****************
	//*************** editing *****************
	int		StartEdit();                     // 开始编辑当前单元格
	int		StartEdit(int key);              // 使用指定键开始编辑当前单元格
	int		StartEdit(int col,long row,int key);  // 使用指定键开始编辑指定单元格
	int		ContinueEdit(int adjustCol,long adjustRow);  // 调整位置后继续编辑

	//*************** 行和列操作 *****************
	//*************** row and column *****************
	int		SetNumberRows(long rows,BOOL redraw = TRUE);   // 设置行数
	long	GetNumberRows();                              // 获取行数
	
	int		SetNumberCols(int cols,BOOL redraw = TRUE);   // 设置列数
	int		GetNumberCols();                              // 获取列数

	int		SetDefColWidth(int width);                    // 设置默认列宽
	int		SetColWidth(int col,int width);               // 设置指定列宽
	int		GetColWidth(int col,int *width);              // 获取列宽
	int		GetColWidth(int col);                         // 获取列宽（重载版本）

	int		SetDefRowHeight(int height);                  // 设置默认行高
	int		SetUniformRowHeight(int flag);                // 设置统一行高
	int		SetRowHeight(long row,int height);            // 设置指定行高
	int		GetRowHeight(long row,int *height);           // 获取行高
	int		GetRowHeight(long row);                       // 获取行高（重载版本）

	int		GetCurrentCol();                              // 获取当前列
	long	GetCurrentRow();                              // 获取当前行
	int		GetLeftCol();                                 // 获取最左列
	long	GetTopRow();                                  // 获取顶部行
	int		GetRightCol();                                // 获取最右列
	long	GetBottomRow();                               // 获取底部行

	int		InsertCol(int col);                           // 插入列
	int		AppendCol();                                  // 追加列
	int		DeleteCol(int col);                           // 删除列
	int		InsertRow(long row);                          // 插入行
	int		AppendRow();                                  // 追加行
	int		DeleteRow(long row);                          // 删除行

	//************ 标题函数 *************
	//************ 标题函数 *************
	int		SetTH_NumberRows(int rows);                   // 设置顶部标题行数
	int		SetTH_RowHeight(int row,int height);          // 设置顶部标题行高
	int		SetSH_NumberCols(int cols);                   // 设置侧边标题列数
	int		SetSH_ColWidth(int col,int width);            // 设置侧边标题列宽


	//************* 查找/替换/排序 *********************
	//************* 查找/替换/排序 *********************
	int FindFirst(CString *string,int *col,long *row,long flags);  // 查找第一个匹配项
	int FindNext(CString *string,int *col,long *row,int flags);    // 查找下一个匹配项

	int FindDialog();                                    // 显示查找对话框
	int ReplaceDialog();                                 // 显示替换对话框
	// v7.2 - update 02 - 64-bit - was defined as long ProcessFindDialog(UINT,long);
	LRESULT ProcessFindDialog(WPARAM,LPARAM);            // 处理查找对话框消息
	int FindInAllCols(BOOL state);                       // 设置是否在所有列中查找
	BOOL m_findDialogRunning;                            // 查找对话框是否运行中
	BOOL m_findDialogStarted;                            // 查找对话框是否已启动
	BOOL m_findInAllCols;                                // 是否在所有列中查找
	CFindReplaceDialog *m_findReplaceDialog;             // 查找替换对话框指针


	int SortBy(int col,int flag = UG_SORT_ASCENDING);    // 按单列排序
	int SortBy(int *cols,int num,int flag = UG_SORT_ASCENDING);  // 按多列排序


	//************* 子窗口 *********************
	//************* child windows *********************
	int		SetTH_Height(int height);                    // 设置顶部标题高度
	int		GetTH_Height();                              // 获取顶部标题高度
	int		SetSH_Width(int width);                      // 设置侧边标题宽度
	int		GetSH_Width();                               // 获取侧边标题宽度
	int		SetVS_Width(int width);                      // 设置垂直滚动条宽度
	int		GetVS_Width();                               // 获取垂直滚动条宽度
	int		SetHS_Height(int height);                    // 设置水平滚动条高度
	int		GetHS_Height();                              // 获取水平滚动条高度

	//************* 默认/单元格信息 *********************
	//************* default/cell information *********************
	int		GetCell(int col,long row,CUGCell *cell);              // 获取单元格
	int		GetCellIndirect(int col,long row,CUGCell *cell);      // 间接获取单元格(触发OnGetCell通知)
	int		SetCell(int col,long row,CUGCell *cell);              // 设置单元格
	int		DeleteCell(int col,long row);                         // 删除单元格

	int		SetColDefault(int col,CUGCell *cell);                 // 设置列默认值
	int		GetColDefault(int col,CUGCell *cell);                 // 获取列默认值
	int		SetGridDefault(CUGCell *cell);                        // 设置网格默认值
	int		GetGridDefault(CUGCell *cell);                        // 获取网格默认值
	int		SetHeadingDefault(CUGCell *cell);                     // 设置标题默认值
	int		GetHeadingDefault(CUGCell *cell);                     // 获取标题默认值

	//列信息和转换
	//column information and translation
	int		GetColTranslation(int col);                           // 获取列转换
	int		SetColTranslation(int col,int transCol);              // 设置列转换


	//单元格合并
	//cell joining
	int		JoinCells(int startcol,long startrow,int endcol,long endrow);  // 合并单元格
	int		UnJoinCells(int col,long row);                                 // 取消单元格合并
	int		EnableJoins(BOOL state);                                       // 启用/禁用单元格合并功能

	//单元格函数 - 快速设置系列
	//cell functions
	int		QuickSetText(int col,long row,LPCTSTR string);            // 快速设置文本
	int		QuickSetNumber(int col,long row,double number);           // 快速设置数字
	int		QuickSetMask(int col,long row,LPCTSTR string);            // 快速设置掩码
	int		QuickSetLabelText(int col,long row,LPCTSTR string);       // 快速设置标签文本
	int		QuickSetTextColor(int col,long row,COLORREF color);       // 快速设置文本颜色
	int		QuickSetBackColor(int col,long row,COLORREF color);       // 快速设置背景颜色
	int		QuickSetAlignment(int col,long row,short align);          // 快速设置对齐方式
	int		QuickSetBorder(int col,long row,short border);            // 快速设置边框
	int		QuickSetBorderColor(int col,long row,CPen *pen);          // 快速设置边框颜色
	int		QuickSetFont(int col,long row,CFont * font);              // 快速设置字体
	int		QuickSetFont(int col,long row,int index);                 // 快速设置字体(索引)
	int		QuickSetBitmap(int col,long row,CBitmap * bitmap);        // 快速设置位图
	int		QuickSetBitmap(int col,long row,int index);               // 快速设置位图(索引)
	int		QuickSetCellType(int col,long row,long type);             // 快速设置单元格类型
	int		QuickSetCellTypeEx(int col,long row,long typeEx);         // 快速设置扩展单元格类型
	int		QuickSetHTextColor(int col,long row,COLORREF color);      // 快速设置高亮文本颜色
	int		QuickSetHBackColor(int col,long row,COLORREF color);      // 快速设置高亮背景颜色

	int		QuickSetRange(int startCol,long startRow,int endCol,long endRow,
				CUGCell* cell);                                        // 快速设置范围单元格

	int			QuickGetText(int col,long row,CString *string);       // 快速获取文本(保存到string)
	LPCTSTR		QuickGetText(int col,long row);                       // 快速获取文本(返回字符串指针)

	int		DuplicateCell(int destCol,long destRow, int srcCol, long srcRow);  // 复制单元格


	//************* 常规模式和设置 *********************
	//************* general modes and settings *********************
	int	SetCurrentCellMode(int mode);	//focus and highlighting
											//bit 1:focus 2:hightlight
                                            // 设置当前单元格模式(焦点和高亮)
                                            // 位1控制焦点，位2控制高亮
	int	SetHighlightRow(int mode, BOOL bExtend = TRUE);		//on,off
                                            // 设置行高亮模式，开/关
	int	SetMultiSelectMode(int mode);	//on,off
                                            // 设置多选模式，开/关
	int	Set3DHeight(int height);		//in pixels
                                            // 设置3D高度，单位像素
	int	SetPaintMode(int mode);			//on,off
                                            // 设置绘制模式，开/关
	int	GetPaintMode();                  // 获取绘制模式
	int	SetVScrollMode(int mode);		// 0-normal 2- tracking 3-joystick
                                            // 设置垂直滚动模式，0-正常 2-跟踪 3-游戏杆
	int	SetHScrollMode(int mode);		// 0-normal 2- tracking
                                            // 设置水平滚动模式，0-正常 2-跟踪
	
	int HideCurrentCell();               // 隐藏当前单元格

	int SetBallisticMode(int mode);	// 0:off  1:on  2:on-squared 3:on-cubed
                                        // 设置加速模式，0-关闭 1-开启 2-平方加速 3-立方加速
	int SetBallisticDelay(int milisec); // 设置加速延迟，单位毫秒

	int SetBallisticKeyMode(int mode); //0-off n-number of key repeats before increase
                                        // 设置键盘加速模式，0-关闭 n-增加前的按键重复次数
	int SetBallisticKeyDelay(int milisec); // 设置键盘加速延迟，单位毫秒
	
	int SetDoubleBufferMode(int mode);	// 0:off 1:on
                                        // 设置双缓冲模式，0-关闭 1-开启
	int SetGridLayout( int layoutMode );	// 0: Left-to-Right (default),
											// 1: Right-to-Left
                                            // 设置网格布局，0-从左到右(默认)，1-从右到左

	int LockColumns(int numCols);       // 锁定列数量
	int	LockRows(int numRows);          // 锁定行数量

	int	EnableCellOverLap(BOOL state);      // 启用/禁用单元格重叠
	int EnableColSwapping(BOOL state);      // 启用/禁用列交换
	int EnableExcelBorders(BOOL state);     // 启用/禁用Excel风格边框
	int EnableScrollOnParialCells( BOOL state ); // 启用/禁用部分单元格滚动


	//************* 移动 *********************
	//************* movement *********************
	int MoveTopRow(int flag);	//0-lineup 1-linedown 2-pageup 3-pagedown 4-top 5-bottom
                                // 移动顶部行
                                // 0-上一行 1-下一行 2-上一页 3-下一页 4-顶部 5-底部
	int AdjustTopRow(long adjust);   // 调整顶部行
	int MoveCurrentRow(int flag);    // 移动当前行
	int AdjustCurrentRow(long adjust); // 调整当前行
	int GotoRow(long row);           // 跳转到指定行
	int	SetTopRow(long row);         // 设置顶部行

	int MoveLeftCol(int flag);       // 移动左侧列
	int AdjustLeftCol(int adjust);   // 调整左侧列
	int MoveCurrentCol(int flag);    // 移动当前列
	int AdjustCurrentCol(int adjust); // 调整当前列
	int GotoCol(int col);            // 跳转到指定列
	int	SetLeftCol(int col);         // 设置左侧列

	int GotoCell(int col,long row);  // 跳转到指定单元格


	//************* 查找单元格 *********************
	//************* finding cells *********************
	int GetCellFromPoint(int x,int y,int *col,long *row);  // 从点坐标获取单元格
	int GetCellFromPoint(int x,int y,int *ptcol,long *ptrow,RECT *rect);  // 从点坐标获取单元格并返回矩形
	int GetAbsoluteCellFromPoint(int x,int y,int *ptcol,long *ptrow);  // 从点坐标获取绝对单元格位置

	int GetCellRect(int col,long row,RECT *rect);  // 获取单元格矩形区域
	int GetCellRect(int *col,long *row,RECT *rect);  // 获取单元格矩形区域（重载）
	int GetRangeRect(int startCol,long startRow,int endCol,long endRow,RECT *rect);  // 获取单元格范围矩形区域
	
	int GetJoinStartCell(int *col,long *row);  // 获取合并单元格的起始单元格
	int GetJoinStartCell(int *col,long *row,CUGCell *cell);  // 获取合并单元格的起始单元格并返回单元格对象
	int GetJoinRange(int *col,long *row,int *col2,long *row2);  // 获取合并单元格的范围


	//************* 单元格类型 *********************
	//************* cell types *********************
	long AddCellType(CUGCellType *);   //returns the cell type ID
                                        // 添加单元格类型，返回单元格类型ID
	int RemoveCellType(int ID);        // 移除单元格类型
	CUGCellType * GetCellType(int type);	//returns the pointer to a cell type
                                            // 获取单元格类型，返回指向单元格类型的指针
	int GetCellType(CUGCellType * type);    // 获取单元格类型ID

	//************* 数据源 *********************
	//************* data sources *********************
	int AddDataSource(CUGDataSource * ds);        // 添加数据源
	CUGDataSource * GetDataSource(int index);     // 获取数据源
	int RemoveDataSource(int index);              // 移除数据源
	int SetDefDataSource(int index);              // 设置默认数据源
	int GetDefDataSource();                       // 获取默认数据源
	int SetGridUsingDataSource(int index);        // 设置网格使用的数据源

	//************* 字体 *********************
	//************* fonts *********************
	int AddFont(LPCTSTR fontName,int height,int weight);  // 添加字体（基础参数）
	int AddFont(int height,int width,int escapement,int orientation, 
			int weight,BYTE italic,BYTE underline,BYTE strikeOut, 
			BYTE charSet,BYTE outputPrecision,BYTE clipPrecision, 
			BYTE quality,BYTE pitchAndFamily,LPCTSTR fontName);  // 添加字体（完整参数）
	int AddFontIndirect( LOGFONT lgFont );  // 通过LOGFONT结构添加字体
	int RemoveFont(int index);              // 移除字体
	int ClearAllFonts();                    // 清除所有字体
	CFont * GetFont(int index);             // 获取字体
	int SetDefFont(CFont *font);            // 设置默认字体
	int SetDefFont(int index);              // 设置默认字体（索引）

	//************* 位图 *********************
	//************* bitmaps *********************
	int AddBitmap( UINT resourceID,LPCTSTR resourceName= NULL);  // 添加位图（资源ID）
	int AddBitmap( LPCTSTR fileName);                            // 添加位图（文件名）
	int RemoveBitmap(int index);                                 // 移除位图
	int ClearAllBitmaps();                                       // 清除所有位图
	CBitmap* GetBitmap(int index);                               // 获取位图


	//************* 重绘 *********************
	//************* redrawing *********************
	int RedrawAll();                                              // 重绘所有
	int RedrawCell(int col,long row);                             // 重绘单元格
	int RedrawRow(long row);                                      // 重绘行
	int RedrawCol(int col);                                       // 重绘列
	int RedrawRange(int startCol,long startRow,int endCol,long endRow);  // 重绘范围
	void TempDisableFocusRect();                                  // 临时禁用焦点矩形


	//************* 多选 *********************
	//************* multi-select *********************
	int ClearSelections();                                        // 清除所有选择
	int Select(int col,long row);                                 // 选择单元格
	int SelectRange(int startCol,long startRow,int endCol,long endRow);  // 选择范围
	int EnumFirstSelected(int *col,long *row);                    // 枚举第一个选择的单元格
	int EnumNextSelected(int *col,long *row);                     // 枚举下一个选择的单元格
	int EnumFirstBlock(int *startCol,long *startRow,int *endCol,long *endRow);  // 枚举第一个选择区块
	int EnumNextBlock(int *startCol,long *startRow,int *endCol,long *endRow);   // 枚举下一个选择区块
	BOOL IsSelected(int col,long row,int *blockNum = NULL);                      // 检查单元格是否被选中

	//************* 剪贴板 ********************
	//************* clipboard ********************
	int CopySelected();                                           // 复制选中内容
	int CutSelected();                                            // 剪切选中内容
	int CopySelected(int cutflag);  //TRUE,FALSE                  // 复制选中内容（带剪切标志）
	int Paste();                                                  // 粘贴
	int Paste(CString &string);                                   // 粘贴指定文本
	int CopyToClipBoard(CString* string);                         // 复制到剪贴板
	int CopyFromClipBoard(CString* string);                       // 从剪贴板复制
	void CreateSelectedString(CString& string,int cutFlag);       // 创建选中内容的字符串


	//************* 列大小调整 ********************
	//************* column sizing ********************
	virtual int FitToWindow(int startCol,int endCol);             // 使列适应窗口
	virtual int BestFit(int startCol,int endCol,int CalcRange,int flag);  // 最佳拟合列宽


	//************* 打印 ********************
	//************* printing ********************
	#ifdef UG_ENABLE_PRINTING
		int PrintInit(CDC * pDC, CPrintDialog* pPD, int startCol,long startRow,
			int endCol,long endRow);                              // 初始化打印
		int PrintPage(CDC * pDC, int pageNum);                    // 打印页面
		int PrintSetMargin(int whichMargin,int size);             // 设置打印边距
		int PrintSetScale(int scalePercent);                      // 设置打印缩放
		int PrintSetOption(int option,long param);                // 设置打印选项
		int PrintGetOption(int option,long *param);               // 获取打印选项
	#endif


	//************* 提示 ********************
	//************* hints ********************
	int UseHints(BOOL state);                                     // 设置是否使用提示
	int UseVScrollHints(BOOL state);                              // 设置是否使用垂直滚动提示
	int UseHScrollHints(BOOL state);                              // 设置是否使用水平滚动提示


	//************* 弹出菜单 ********************
	//************* pop-up menu ********************
	CMenu * GetPopupMenu();                                       // 获取弹出菜单
	int EmptyMenu();                                              // 清空菜单
	int AddMenuItem(int ID,LPCTSTR string);                       // 添加菜单项
	int RemoveMenuItem(int ID);                                   // 移除菜单项
	int EnableMenu(BOOL state);                                   // 启用/禁用菜单


	//************* 拖放 ********************
	//************* drag and drop ********************
	#ifdef UG_ENABLE_DRAGDROP
		COleDataSource	m_dataSource;                             // OLE数据源
		CUGDropTarget	m_dropTarget;                             // 拖放目标

		int StartDragDrop();                                      // 开始拖放操作
		int DragDropTarget(BOOL state);                           // 设置拖放目标状态
	#endif


	//************* 标签页 ********************
	//************* tabs ********************
	int AddTab( CString label, long ID );                         // 添加标签页
	int InsertTab( int pos, CString label, long ID );             // 插入标签页
	int DeleteTab( long ID );                                     // 删除标签页
	int SetTabBackColor( long ID, COLORREF color );               // 设置标签页背景颜色
	int SetTabTextColor( long ID, COLORREF color );               // 设置标签页文本颜色
	int SetTabWidth( int width );                                 // 设置标签页宽度
	int SetCurrentTab( long ID );                                 // 设置当前标签页
	int GetCurrentTab();                                          // 获取当前标签页


	//************* 表格 ********************
	//************* sheets ********************
	int SetNumberSheets(int numSheets);                           // 设置表格数量
	int GetNumberSheets();                                        // 获取表格数量
	int SetSheetNumber(int index,BOOL update = TRUE);             // 设置当前表格编号
	int GetSheetNumber();                                         // 获取当前表格编号

	//****************************************************
	//*********** 可重写的通知函数 **********
	//*********** Over-ridable Notify Functions **********
	//****************************************************
	virtual void OnSetup();                                          // 网格设置完成通知
	virtual void OnReset();                                          // 网格重置通知
	virtual void OnSheetSetup(int sheetNumber);                      // 表格设置通知

	//移动和大小调整
	//movement and sizing
	virtual int  OnCanMove(int oldcol,long oldrow,int newcol,long newrow);  // 是否允许移动
	virtual int  OnCanViewMove(int oldcol,long oldrow,int newcol,long newrow); // 是否允许视图移动
	virtual void OnHitBottom(long numrows,long rowspast,long rowsfound);    // 到达底部通知
	virtual void OnHitTop(long numrows,long rowspast);                      // 到达顶部通知
	
	virtual int  OnCanSizeCol(int col);                              // 是否允许调整列大小
	virtual void OnColSizing(int col,int *width);                    // 列大小调整中通知
	virtual void OnColSized(int col,int *width);                     // 列大小调整完成通知
	virtual int  OnCanSizeRow(long row);                             // 是否允许调整行大小
	virtual void OnRowSizing(long row,int *height);                  // 行大小调整中通知
	virtual void OnRowSized(long row,int *height);                   // 行大小调整完成通知

	virtual int  OnCanSizeTopHdg();                                  // 是否允许调整顶部标题大小
	virtual int  OnCanSizeSideHdg();                                 // 是否允许调整侧边标题大小
	virtual int  OnTopHdgSizing(int *height);                        // 顶部标题大小调整中通知
	virtual int  OnSideHdgSizing(int *width);                        // 侧边标题大小调整中通知
	virtual int  OnTopHdgSized(int *height);                         // 顶部标题大小调整完成通知
	virtual int  OnSideHdgSized(int *width);                         // 侧边标题大小调整完成通知
	
	virtual void OnColChange(int oldcol,int newcol);                 // 列改变通知
	virtual void OnRowChange(long oldrow,long newrow);               // 行改变通知
	virtual void OnCellChange(int oldcol,int newcol,long oldrow,long newrow); // 单元格改变通知
	virtual void OnLeftColChange(int oldcol,int newcol);             // 左侧列改变通知
	virtual void OnTopRowChange(long oldrow,long newrow);            // 顶部行改变通知
	virtual void OnViewMoved( int nScrolDir, long oldPos, long newPos ); // 视图移动通知
	virtual void OnSelectionChanged(int startCol,long startRow,int endCol,long endRow,int blockNum); // 选择改变通知

	//鼠标和按键事件
	//mouse and key strokes
	virtual void OnLClicked(int col,long row,int updn,RECT *rect,POINT *point,BOOL processed); // 左键点击通知
	virtual void OnRClicked(int col,long row,int updn,RECT *rect,POINT *point,BOOL processed); // 右键点击通知
	virtual void OnDClicked(int col,long row,RECT *rect,POINT *point,BOOL processed); // 双击通知
	virtual void OnMouseMove(int col,long row,POINT *point,UINT nFlags,BOOL processed=0); // 鼠标移动通知
	virtual void OnTH_LClicked(int col,long row,int updn,RECT *rect,POINT *point,BOOL processed=0); // 顶部标题左键点击
	virtual void OnTH_RClicked(int col,long row,int updn,RECT *rect,POINT *point,BOOL processed=0); // 顶部标题右键点击
	virtual void OnTH_DClicked(int col,long row,RECT *rect,POINT *point,BOOL processed=0); // 顶部标题双击
	virtual void OnSH_LClicked(int col,long row,int updn,RECT *rect,POINT *point,BOOL processed=0); // 侧边标题左键点击
	virtual void OnSH_RClicked(int col,long row,int updn,RECT *rect,POINT *point,BOOL processed=0); // 侧边标题右键点击
	virtual void OnSH_DClicked(int col,long row,RECT *rect,POINT *point,BOOL processed=0); // 侧边标题双击
	virtual void OnCB_LClicked(int updn,RECT *rect,POINT *point,BOOL processed=0); // 角落按钮左键点击
	virtual void OnCB_RClicked(int updn,RECT *rect,POINT *point,BOOL processed=0); // 角落按钮右键点击
	virtual void OnCB_DClicked(RECT *rect,POINT *point,BOOL processed=0); // 角落按钮双击
	
	virtual void OnKeyDown(UINT *vcKey,BOOL processed); // 键盘按下通知
	virtual void OnKeyUp(UINT *vcKey,BOOL processed);   // 键盘释放通知
	virtual void OnCharDown(UINT *vcKey,BOOL processed); // 字符输入通知
	
	//GetCellIndirect通知
	//GetCellIndirect notification
	virtual void OnGetCell(int col,long row,CUGCell *cell); // 获取单元格通知

	//SetCell通知
	//SetCell notification
	virtual void OnSetCell(int col,long row,CUGCell *cell); // 设置单元格通知
	
	//数据源通知
	//data source notifications
	virtual void OnDataSourceNotify(int ID,long msg,long param); // 数据源通知

	//单元格类型通知
	//cell type notifications
	virtual int OnCellTypeNotify(long ID,int col,long row,long msg,LONG_PTR param); // 单元格类型通知

	//编辑相关通知
	//editing
	virtual int OnEditStart(int col, long row,CWnd **edit); // 开始编辑通知
	virtual int OnEditVerify(int col,long row,CWnd *edit,UINT *vcKey); // 编辑验证通知
	virtual int OnEditFinish(int col, long row,CWnd *edit,LPCTSTR string,BOOL cancelFlag); // 编辑完成通知
	virtual int OnEditContinue(int oldcol,long oldrow,int* newcol,long* newrow); // 编辑继续通知
	virtual int OnEditKeyDown(int col,long row,CWnd *edit,UINT *vcKey); // 编辑时键盘按下通知
	virtual int OnEditKeyUp(int col,long row,CWnd *edit,UINT *vcKey); // 编辑时键盘释放通知

	//菜单通知
	//menu notifications
	virtual void OnMenuCommand(int col,long row,int section,int item); // 菜单命令通知
	virtual int  OnMenuStart(int col,long row,int section); // 菜单开始通知

	// 上下文帮助 
	// Context help 
	virtual DWORD OnGetContextHelpID( int col, long row, int section ); // 获取上下文帮助ID

	//提示通知
	//hints
	virtual int OnHint(int col,long row,int section,CString *string); // 提示通知
	virtual int OnVScrollHint(long row,CString *string); // 垂直滚动提示通知
	virtual int OnHScrollHint(int col,CString *string); // 水平滚动提示通知

	//拖放通知
	//drag and drop
	#ifdef UG_ENABLE_DRAGDROP
		virtual DROPEFFECT OnDragEnter(COleDataObject* pDataObject); // 拖动进入通知
		virtual DROPEFFECT OnDragOver(COleDataObject* pDataObject,int col,long row); // 拖动经过通知
		virtual DROPEFFECT OnDragDrop(COleDataObject* pDataObject,int col,long row); // 拖放通知
	#endif

	//排序通知
	//sorting
	virtual int  OnSortEvaluate(CUGCell *cell1,CUGCell *cell2,int flags); // 排序评估通知
	
	//DC设置
	//DC setup
	virtual void OnScreenDCSetup(CDC *dc,CDC *db_dc,int section); // 屏幕DC设置通知
	
	virtual void OnAdjustComponentSizes(RECT *grid,RECT *topHdg,RECT *sideHdg,
		RECT *cnrBtn,RECT *vScroll,RECT *hScroll,RECT *tabs); // 调整组件大小通知

	virtual void OnTabSelected(int ID); // 标签页选择通知

	virtual COLORREF OnGetDefBackColor(int section); // 获取默认背景颜色通知

	//焦点矩形设置
	//focus rect setup
	virtual void OnDrawFocusRect(CDC *dc,RECT *rect); // 绘制焦点矩形通知

	virtual void OnSetFocus(int section); // 获得焦点通知
	virtual void OnKillFocus(int section); // 失去焦点通知
	virtual void OnKillFocus(int section, CWnd *pNewWnd); // 失去焦点通知（指定新窗口）
	
	void DrawExcelFocusRect(CDC *dc,RECT *rect);  // 绘制Excel风格的焦点矩形
		
	int StartMenu(int col,long row,POINT *point,int section);  // 启动菜单

	int SetArrowCursor(HCURSOR cursor);         // 设置箭头光标
	int SetWESizingCursor(HCURSOR cursor);      // 设置水平调整大小光标
	int SetNSSizingCursor(HCURSOR cursor);      // 设置垂直调整大小光标

	int SetMargin(int pixels);                  // 设置边距

	//由网格编辑控件调用的函数
	//functions to be called by grid edit controls
	int EditCtrlFinished(LPCTSTR string,BOOL cancelFlag,
		BOOL continueFlag,int continueCol,long continueRow);  // 编辑控件完成时调用

	int EditCancel();                           // 取消编辑

	int SetCancelMode( BOOL bCancel );          // 设置取消模式
	BOOL GetCancelMode();                       // 获取取消模式

	CUGCellType * GetCellType(int col,long row);  // 获取单元格类型

	virtual BOOL OnColSwapStart(int col);         // 列交换开始通知
	virtual BOOL OnCanColSwap(int fromCol,int toCol);  // 是否允许列交换通知
	virtual void OnColSwapped(int fromCol,int toCol);  // 列交换完成通知

	int MoveColPosition(int fromCol,int toCol,BOOL insertBefore);  // 移动列位置

	int SetNewTopHeadingClass(CUGTopHdg * topHeading);  // 设置新的顶部标题类
	int SetNewSideHeadingClass(CUGSideHdg * sideHeading);  // 设置新的侧边标题类
	int SetNewGridClass(CUGGrid * grid);                   // 设置新的网格类
	int SetNewMultiSelectClass(CUGMultiSelect * multiSelect);  // 设置新的多选类
	int SetNewTabClass(CUGTab * tab);                      // 设置新的标签页类
	int SetNewVScrollClass(CUGVScroll * scroll);           // 设置新的垂直滚动条类
	int SetNewHScrollClass(CUGHScroll * scroll);           // 设置新的水平滚动条类


	int SetNewEditClass(CWnd * edit);                      // 设置新的编辑类
	int SetNewMaskedEditClass(CWnd * edit);                // 设置新的带掩码的编辑类
	CWnd * GetEditClass();                                 // 获取编辑类
	CWnd * GetMaskedEditClass();                           // 获取带掩码的编辑类

	//跟踪窗口
	//tracking window
	int SetTrackingWindow(CWnd *wnd);                      // 设置跟踪窗口
	int SetTrackingWindowMode(int mode);                   // 设置跟踪窗口模式
	virtual void OnTrackingWindowMoved(RECT *origRect,RECT *newRect);  // 跟踪窗口移动通知

	int EnableUpdate(BOOL state);                          // 启用/禁用更新

	int SetUserSizingMode(int state);                      // 设置用户调整大小模式

	int SetColDataSource(int col,int dataSrcIndex);        // 设置列数据源（索引）
	int SetColDataSource(int col,CUGDataSource * dataSrc);  // 设置列数据源（指针）

	static BOOL CALLBACK ModifyDlgItemText(HWND hWnd, LPARAM lParam);  // 修改对话框项文本的回调

	// 此函数返回m_CUGPrint，用于打印单元格
	// This function returns the m_CUGPrint.
	// It is used in printing cells.
	#ifdef UG_ENABLE_PRINTING
	inline	CUGPrint* GetUGPrint (void) {
		return m_CUGPrint;
	}
	#endif
};

#endif // _UGCtrl_H_
