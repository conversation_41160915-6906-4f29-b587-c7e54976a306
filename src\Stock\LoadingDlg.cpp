﻿#include "pch.h"
#include "Stock.h"
#include "LoadingDlg.h"
#include "afxdialogex.h"

// CLoadingDlg 对话框实现

IMPLEMENT_DYNAMIC(CLoadingDlg, CDialog)

BEGIN_MESSAGE_MAP(CLoadingDlg, CDialog)
END_MESSAGE_MAP()

CLoadingDlg::CLoadingDlg(CWnd* pParent /*=NULL*/)
    : CDialog(CLoadingDlg::IDD, pParent)
    , m_nProgress(0)
    , m_strStatus(_T("正在加载..."))
{
}

CLoadingDlg::~CLoadingDlg()
{
}

void CLoadingDlg::DoDataExchange(CDataExchange* pDX)
{
    CDialog::DoDataExchange(pDX);
    DDX_Control(pDX, IDC_PROGRESS_LOADING, m_ctrlProgress);
    DDX_Control(pDX, IDC_STATIC_STATUS, m_staticStatus);
}

BOOL CLoadingDlg::OnInitDialog()
{
    CDialog::OnInitDialog();

    // 设置进度条范围为0-100
    m_ctrlProgress.SetRange(0, 100);
    m_ctrlProgress.SetPos(m_nProgress);
    
    // 设置初始状态文本
    m_staticStatus.SetWindowText(m_strStatus);
    
    // 设置对话框标题
    SetWindowText(_T("正在加载股票数据"));
    
    // 居中显示
    CenterWindow();
    
    return TRUE;
}

void CLoadingDlg::PostNcDestroy()
{
    CDialog::PostNcDestroy();
    
    // 非模态对话框需要自己销毁
    delete this;
}

// 设置进度值
void CLoadingDlg::SetProgress(int nProgress)
{
    m_nProgress = nProgress;
    if (::IsWindow(m_ctrlProgress.GetSafeHwnd()))
    {
        m_ctrlProgress.SetPos(m_nProgress);
        
        // 立即更新UI
        m_ctrlProgress.UpdateWindow();
    }

    // 处理消息队列，保持UI响应
    MSG msg;
    while (::PeekMessage(&msg, NULL, 0, 0, PM_REMOVE))
    {
        ::TranslateMessage(&msg);
        ::DispatchMessage(&msg);
    }
}

// 设置状态文本
void CLoadingDlg::SetStatusText(LPCTSTR lpszText)
{
    m_strStatus = lpszText;
    if (::IsWindow(m_staticStatus.GetSafeHwnd()))
    {
        m_staticStatus.SetWindowText(m_strStatus);
        
        // 立即更新UI
        m_staticStatus.UpdateWindow();
    }
}

// 创建并显示对话框
BOOL CLoadingDlg::Create(CWnd* pParentWnd)
{
    // 使用正确的父窗口创建对话框
    BOOL bResult = CDialog::Create(IDD, pParentWnd);
    
    // 确保对话框在前台显示
    if (bResult)
    {
        // 设置对话框样式，确保在任务栏显示
        ModifyStyle(0, WS_VISIBLE | WS_CAPTION | DS_MODALFRAME);
        
        // 确保它在前台显示
        BringWindowToTop();
    }
    
    return bResult;
}

// 销毁对话框
BOOL CLoadingDlg::DestroyWindow()
{
    if (::IsWindow(GetSafeHwnd()))
    {
        return CDialog::DestroyWindow();
    }
    return TRUE;
} 