﻿#include "pch.h"
#include "..\Stock.h"
#include "SymbolBar.h"

IMPLEMENT_DYNCREATE(CSymbolBar, CView)

BEGIN_MESSAGE_MAP(CSymbolBar, CView)
	ON_WM_PAINT()
	ON_WM_ERASEBKGND()
	ON_WM_LBUTTONDOWN()
	ON_WM_MOUSEMOVE()
	ON_WM_LBUTTONUP()
	ON_WM_SIZE()
END_MESSAGE_MAP()

// 构造函数
CSymbolBar::CSymbolBar()
{
	// 初始化按钮文本
	m_buttons[BTN_FAVORITE].text = _T("自选股");
	m_buttons[BTN_LIMIT_UP].text = _T("涨停个股");
	m_buttons[BTN_LIMIT_DOWN].text = _T("跌停个股");
	m_buttons[BTN_NEW_HIGH].text = _T("近期新高");
	m_buttons[BTN_NEW_LOW].text = _T("近期新低");
	m_buttons[BTN_STRONG].text = _T("近期强势");

	// 初始化复选框文本
	m_checkBoxes[CHK_SH_A].text = _T("上证A");
	m_checkBoxes[CHK_SZ_A].text = _T("深证A");
	m_checkBoxes[CHK_GEM].text = _T("创业板");
	m_checkBoxes[CHK_STAR].text = _T("科创板");
	m_checkBoxes[CHK_BJ_A].text = _T("北证A");

	// 初始化按钮状态
	for (int i = 0; i < BTN_COUNT; i++) {
		m_buttons[i].isHover = false;
		m_buttons[i].isPressed = false;
		m_buttons[i].isSelected = false;
	}

	// 初始化复选框状态
	for (int i = 0; i < CHK_COUNT; i++) {
		m_checkBoxes[i].isHover = false;
		m_checkBoxes[i].isChecked = false;
	}
	
	// 默认选中上证A股复选框
	m_checkBoxes[CHK_SH_A].isChecked = true;
	m_dwCheckBoxState = 1 << CHK_SH_A;

	// 默认选中第一个按钮
	m_buttons[BTN_FAVORITE].isSelected = true;
	m_nSelectedButton = BTN_FAVORITE;

	// 初始化鼠标相关
	m_nHoverButton = -1;
	m_nPressedButton = -1;
	m_nHoverCheckBox = -1;
	m_nPressedCheckBox = -1;

	// 初始化回调函数
	m_pCallback = NULL;
	m_lParam = 0;
	m_pCheckBoxCallback = NULL;
	m_lCheckBoxParam = 0;

	// 初始化视觉样式
	m_clrBackground = RGB(0, 0, 0);               // 黑色背景
	m_clrText = RGB(200, 200, 200);               // 浅灰色文字
	m_clrHover = RGB(50, 50, 50);                 // 悬停深灰色
	m_clrPressed = RGB(70, 70, 70);               // 按下更深灰色
	m_clrSelected = RGB(0, 122, 204);             // 选中蓝色
	m_clrBorder = RGB(40, 40, 40);                // 边框深灰色
	m_clrCheckBox = RGB(120, 120, 120);           // 复选框灰色
	m_clrChecked = RGB(0, 160, 0);                // 复选框选中绿色
	m_nButtonWidth = 100;                         // 按钮宽度100像素
	m_nButtonHeight = 30;                         // 按钮高度30像素
	m_nButtonMargin = 0;                          // 按钮之间没有间距
	m_nCheckBoxWidth = 80;                        // 复选框宽度80像素
	m_nCheckBoxHeight = 30;                       // 复选框高度30像素
	m_nCheckBoxMargin = 5;                        // 复选框间距5像素
	m_nCheckBoxSize = 12;                         // 复选框选择框大小12像素
}

CSymbolBar::~CSymbolBar()
{
}

BOOL CSymbolBar::PreCreateWindow(CREATESTRUCT& cs)
{
	// 无边框窗口
	cs.style &= ~WS_BORDER;
	cs.dwExStyle &= ~WS_EX_CLIENTEDGE;
	
	return CView::PreCreateWindow(cs);
}

// 绘制函数
void CSymbolBar::OnDraw(CDC* pDC)
{
	// 获取客户区大小
	CRect rcClient;
	GetClientRect(rcClient);
	
	// 创建内存DC和位图用于双缓冲
	CDC memDC;
	CBitmap memBitmap;
	CBitmap* pOldBitmap = NULL;
	
	// 初始化内存DC
	if (memDC.CreateCompatibleDC(pDC))
	{
		// 创建兼容位图
		if (memBitmap.CreateCompatibleBitmap(pDC, rcClient.Width(), rcClient.Height()))
		{
			// 选入位图
			pOldBitmap = memDC.SelectObject(&memBitmap);
			
			// 填充背景
			memDC.FillSolidRect(rcClient, m_clrBackground);
			
			// 绘制所有按钮
			for (int i = 0; i < BTN_COUNT; i++) {
				DrawButton(&memDC, i);
			}
			
			// 绘制所有复选框
			for (int i = 0; i < CHK_COUNT; i++) {
				DrawCheckBox(&memDC, i);
			}
			
			// 将内存DC的内容拷贝到屏幕DC
			pDC->BitBlt(0, 0, rcClient.Width(), rcClient.Height(), &memDC, 0, 0, SRCCOPY);
			
			// 清理
			memDC.SelectObject(pOldBitmap);
			memBitmap.DeleteObject();
		}
		
		// 删除内存DC
		memDC.DeleteDC();
	}
}

void CSymbolBar::OnPaint()
{
	// 使用CPaintDC处理WM_PAINT消息
	CPaintDC dc(this);
	
	// 调用OnDraw进行双缓冲绘制
	OnDraw(&dc);
}

BOOL CSymbolBar::OnEraseBkgnd(CDC* pDC)
{
	return TRUE; // 在OnDraw中绘制背景
}

// 绘制单个按钮
void CSymbolBar::DrawButton(CDC* pDC, int nIndex)
{
	if (nIndex < 0 || nIndex >= BTN_COUNT)
		return;

	ButtonInfo& btn = m_buttons[nIndex];
	CRect rcButton = btn.rect;

	// 选择背景色
	COLORREF clrBackground = m_clrBackground;
	if (btn.isSelected)
		clrBackground = m_clrSelected;
	else if (btn.isPressed)
		clrBackground = m_clrPressed;
	else if (btn.isHover)
		clrBackground = m_clrHover;

	// 绘制按钮背景
	pDC->FillSolidRect(rcButton, clrBackground);

	// 绘制按钮边框
	if (btn.isHover || btn.isPressed || btn.isSelected) {
		CPen pen(PS_SOLID, 1, m_clrBorder);
		CPen* pOldPen = pDC->SelectObject(&pen);
		pDC->MoveTo(rcButton.left, rcButton.bottom - 1);
		pDC->LineTo(rcButton.right, rcButton.bottom - 1);
		pDC->SelectObject(pOldPen);
	}

	// 绘制按钮文本
	pDC->SetBkMode(TRANSPARENT);
	pDC->SetTextColor(m_clrText);
	
	// 创建字体
	CFont font;
	font.CreateFont(22, 0, 0, 0, FW_MEDIUM, FALSE, FALSE, 0,
		DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
		DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&font);
	
	// 绘制文本，水平和垂直居中对齐
	pDC->DrawText(btn.text, rcButton, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	
	pDC->SelectObject(pOldFont);
}

// 绘制单个复选框
void CSymbolBar::DrawCheckBox(CDC* pDC, int nIndex)
{
	if (nIndex < 0 || nIndex >= CHK_COUNT)
		return;

	CheckBoxInfo& chk = m_checkBoxes[nIndex];
	CRect rcCheckBox = chk.rect;

	// 选择背景色
	COLORREF clrBackground = m_clrBackground;
	if (chk.isHover)
		clrBackground = m_clrHover;

	// 绘制复选框背景
	pDC->FillSolidRect(rcCheckBox, clrBackground);

	// 创建字体
	CFont font;
	font.CreateFont(22, 0, 0, 0, FW_MEDIUM, FALSE, FALSE, 0,
		DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
		DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&font);
	
	// 绘制复选框文本
	pDC->SetBkMode(TRANSPARENT);
	pDC->SetTextColor(m_clrText);
	
	// 文本区域，留出左侧复选框区域
	CRect rcText = rcCheckBox;
	rcText.left += m_nCheckBoxSize + 5; // 留出复选框和间距
	
	// 绘制文本，左对齐并垂直居中
	pDC->DrawText(chk.text, rcText, DT_LEFT | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制复选框
	CRect rcCheck(
		rcCheckBox.left + 2,
		rcCheckBox.top + (rcCheckBox.Height() - m_nCheckBoxSize) / 2,
		rcCheckBox.left + 2 + m_nCheckBoxSize,
		rcCheckBox.top + (rcCheckBox.Height() - m_nCheckBoxSize) / 2 + m_nCheckBoxSize
	);
	
	// 绘制外框
	CPen pen(PS_SOLID, 1, m_clrCheckBox);
	CPen* pOldPen = pDC->SelectObject(&pen);
	pDC->Rectangle(rcCheck);
	
	// 如果选中，绘制选中标记
	if (chk.isChecked) {
		// 绘制选中标记 (对勾)
		CPen checkPen(PS_SOLID, 2, m_clrChecked);
		pDC->SelectObject(&checkPen);
		
		// 绘制对勾的左下部分
		pDC->MoveTo(rcCheck.left + 2, rcCheck.top + m_nCheckBoxSize / 2);
		pDC->LineTo(rcCheck.left + m_nCheckBoxSize / 3, rcCheck.bottom - 2);
		
		// 绘制对勾的右上部分
		pDC->LineTo(rcCheck.right - 2, rcCheck.top + 2);
	}
	
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
}

// 更新按钮位置
void CSymbolBar::UpdateButtonRects()
{
	CRect rcClient;
	GetClientRect(rcClient);

	int x = 0;
	int y = 0;
	int height = rcClient.Height();  // 按钮高度与导航条高度相同

	// 计算左侧按钮
	for (int i = 0; i < BTN_COUNT; i++) {
		m_buttons[i].rect = CRect(x, y, x + m_nButtonWidth, y + height);
		x += m_nButtonWidth + m_nButtonMargin;
	}
	
	// 更新复选框位置
	UpdateCheckBoxRects();
}

// 更新复选框位置
void CSymbolBar::UpdateCheckBoxRects()
{
	CRect rcClient;
	GetClientRect(rcClient);

	int height = rcClient.Height();  // 复选框高度与导航条高度相同
	int x = rcClient.right;          // 从右侧开始排列
	int y = 0;

	// 从右向左排列复选框
	for (int i = CHK_COUNT - 1; i >= 0; i--) {
		x -= m_nCheckBoxWidth;
		m_checkBoxes[i].rect = CRect(x, y, x + m_nCheckBoxWidth, y + height);
		x -= m_nCheckBoxMargin;  // 添加间距
	}
}

// 判断点击的按钮
int CSymbolBar::HitTestButton(CPoint point)
{
	for (int i = 0; i < BTN_COUNT; i++) {
		if (m_buttons[i].rect.PtInRect(point)) {
			return i;
		}
	}
	return -1;
}

// 判断点击的复选框
int CSymbolBar::HitTestCheckBox(CPoint point)
{
	for (int i = 0; i < CHK_COUNT; i++) {
		if (m_checkBoxes[i].rect.PtInRect(point)) {
			return i;
		}
	}
	return -1;
}

// 获取复选框状态
bool CSymbolBar::GetCheckBoxState(int nCheckBoxID) const
{
	if (nCheckBoxID >= 0 && nCheckBoxID < CHK_COUNT) {
		return m_checkBoxes[nCheckBoxID].isChecked;
	}
	return false;
}

// 消息处理

void CSymbolBar::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);
	
	// 更新按钮和复选框的位置
	UpdateButtonRects();
	
	// 使用InvalidateRect而不是Invalidate，避免不必要的背景擦除
	InvalidateRect(NULL, FALSE);
}

void CSymbolBar::OnLButtonDown(UINT nFlags, CPoint point)
{
	// 检查是否点击了按钮
	int buttonIndex = HitTestButton(point);
	if (buttonIndex >= 0) {
		m_nPressedButton = buttonIndex;
		m_buttons[buttonIndex].isPressed = true;
		
		// 仅重绘按钮区域
		InvalidateRect(m_buttons[buttonIndex].rect, FALSE);
	}
	
	// 检查是否点击了复选框
	int checkBoxIndex = HitTestCheckBox(point);
	if (checkBoxIndex >= 0) {
		m_nPressedCheckBox = checkBoxIndex;
		
		// 仅重绘复选框区域
		InvalidateRect(m_checkBoxes[checkBoxIndex].rect, FALSE);
	}
	
	CView::OnLButtonDown(nFlags, point);
}

void CSymbolBar::OnMouseMove(UINT nFlags, CPoint point)
{
	// 更新按钮悬停状态
	int buttonIndex = HitTestButton(point);
	if (buttonIndex != m_nHoverButton) {
		// 需要重绘的区域
		CRect rcInvalidate;
		
		// 清除旧的悬停状态
		if (m_nHoverButton >= 0) {
			m_buttons[m_nHoverButton].isHover = false;
			rcInvalidate.UnionRect(rcInvalidate, m_buttons[m_nHoverButton].rect);
		}
		
		// 设置新的悬停状态
		m_nHoverButton = buttonIndex;
		if (m_nHoverButton >= 0) {
			m_buttons[m_nHoverButton].isHover = true;
			rcInvalidate.UnionRect(rcInvalidate, m_buttons[m_nHoverButton].rect);
		}
		
		// 仅重绘需要更新的区域
		if (!rcInvalidate.IsRectEmpty()) {
			InvalidateRect(rcInvalidate, FALSE);
		}
	}
	
	// 更新复选框悬停状态
	int checkBoxIndex = HitTestCheckBox(point);
	if (checkBoxIndex != m_nHoverCheckBox) {
		// 需要重绘的区域
		CRect rcInvalidate;
		
		// 清除旧的悬停状态
		if (m_nHoverCheckBox >= 0) {
			m_checkBoxes[m_nHoverCheckBox].isHover = false;
			rcInvalidate.UnionRect(rcInvalidate, m_checkBoxes[m_nHoverCheckBox].rect);
		}
		
		// 设置新的悬停状态
		m_nHoverCheckBox = checkBoxIndex;
		if (m_nHoverCheckBox >= 0) {
			m_checkBoxes[m_nHoverCheckBox].isHover = true;
			rcInvalidate.UnionRect(rcInvalidate, m_checkBoxes[m_nHoverCheckBox].rect);
		}
		
		// 仅重绘需要更新的区域
		if (!rcInvalidate.IsRectEmpty()) {
			InvalidateRect(rcInvalidate, FALSE);
		}
	}
	
	CView::OnMouseMove(nFlags, point);
}

void CSymbolBar::OnLButtonUp(UINT nFlags, CPoint point)
{
	// 处理按钮点击
	int buttonIndex = HitTestButton(point);
	bool needFullInvalidate = false;
	
	// 如果鼠标释放时仍在按下的按钮上，触发点击事件
	if (buttonIndex >= 0 && buttonIndex == m_nPressedButton) {
		// 如果是切换按钮选中状态，可能需要重绘所有按钮
		if (buttonIndex != m_nSelectedButton) {
			needFullInvalidate = true;
		}
		
		// 清除所有按钮的选中状态
		for (int i = 0; i < BTN_COUNT; i++) {
			m_buttons[i].isSelected = false;
		}
		
		// 设置新的选中按钮
		m_buttons[buttonIndex].isSelected = true;
		m_nSelectedButton = buttonIndex;
		
		// 调用回调函数
		if (m_pCallback) {
			m_pCallback(buttonIndex, m_lParam);
		}
	}
	
	// 记录需要重绘的按钮区域
	CRect rcButtonInvalidate;
	
	// 清除按钮按下状态
	if (m_nPressedButton >= 0) {
		m_buttons[m_nPressedButton].isPressed = false;
		rcButtonInvalidate = m_buttons[m_nPressedButton].rect;
		m_nPressedButton = -1;
	}
	
	// 处理复选框点击
	int checkBoxIndex = HitTestCheckBox(point);
	
	// 如果鼠标释放时仍在按下的复选框上，改变其状态
	if (checkBoxIndex >= 0 && checkBoxIndex == m_nPressedCheckBox) {
		// 切换该选项的状态
		m_checkBoxes[checkBoxIndex].isChecked = !m_checkBoxes[checkBoxIndex].isChecked;
		
		// 计算当前选中的复选框状态
		DWORD dwCheckState = 0;
		for (int i = 0; i < CHK_COUNT; i++) {
			if (m_checkBoxes[i].isChecked) {
				dwCheckState |= (1 << i);
			}
		}
		
		// 如果没有复选框被选中，默认选中上证A股
		if (dwCheckState == 0) {
			m_checkBoxes[CHK_SH_A].isChecked = true;
			dwCheckState |= (1 << CHK_SH_A);
			
			// 需要重绘上证A股复选框
			needFullInvalidate = true;
		}
		
		// 更新状态掩码
		m_dwCheckBoxState = dwCheckState;
		
		// 调用复选框回调函数
		if (m_pCheckBoxCallback) {
			m_pCheckBoxCallback(checkBoxIndex, m_checkBoxes[checkBoxIndex].isChecked, m_lCheckBoxParam);
		}
	}
	
	// 记录需要重绘的复选框区域
	CRect rcCheckBoxInvalidate;
	
	// 清除复选框按下状态
	if (m_nPressedCheckBox >= 0) {
		rcCheckBoxInvalidate = m_checkBoxes[m_nPressedCheckBox].rect;
		m_nPressedCheckBox = -1;
	}
	
	// 根据情况决定重绘范围
	if (needFullInvalidate) {
		// 如果需要全部重绘（例如，按钮选中状态变化或复选框默认选中）
		Invalidate(FALSE);
	} else {
		// 分别重绘按钮和复选框区域
		if (!rcButtonInvalidate.IsRectEmpty()) {
			InvalidateRect(rcButtonInvalidate, FALSE);
		}
		if (!rcCheckBoxInvalidate.IsRectEmpty()) {
			InvalidateRect(rcCheckBoxInvalidate, FALSE);
		}
	}
	
	CView::OnLButtonUp(nFlags, point);
}

// 设置复选框回调函数
void CSymbolBar::SetCheckBoxCallback(ON_CHECKBOX_CLICKED pfnCallback, LPARAM lParam)
{
	m_pCheckBoxCallback = pfnCallback;
	m_lCheckBoxParam = lParam;
}

// 设置复选框状态
void CSymbolBar::SetCheckBoxState(DWORD dwState)
{
	// 更新状态掩码
	m_dwCheckBoxState = dwState;
	
	// 更新各个复选框的选中状态
	for (int i = 0; i < CHK_COUNT; i++)
	{
		m_checkBoxes[i].isChecked = (dwState & (1 << i)) != 0;
	}
	
	// 重绘
	Invalidate(FALSE);
} 