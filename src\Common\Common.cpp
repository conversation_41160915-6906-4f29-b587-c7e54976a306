﻿#include "pch.h"
#include "Common.h"

// 根据股票代码和名称判断股票类型
StockType GetStockType(const CString& stockCode, const CString& stockName)
{
    // 判断是否为指数
    if (stockCode.GetLength() > 0 && (stockCode[0] == '0' || stockCode[0] == '3' || stockCode[0] == '9'))
    {
        if (stockCode.GetLength() > 2 && stockCode[1] == '0' && stockCode[2] == '0')
            return STOCK_TYPE_INDEX; // 指数代码通常为000xxx, 399xxx等
    }
    
    // 判断是否为北交所股票
    if (stockCode.GetLength() >= 6 && stockCode[0] == '8' && stockCode[1] >= '0' && stockCode[1] <= '9')
    {
        return STOCK_TYPE_BSE; // 北交所股票代码以8开头的6位数
    }
    
    // 判断是否为创业板股票
    if (stockCode.GetLength() >= 6 && stockCode.Left(3) == _T("300"))
    {
        return STOCK_TYPE_GEM; // 创业板以300开头
    }
    
    // 判断是否为科创板股票
    if (stockCode.GetLength() >= 6 && stockCode.Left(3) == _T("688"))
    {
        return STOCK_TYPE_STAR; // 科创板以688开头
    }
    
    // 判断是否为ST股票
    if (!stockName.IsEmpty() && (stockName.Find(_T("ST")) >= 0 || stockName.Find(_T("st")) >= 0))
    {
        return STOCK_TYPE_ST; // 名称中包含ST字样
    }
    
    // 判断是否为新股
    if (IsStockNew(stockCode))
    {
        return STOCK_TYPE_NEW;
    }
    
    // 默认为普通A股
    return STOCK_TYPE_NORMAL;
}

// 根据股票代码判断市场类型（新方法）
MarketType DetermineMarketInfo(const std::string& code)
{
	// 初始化为默认值
	MarketType marketType = MARKET_ALL;

	if (!code.empty()) {
		if (code.size() >= 2 && code.substr(0, 2) == "68") {
			// 科创板股票：以68开头
			marketType = MARKET_STAR;
		}
		else if (code.size() >= 1) {
			char prefix = code[0];
			if (prefix == '6') {
				// 上证A股：以6开头但不是68开头
				marketType = MARKET_SH_A;
			}
			else if (prefix == '0') {
				marketType = MARKET_SZ_A;
			}
			else if (prefix == '3') {
				marketType = MARKET_GEM;
			}
			else if (prefix == '4' || prefix == '8') {
				marketType = MARKET_BJ_A;
			}
			else if (prefix == '5') {
				marketType = MARKET_STAR;
			}
		}
	}
	
	return marketType;
}

// 根据股票代码获取市场标识
std::string GetMarketPrefix(const std::string& code)
{
	std::string market = "";

	if (!code.empty()) {
		if (code.size() >= 2 && code.substr(0, 2) == "68") {
			// 科创板股票：以68开头
			market = "sh";
		}
		else if (code.size() >= 1) {
			char prefix = code[0];
			if (prefix == '6') {
				// 上证A股：以6开头但不是68开头
				market = "sh";
			}
			else if (prefix == '0') {
				market = "sz";
			}
			else if (prefix == '3') {
				market = "sz";
			}
			else if (prefix == '4' || prefix == '8') {
				market = "bj";
			}
			else if (prefix == '5') {
				market = "sh";
			}
		}
	}
	
	return market;
}

// 根据股票代码判断市场类型和标识（原方法，保留兼容性）
void DetermineMarketInfo(const std::string& code, std::string& market, MarketType& marketType) 
{
	market = GetMarketPrefix(code);
	marketType = DetermineMarketInfo(code);
}

// 判断是否为新股
BOOL IsStockNew(const CString& stockCode)
{
    // 简单判断：如果代码以00、60或30开头，且最后一位是0、1、2或3，可能是新股
    // 实际应用中应该查询数据库或接口获取上市日期
    if (stockCode.GetLength() >= 6)
    {
        CString prefix = stockCode.Left(2);
        TCHAR lastChar = stockCode[stockCode.GetLength() - 1];
        
        if ((prefix == _T("00") || prefix == _T("60") || prefix == _T("30")) &&
            (lastChar >= '0' && lastChar <= '3'))
        {
            return TRUE;
        }
    }
    
    return FALSE;
}

// 判断是否为新股首日
BOOL IsStockFirstTradingDay(const CString& stockCode)
{
    // 此处简化处理，实际应用中应该查询数据库或接口
    // 例如：检查当前日期是否等于该股票的上市日期
    
    // 为了演示，这里假设所有新股都不是首日交易
    return FALSE;
}

// 获取对应类型股票的涨停幅度
float GetLimitUpRate(StockType type, BOOL isFirstDay)
{
    switch (type)
    {
    case STOCK_TYPE_NORMAL:
        return 0.10f; // 普通A股涨幅10%
        
    case STOCK_TYPE_ST:
        return 0.05f; // ST股票涨幅5%
        
    case STOCK_TYPE_GEM:
    case STOCK_TYPE_STAR:
        return 0.20f; // 创业板和科创板涨幅20%
        
    case STOCK_TYPE_BSE:
        return 0.30f; // 北交所股票涨幅30%
        
    case STOCK_TYPE_NEW:
        if (isFirstDay)
            return 0.44f; // 新股首日涨幅44%
        else
            return 0.10f; // 非首日按普通股处理
        
    case STOCK_TYPE_INDEX:
        return 0.15f; // 指数无实际涨跌停限制，这里设置15%作为绘图参考
        
    default:
        return 0.10f;
    }
}

// 获取对应类型股票的跌停幅度
float GetLimitDownRate(StockType type, BOOL isFirstDay)
{
    switch (type)
    {
    case STOCK_TYPE_NORMAL:
        return 0.10f; // 普通A股跌幅10%
        
    case STOCK_TYPE_ST:
        return 0.05f; // ST股票跌幅5%
        
    case STOCK_TYPE_GEM:
    case STOCK_TYPE_STAR:
        return 0.20f; // 创业板和科创板跌幅20%
        
    case STOCK_TYPE_BSE:
        return 0.30f; // 北交所股票跌幅30%
        
    case STOCK_TYPE_NEW:
        if (isFirstDay)
            return 0.0f; // 新股首日无跌幅限制
        else
            return 0.10f; // 非首日按普通股处理
        
    case STOCK_TYPE_INDEX:
        return 0.15f; // 指数无实际涨跌停限制，这里设置15%作为绘图参考
        
    default:
        return 0.10f;
    }
}


CString GetWeekString(int nWeek)
{
	CString strWeek = "未知";
	if (nWeek == 1)
	{
		strWeek = "星期日";
		return strWeek;
	}
	else if (nWeek == 2)
	{
		strWeek = "星期一";
		return strWeek;
	}
	else if (nWeek == 3)
	{
		strWeek = "星期二";
		return strWeek;
	}
	else if (nWeek == 4)
	{
		strWeek = "星期三";
		return strWeek;
	}
	else if (nWeek == 5)
	{
		strWeek = "星期四";
		return strWeek;
	}
	else if (nWeek == 6)
	{
		strWeek = "星期五";
		return strWeek;
	}
	else if (nWeek == 7)
	{
		strWeek = "星期六";
		return strWeek;
	}
	return strWeek;
}

float  RoundUp(float  val, int n)
{
	float ret = 0;
	CString strTemp;
	strTemp.Format("%.2f", val * 100000);
	CString ww = strTemp.Mid(strTemp.GetLength() - 5, 1);
	int t1 = atoi(ww.GetBuffer(0));
	CString ww2 = strTemp.Mid(strTemp.GetLength() - 6, 1);
	int t = atoi(ww2.GetBuffer(0));
	if (t1 > 4) 
		t++;
	if (t > 4)
	{
		ret = (float)((int)(val * 100) + 1) / 100;
	}
	else
	{
		ret = (float)((int)(val * 100)) / 100;
	}
	return ret;
}
;
;



float CalcLimitUpPrice(std::string code, std::string name, float fPreClose)
{
	float fHighLimit = 0.000;
	std::string strCode = code.substr(0, 2);
	CString strFix = strCode.c_str();
	if ((strFix == "68") || (strFix == "30"))
	{
		fHighLimit = fPreClose + (fPreClose * 20) / 100;
		fHighLimit = RoundUp(fHighLimit, 2);
	}
	else if ((strFix == "00") || (strFix == "60"))
	{
		fHighLimit = fPreClose + (fPreClose * 10) / 100;
		fHighLimit = RoundUp(fHighLimit, 2);
	}
	else
	{
		fHighLimit = fPreClose + (fPreClose * 30) / 100;
		fHighLimit = RoundUp(fHighLimit, 2);
	}
	return fHighLimit;
}
      
float CalcLimitDownPrice(std::string code, std::string name, float fPreClose)
{
	float fLowLimit = 0.0f;
	if (fPreClose <= 0)
		return 0;
	std::string strCode = code.substr(0, 2);
	CString strFix = strCode.c_str();
	if ((strFix == "68") || (strFix == "30"))
	{
		fLowLimit = fPreClose - (fPreClose * 20) / 100;
		fLowLimit = RoundUp(fLowLimit, 2);
	}
	else if ((strFix == "00") || (strFix == "60"))
	{
		fLowLimit = fPreClose - (fPreClose * 10) / 100;
		fLowLimit = RoundUp(fLowLimit, 2);
	}
	else
	{
		fLowLimit = fPreClose - (fPreClose * 30) / 100;
		fLowLimit = RoundUp(fLowLimit, 2);
	}
	return fLowLimit;
}

float CalcLimitUpPercent(std::string code, std::string name, float fPreClose)
{
	float fPrice = CalcLimitUpPrice(code, name, fPreClose);
	if (fPreClose <= 0)
		return 0;
	float fPercent = (fPrice - fPreClose) / fPreClose * 100;
	return fPercent;
}

float CalcLimitDownPercent(std::string code, std::string name, float fPreClose)
{
	float fPrice = CalcLimitDownPrice(code, name, fPreClose);
	if (fPreClose <= 0)
		return 0;
	float fPercent = (fPrice - fPreClose) / fPreClose * 100;
	return fPercent;
}

BOOL IsLimitUp(CString strCode, float fPreClose, float fClose)
{
	float fHighLimit = 0.000;
	CString strFix = strCode.Left(2);
	if ((strFix == "68") || (strFix == "30"))
	{
		fHighLimit = fPreClose + (fPreClose * 20) / 100;
		fHighLimit = RoundUp(fHighLimit, 2);
	}
	else if ((strFix == "60") || (strFix == "00"))
	{
		fHighLimit = fPreClose + (fPreClose * 10) / 100;
		fHighLimit = RoundUp(fHighLimit, 2);
	}
	else
	{
		fHighLimit = fPreClose + (fPreClose * 30) / 100;
		fHighLimit = RoundUp(fHighLimit, 2);
	}
	return (fHighLimit == RoundUp(fClose, 2));
}

BOOL IsLimitDown(CString strCode, float fPreClose, float fClose)
{
	float fLowLimit = 0.000;
	CString strFix = strCode.Left(2);
	if ((strFix == "68") || (strFix == "30"))
	{
		fLowLimit = fPreClose - (fPreClose * 20) / 100;
		fLowLimit = RoundUp(fLowLimit, 2);
	}
	else if ((strFix == "60") || (strFix == "00"))
	{
		fLowLimit = fPreClose - (fPreClose * 10) / 100;
		fLowLimit = RoundUp(fLowLimit, 2);
	}
	else
	{
		fLowLimit = fPreClose - (fPreClose * 30) / 100;
		fLowLimit = RoundUp(fLowLimit, 2);
	}
	return (fLowLimit == RoundUp(fClose, 2));
}