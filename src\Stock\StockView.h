﻿// StockView.h: CStockView 类的接口
//

#pragma once

class CStockDoc;

class CStockView : public CView
{
protected: // 仅从序列化创建
	CStockView() noexcept;
	DECLARE_DYNCREATE(CStockView)

// 特性
public:
	CStockDoc* GetDocument() const;

// 操作
public:
	virtual void OnInitialUpdate();

// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);

// 实现
public:
	virtual ~CStockView();

protected:


// 生成的消息映射函数
protected:
	DECLARE_MESSAGE_MAP()
};


