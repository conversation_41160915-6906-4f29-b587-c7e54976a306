﻿#pragma once
#include <string>
#include <unordered_map>
#include <mutex>
#include <windows.h>

// 字符串转换工具类，提供缓存机制提高性能
class StringUtils {
private:
    // 转换结果缓存
    static std::unordered_map<std::wstring, std::string> s_conversionCache;
    static std::mutex s_cacheMutex;
    static const size_t MAX_CACHE_SIZE = 1000;  // 最大缓存条目数
    
public:
    // 将宽字符串转换为ANSI字符串（带缓存）
    static std::string WideToANSI(const std::wstring& wstr) {
        if (wstr.empty()) return "";
        
        // 检查缓存
        {
            std::lock_guard<std::mutex> lock(s_cacheMutex);
            auto it = s_conversionCache.find(wstr);
            if (it != s_conversionCache.end()) {
                return it->second;
            }
        }
        
        // 执行转换
        std::string result = ConvertWideToANSI(wstr);
        
        // 添加到缓存
        {
            std::lock_guard<std::mutex> lock(s_cacheMutex);
            if (s_conversionCache.size() < MAX_CACHE_SIZE) {
                s_conversionCache[wstr] = result;
            }
        }
        
        return result;
    }
    
    // 将宽字符串转换为UTF-8字符串
    static std::string WideToUTF8(const std::wstring& wstr) {
        if (wstr.empty()) return "";
        
        int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, NULL, 0, NULL, NULL);
        if (len <= 0) return "";
        
        std::string result(len - 1, 0);  // -1 to exclude null terminator
        WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], len, NULL, NULL);
        return result;
    }
    
    // 将ANSI字符串转换为宽字符串
    static std::wstring ANSIToWide(const std::string& str) {
        if (str.empty()) return L"";
        
        int len = MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, NULL, 0);
        if (len <= 0) return L"";
        
        std::wstring result(len - 1, 0);  // -1 to exclude null terminator
        MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, &result[0], len);
        return result;
    }
    
    // 将UTF-8字符串转换为宽字符串
    static std::wstring UTF8ToWide(const std::string& str) {
        if (str.empty()) return L"";
        
        int len = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, NULL, 0);
        if (len <= 0) return L"";
        
        std::wstring result(len - 1, 0);  // -1 to exclude null terminator
        MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &result[0], len);
        return result;
    }
    
    // 清除转换缓存
    static void ClearCache() {
        std::lock_guard<std::mutex> lock(s_cacheMutex);
        s_conversionCache.clear();
    }
    
    // 获取缓存统计信息
    static size_t GetCacheSize() {
        std::lock_guard<std::mutex> lock(s_cacheMutex);
        return s_conversionCache.size();
    }
    
    // 字符串格式化辅助函数
    template<typename... Args>
    static std::string Format(const char* format, Args... args) {
        int size = snprintf(nullptr, 0, format, args...) + 1;
        if (size <= 0) return "";
        
        std::string result(size, 0);
        snprintf(&result[0], size, format, args...);
        result.pop_back();  // 移除末尾的null字符
        return result;
    }
    
    // 宽字符串格式化辅助函数
    template<typename... Args>
    static std::wstring FormatW(const wchar_t* format, Args... args) {
        int size = swprintf(nullptr, 0, format, args...) + 1;
        if (size <= 0) return L"";
        
        std::wstring result(size, 0);
        swprintf(&result[0], size, format, args...);
        result.pop_back();  // 移除末尾的null字符
        return result;
    }
    
    // 字符串修剪函数
    static std::string Trim(const std::string& str) {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos) return "";
        
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }
    
    // 宽字符串修剪函数
    static std::wstring TrimW(const std::wstring& str) {
        size_t start = str.find_first_not_of(L" \t\r\n");
        if (start == std::wstring::npos) return L"";
        
        size_t end = str.find_last_not_of(L" \t\r\n");
        return str.substr(start, end - start + 1);
    }
    
private:
    // 内部转换函数（不使用缓存）
    static std::string ConvertWideToANSI(const std::wstring& wstr) {
        int len = WideCharToMultiByte(CP_ACP, 0, wstr.c_str(), -1, NULL, 0, NULL, NULL);
        if (len <= 0) return "";
        
        std::string result(len - 1, 0);  // -1 to exclude null terminator
        WideCharToMultiByte(CP_ACP, 0, wstr.c_str(), -1, &result[0], len, NULL, NULL);
        return result;
    }
};

// 静态成员定义
std::unordered_map<std::wstring, std::string> StringUtils::s_conversionCache;
std::mutex StringUtils::s_cacheMutex;
