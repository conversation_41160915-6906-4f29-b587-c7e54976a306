﻿#include "pch.h"
#include "WinHttp.h"

/**
 * @brief 内存分配宏，分配指定大小的零初始化内存
 * @param _a 要分配的字节数
 * @return 分配的内存指针，失败时返回NULL
 */
#define AllocMemory(_a)  HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, _a)

/**
 * @brief 内存释放宏，安全释放内存并置空指针
 * @param _a 要释放的内存指针
 */
#define FreeMemory(_a)   { if (_a) { HeapFree(GetProcessHeap(), HEAP_NO_SERIALIZE, _a); _a=NULL; } }

// 定义缺少的安全标志
#ifndef INTERNET_FLAG_IGNORE_CERT_WRONG_USAGE
#define INTERNET_FLAG_IGNORE_CERT_WRONG_USAGE 0x00000200
#endif

/**
 * @brief 错误信息映射数组，与HttpErrorCode枚举对应
 */
static const char* g_ErrorMessages[] = {
	"操作成功",
	"初始化失败",
	"连接服务器失败",
	"发送请求失败",
	"接收响应失败",
	"解压数据失败",
	"内存分配失败",
	"无效参数",
	"未连接到服务器",
	"操作已取消",
	"操作超时",
	"未知错误"
};

// 回调函数，用于拦截WinInet状态变化
VOID CALLBACK InternetStatusCallback(
    HINTERNET hInternet,
    DWORD_PTR dwContext,
    DWORD dwInternetStatus,
    LPVOID lpvStatusInformation,
    DWORD dwStatusInformationLength)
{
    CWinHttp* pThis = (CWinHttp*)dwContext;
    if (!pThis)
        return;
        
    const char* statusText = "未知状态";
    
    switch (dwInternetStatus)
    {
    case INTERNET_STATUS_RESOLVING_NAME:
        statusText = "正在解析域名";
        break;
    case INTERNET_STATUS_NAME_RESOLVED:
        statusText = "域名已解析";
        break;
    case INTERNET_STATUS_CONNECTING_TO_SERVER:
        statusText = "正在连接到服务器";
        break;
    case INTERNET_STATUS_CONNECTED_TO_SERVER:
        statusText = "已连接到服务器";
        break;
    case INTERNET_STATUS_SENDING_REQUEST:
        statusText = "正在发送请求";
        break;
    case INTERNET_STATUS_REQUEST_SENT:
        statusText = "请求已发送";
        break;
    case INTERNET_STATUS_RECEIVING_RESPONSE:
        statusText = "正在接收响应";
        break;
    case INTERNET_STATUS_RESPONSE_RECEIVED:
        statusText = "响应已接收";
        break;
    case INTERNET_STATUS_CLOSING_CONNECTION:
        statusText = "正在关闭连接";
        break;
    case INTERNET_STATUS_CONNECTION_CLOSED:
        statusText = "连接已关闭";
        break;
    case INTERNET_STATUS_HANDLE_CREATED:
        statusText = "句柄已创建";
        break;
    case INTERNET_STATUS_HANDLE_CLOSING:
        statusText = "句柄正在关闭";
        break;
    case INTERNET_STATUS_REQUEST_COMPLETE:
        statusText = "请求已完成";
        break;
    }
    
    //TRACE("WinInet状态回调 [0x%p]: %s\n", hInternet, statusText);
    
    // 特殊处理句柄关闭事件
    if (dwInternetStatus == INTERNET_STATUS_HANDLE_CLOSING)
    {
        //TRACE("注意: WinInet句柄0x%p正在关闭\n", hInternet);
        
        // 检查是否为请求句柄
        if (hInternet == pThis->m_hRequest)
        {
            TRACE("警告: 请求句柄m_hRequest=0x%p正在被WinInet关闭，但这可能不是由我们的代码发起的\n", 
                  hInternet);
        }
    }
}

/**
 * @brief CWinHttp构造函数
 * @details 初始化成员变量，设置默认超时和选项
 */
CWinHttp::CWinHttp(void)
: m_hSession(NULL)
, m_hConnect(NULL)
, m_hRequest(NULL)
, m_bHttps(false)
, m_nResponseCode(0)
, m_dwConnectTimeout(60000)   // 默认60秒连接超时
, m_dwSendTimeout(30000)      // 默认30秒发送超时
, m_dwReceiveTimeout(30000)   // 默认30秒接收超时
, m_bAutoGZip(FALSE)           // 默认启用GZIP自动解压
, m_bVerifyCert(FALSE)        // 默认禁用证书验证
, m_lastError(HTTP_ERROR_SUCCESS)
{
	m_bConnect = FALSE;
}

/**
 * @brief CWinHttp析构函数
 * @details 关闭所有连接和句柄
 */
CWinHttp::~CWinHttp(void)
{
	Close();
}

/**
 * @brief 检查是否已连接到服务器
 * @return 已连接返回TRUE，否则返回FALSE
 */
BOOL		CWinHttp::IsConnect()
{
	return m_bConnect;
}

/**
 * @brief 设置最后一次错误代码
 * @param code 错误代码
 */
void CWinHttp::SetLastError(HttpErrorCode code)
{
	m_lastError = code;
}

/**
 * @brief 获取最后一次错误的中文描述
 * @return 错误描述字符串
 */
std::string CWinHttp::GetLastErrorDescription()
{
	if (m_lastError >= HTTP_ERROR_SUCCESS && m_lastError <= HTTP_ERROR_TIMEOUT)
		return std::string(g_ErrorMessages[m_lastError]);
	else
		return std::string(g_ErrorMessages[HTTP_ERROR_UNKNOWN]);
}

/**
 * @brief 连接HTTP服务器
 * @param lpUrl 服务器URL
 * @return 成功返回TRUE，失败返回FALSE
 * @details 解析URL并建立连接，支持HTTP和HTTPS协议
 */
BOOL CWinHttp::ConnectHttpServer(LPCSTR lpUrl)
{
	// 验证URL
	if (!lpUrl || strlen(lpUrl) == 0)
	{
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}

	// 释放之前的连接
	Close();
	
	// 初始化Internet会话
	m_hSession = InternetOpen("Http-connect", INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, NULL);
	if (NULL == m_hSession)
	{
		DWORD dwErr = GetLastError();
		SetLastError(HTTP_ERROR_INITIALIZE_FAILED);
		return FALSE;
	}
	
	// 注册状态回调函数，使用this指针作为上下文
	if (InternetSetStatusCallback(m_hSession, InternetStatusCallback) == INTERNET_INVALID_STATUS_CALLBACK)
	{
		TRACE("设置Internet状态回调失败，错误码: %d\n", GetLastError());
	}
	else
	{
		//TRACE("成功注册Internet状态回调\n");
	}
	
	// 设置上下文，将this指针传递到Internet选项中
	DWORD_PTR dwContext = (DWORD_PTR)this;
	if (!InternetSetOption(m_hSession, INTERNET_OPTION_CONTEXT_VALUE, &dwContext, sizeof(dwContext)))
	{
		TRACE("设置上下文值失败，错误码: %d\n", GetLastError());
	}
	
	// 设置超时
	InternetSetOption(m_hSession, INTERNET_OPTION_CONNECT_TIMEOUT, &m_dwConnectTimeout, sizeof(m_dwConnectTimeout));
	InternetSetOption(m_hSession, INTERNET_OPTION_SEND_TIMEOUT, &m_dwSendTimeout, sizeof(m_dwSendTimeout));
	InternetSetOption(m_hSession, INTERNET_OPTION_RECEIVE_TIMEOUT, &m_dwReceiveTimeout, sizeof(m_dwReceiveTimeout));
	
	// 解析URL为主机名、路径和端口
	INTERNET_PORT port = INTERNET_DEFAULT_HTTP_PORT;
	string strHostName, strPageName;
	ParseUrlA(lpUrl, strHostName, strPageName, port);
	
	// 检查是否HTTPS连接
	if (port == INTERNET_DEFAULT_HTTPS_PORT)
		m_bHttps = TRUE;
	
	// 连接到HTTP服务器
	m_hConnect = InternetConnectA(m_hSession, strHostName.c_str(), port, NULL, NULL, INTERNET_SERVICE_HTTP, NULL, NULL);
	if (NULL == m_hConnect)
	{
		SetLastError(HTTP_ERROR_CONNECT_FAILED);
		return FALSE;
	}
	
	// 连接成功
	m_bConnect = TRUE;
	SetLastError(HTTP_ERROR_SUCCESS);
	return TRUE;
}

/**
 * @brief 设置HTTP请求超时
 * @param dwConnectTimeout 连接超时(毫秒)
 * @param dwSendTimeout 发送超时(毫秒)
 * @param dwReceiveTimeout 接收超时(毫秒)
 */
void CWinHttp::SetTimeout(DWORD dwConnectTimeout, DWORD dwSendTimeout, DWORD dwReceiveTimeout)
{
	m_dwConnectTimeout = dwConnectTimeout;
	m_dwSendTimeout = dwSendTimeout;
	m_dwReceiveTimeout = dwReceiveTimeout;
	
	// 如果会话已存在，立即应用超时设置
	if (m_hSession)
	{
		InternetSetOption(m_hSession, INTERNET_OPTION_CONNECT_TIMEOUT, &m_dwConnectTimeout, sizeof(m_dwConnectTimeout));
		InternetSetOption(m_hSession, INTERNET_OPTION_SEND_TIMEOUT, &m_dwSendTimeout, sizeof(m_dwSendTimeout));
		InternetSetOption(m_hSession, INTERNET_OPTION_RECEIVE_TIMEOUT, &m_dwReceiveTimeout, sizeof(m_dwReceiveTimeout));
	}
}

/**
 * @brief 验证URL是否合法
 * @param lpUrl 待验证的URL
 * @return 合法返回TRUE，不合法返回FALSE
 */
BOOL CWinHttp::ValidateUrl(LPCSTR lpUrl)
{
	if (!lpUrl || strlen(lpUrl) == 0)
		return FALSE;
		
	// 确保URL包含协议
	if (strstr(lpUrl, "http://") == NULL && strstr(lpUrl, "https://") == NULL)
		return FALSE;
		
	return TRUE;
}

// UTF8 转 多字节
PSTR UTF8ToANSI( PSTR str,  size_t size)
{
	LPWSTR  pElementText;
	int    iTextLen;
	// wide char to multi char
	iTextLen = MultiByteToWideChar(CP_UTF8,
		0,
		str,
		-1,
		NULL,
		0);

	pElementText = (LPWSTR)AllocMemory((iTextLen + 1) * sizeof(WCHAR));
	_ASSERT(pElementText);
	if (pElementText == NULL)
	{
		return NULL;
	}
	MultiByteToWideChar(CP_UTF8,
		0,
		str,
		-1,
		pElementText,
		iTextLen);

	memset(str, 0, size);
	WideCharToMultiByte(CP_ACP,
		0,
		pElementText,
		-1,
		(PCHAR)str,
		size,
		NULL,
		NULL);

	FreeMemory(pElementText);
	return str;
}

PSTR ANSIToUTF8( PSTR str,  size_t size)
{
	LPWSTR  pElementText;
	int    iTextLen;
	// wide char to multi char
	iTextLen = MultiByteToWideChar(CP_ACP,
		0,
		str,
		-1,
		NULL,
		0);

	pElementText = (LPWSTR)AllocMemory((iTextLen + 1) * sizeof(WCHAR));
	_ASSERT(pElementText);
	if (pElementText == NULL)
	{
		return NULL;
	}
	MultiByteToWideChar(CP_ACP,
		0,
		str,
		-1,
		pElementText,
		iTextLen);

	memset(str, 0, size);
	WideCharToMultiByte(CP_UTF8,
		0,
		pElementText,
		-1,
		(PCHAR)str,
		size,
		NULL,
		NULL);

	FreeMemory(pElementText);
	return str;
}


// 多字节转宽字节，在传入参数时需要传入指针
PWSTR MulToWide(LPCSTR str)
{
	PWSTR  pElementText;
	int    iTextLen;

	iTextLen = MultiByteToWideChar(CP_ACP,
		0,
		(PCHAR)str,
		-1,
		NULL,
		0);

	pElementText =
		(PWSTR)AllocMemory((iTextLen + 1) * sizeof(WCHAR));
	_ASSERT(pElementText);
	if (pElementText)
	{
		MultiByteToWideChar(CP_ACP,
			0,
			(PCHAR)str,
			-1,
			pElementText,
			iTextLen);
	}

	return pElementText;
}

PSTR WideToMul(LPCWSTR str)
{
	PSTR  pElementText;
	int    iTextLen;

	iTextLen = WideCharToMultiByte(CP_ACP,
		0,
		str,
		-1,
		NULL,
		0,
		NULL,
		NULL);

	pElementText =
		(PSTR)AllocMemory(iTextLen + 1);
	_ASSERT(pElementText);
	if (pElementText)
	{
		WideCharToMultiByte(CP_ACP,
			0,
			str,
			-1,
			pElementText,
			iTextLen,
			NULL,
			NULL);
	}

	return pElementText;
}


/**
 * @brief 发送HTTP请求(内部实现)
 * @param lpMethod HTTP方法(GET/POST/PUT/DELETE等)
 * @param lpUrl 目标URL
 * @param pData 请求体数据
 * @param dwDataLen 请求体数据长度
 * @param strHtml 接收响应内容的字符串
 * @return 成功返回TRUE，失败返回FALSE
 * @details 核心方法，处理所有类型的HTTP请求发送和响应接收
 */
BOOL CWinHttp::SendHttpRequest(LPCSTR lpMethod, LPCSTR lpUrl, const char* pData, DWORD dwDataLen, CString &strHtml)
{
	// 重置错误状态
	SetLastError(HTTP_ERROR_SUCCESS);
	strHtml = "";
	
	// 验证参数
	if (!lpMethod || !lpUrl || strlen(lpUrl) == 0)
	{
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}
	
	// 验证连接状态
	if (!m_bConnect || !m_hConnect)
	{
		// 尝试连接
		if (!ConnectHttpServer(lpUrl))
		{
			return FALSE; // ConnectHttpServer已设置错误代码
		}
	}
	
	INTERNET_PORT port = INTERNET_DEFAULT_HTTP_PORT;
	string strHostName, strPageName;
	ParseUrlA(lpUrl, strHostName, strPageName, port);

	// 设置HTTP请求标志
	DWORD dwFlags = INTERNET_FLAG_RELOAD | INTERNET_FLAG_KEEP_CONNECTION;
	if (m_bHttps)
	{
		dwFlags |= (INTERNET_FLAG_SECURE);
		// 根据证书验证选项设置标志
		if (!m_bVerifyCert)
		{
			dwFlags |= (INTERNET_FLAG_IGNORE_CERT_CN_INVALID | 
						INTERNET_FLAG_IGNORE_CERT_DATE_INVALID |
						INTERNET_FLAG_IGNORE_CERT_WRONG_USAGE);
		}
	}
	
	// 创建HTTP请求句柄
	m_hRequest = HttpOpenRequestA(m_hConnect, lpMethod, strPageName.c_str(), "HTTP/1.1", NULL, NULL, dwFlags, NULL);
	if (NULL == m_hRequest)
	{
		TRACE("HttpOpenRequestA失败，错误码: %d\n", GetLastError());
		SetLastError(HTTP_ERROR_INITIALIZE_FAILED);
		return FALSE;
	}
	
	//TRACE("成功创建HTTP请求句柄: 0x%p\n", m_hRequest);

	BOOL bRet = FALSE;
	
	// 如果启用自动GZIP，则添加Accept-Encoding头
	if (m_bAutoGZip)
	{
		AddHeader("Accept-Encoding", "gzip, deflate");
	}
	
	// 添加HTTP请求头
	std::string httpHeaders = m_header.toHttpHeaders();
	HttpAddRequestHeadersA(m_hRequest, httpHeaders.c_str(), httpHeaders.size(), HTTP_ADDREQ_FLAG_ADD | HTTP_ADDREQ_FLAG_REPLACE);
	
	// 设置超时选项到请求
	InternetSetOption(m_hRequest, INTERNET_OPTION_SEND_TIMEOUT, &m_dwSendTimeout, sizeof(m_dwSendTimeout));
	InternetSetOption(m_hRequest, INTERNET_OPTION_RECEIVE_TIMEOUT, &m_dwReceiveTimeout, sizeof(m_dwReceiveTimeout));
	
	// 尝试禁用连接过期检查，通知系统此连接需要保持活动状态
	DWORD dwKeepAlive = 1;
	InternetSetOption(m_hRequest, INTERNET_OPTION_KEEP_CONNECTION, &dwKeepAlive, sizeof(dwKeepAlive));
	
	// 设置较长的保持连接超时（10分钟）
	DWORD dwKeepAliveTimeout = 600000;
	InternetSetOption(m_hRequest, INTERNET_OPTION_RECEIVE_TIMEOUT, &dwKeepAliveTimeout, sizeof(dwKeepAliveTimeout));
	
	// 启用重定向支持
	DWORD dwRedirectEnabled = 1;
	InternetSetOption(m_hRequest, INTERNET_OPTION_SECURITY_FLAGS, &dwRedirectEnabled, sizeof(dwRedirectEnabled));
	
	// 发送请求
	if (pData != NULL && dwDataLen > 0)
	{
		TRACE("准备发送POST请求，m_hRequest=0x%p\n", m_hRequest);
		bRet = HttpSendRequestA(m_hRequest, NULL, 0, (LPVOID)pData, dwDataLen);
		DWORD dwErr = GetLastError();
		// 检查请求句柄是否还有效
		if (m_hRequest == NULL)
		{
			TRACE("发送POST请求后m_hRequest变为NULL，错误码: %d\n", dwErr);
			
			// 尝试重新创建请求句柄
			m_hRequest = HttpOpenRequestA(m_hConnect, lpMethod, strPageName.c_str(), "HTTP/1.1", NULL, NULL, dwFlags, NULL);
			if (m_hRequest == NULL)
			{
				TRACE("尝试重新创建请求句柄失败，错误码: %d\n", GetLastError());
				SetLastError(HTTP_ERROR_SEND_FAILED);
				return FALSE;
			}
			
			TRACE("成功重新创建请求句柄: 0x%p\n", m_hRequest);
			
			// 重新添加HTTP请求头
			if (!httpHeaders.empty())
			{
				HttpAddRequestHeadersA(m_hRequest, httpHeaders.c_str(), httpHeaders.size(), 
					HTTP_ADDREQ_FLAG_ADD | HTTP_ADDREQ_FLAG_REPLACE);
			}
			
			// 重新尝试发送请求
			TRACE("尝试重新发送POST请求\n");
			bRet = HttpSendRequestA(m_hRequest, NULL, 0, (LPVOID)pData, dwDataLen);
			dwErr = GetLastError();
			
			if (!bRet)
			{
				TRACE("重新发送POST请求失败，错误码: %d\n", dwErr);
				SetLastError(HTTP_ERROR_SEND_FAILED);
				return FALSE;
			}
			
			TRACE("重新发送POST请求成功\n");
		}
		else if (!bRet)
		{
			TRACE("发送POST请求失败，m_hRequest仍然有效，错误码: %d\n", dwErr);
		}
	}
	else
	{
		//TRACE("准备发送GET请求，m_hRequest=0x%p\n", m_hRequest);
		bRet = HttpSendRequestA(m_hRequest, NULL, 0, NULL, 0);
		DWORD dwErr = GetLastError();
		// 检查请求句柄是否还有效
		if (m_hRequest == NULL)
		{
			TRACE("发送GET请求后m_hRequest变为NULL，错误码: %d\n", dwErr);
			
			// 尝试重新创建请求句柄
			m_hRequest = HttpOpenRequestA(m_hConnect, lpMethod, strPageName.c_str(), "HTTP/1.1", NULL, NULL, dwFlags, NULL);
			if (m_hRequest == NULL)
			{
				TRACE("尝试重新创建请求句柄失败，错误码: %d\n", GetLastError());
				SetLastError(HTTP_ERROR_SEND_FAILED);
				return FALSE;
			}
			
			TRACE("成功重新创建请求句柄: 0x%p\n", m_hRequest);
			
			// 重新添加HTTP请求头
			if (!httpHeaders.empty())
			{
				HttpAddRequestHeadersA(m_hRequest, httpHeaders.c_str(), httpHeaders.size(), 
					HTTP_ADDREQ_FLAG_ADD | HTTP_ADDREQ_FLAG_REPLACE);
			}
			
			// 重新尝试发送请求
			TRACE("尝试重新发送GET请求\n");
			bRet = HttpSendRequestA(m_hRequest, NULL, 0, NULL, 0);
			dwErr = GetLastError();
			
			if (!bRet)
			{
				TRACE("重新发送GET请求失败，错误码: %d\n", dwErr);
				SetLastError(HTTP_ERROR_SEND_FAILED);
				return FALSE;
			}
			
			TRACE("重新发送GET请求成功\n");
		}
		else if (!bRet)
		{
			TRACE("发送GET请求失败，m_hRequest仍然有效，错误码: %d\n", dwErr);
		}
	}
	
	if (!bRet)
	{
		SetLastError(HTTP_ERROR_SEND_FAILED);
		return FALSE;
	}

	// 获取响应头
	char szBuffer[4096] = { 0 };
	DWORD dwSize = 4096;
	if (HttpQueryInfoA(m_hRequest, HTTP_QUERY_RAW_HEADERS_CRLF, szBuffer, &dwSize, NULL))
	{
		m_strResponseHeader = szBuffer;
	}
	
	// 获取HTTP状态码
	m_nResponseCode = QueryStatusCode();
	if (m_nResponseCode == HTTP_STATUS_NOT_FOUND)
	{
		SetLastError(HTTP_ERROR_RECEIVE_FAILED);
		return FALSE;
	}

	// 检查是否为GZIP压缩内容
	BOOL bGZipContent = FALSE;
	std::string strContentEncoding = GetResponseHeaderValue("Content-Encoding");
	if (m_bAutoGZip && !strContentEncoding.empty() && 
		(strContentEncoding.find("gzip") != std::string::npos || 
		 strContentEncoding.find("deflate") != std::string::npos))
	{
		bGZipContent = TRUE;
	}
	
	// 读取响应数据
	return ReadResponseData(strHtml, bGZipContent);
}

/**
 * @brief 发送HTTP GET请求
 * @param pUrl 目标URL
 * @param type 请求类型(只支持HttpGet)
 * @param strHtml 接收响应内容的字符串
 * @return 成功返回TRUE，失败返回FALSE
 */
BOOL CWinHttp::Request(LPCSTR lpUrl, HttpRequest type, CString &strHtml)
{
	if (!ValidateUrl(lpUrl))
	{
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}
	
	if (type == HttpGet)
	{
		return SendHttpRequest("GET", lpUrl, NULL, 0, strHtml);
	}
	else if (type == HttpPost)
	{
		// 对于POST请求，需要提供POST数据，这里只返回FALSE
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}
	
	SetLastError(HTTP_ERROR_INVALID_PARAMETER);
	return FALSE;
}

/**
 * @brief 发送HTTP POST请求
 * @param pUrl 目标URL
 * @param pPostData POST数据
 * @param dwPostDataLen POST数据长度
 * @param strHtml 接收响应内容的字符串
 * @return 成功返回TRUE，失败返回FALSE
 */
BOOL CWinHttp::Post(LPCSTR pUrl, const char* pPostData, DWORD dwPostDataLen, CString& strHtml)
{
	// 验证参数
	if (!ValidateUrl(pUrl) || !pPostData || dwPostDataLen == 0)
	{
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}
	
	// 添加Content-Type头，如果未设置
	if (m_header.GetValue("Content-Type").empty())
	{
		AddHeader("Content-Type", "application/x-www-form-urlencoded");
	}
	
	// 添加Content-Length头
	char szLength[32] = { 0 };
	sprintf_s(szLength, "%d", dwPostDataLen);
	AddHeader("Content-Length", szLength);
	
	return SendHttpRequest("POST", pUrl, pPostData, dwPostDataLen, strHtml);
}

BOOL CWinHttp::Put(LPCSTR pUrl, const char* pPutData, DWORD dwPutDataLen, CString& strHtml)
{
	// 验证参数
	if (!ValidateUrl(pUrl) || !pPutData || dwPutDataLen == 0)
	{
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}
	
	// 添加Content-Type头，如果未设置
	if (m_header.GetValue("Content-Type").empty())
	{
		AddHeader("Content-Type", "application/x-www-form-urlencoded");
	}
	
	// 添加Content-Length头
	char szLength[32] = { 0 };
	sprintf_s(szLength, "%d", dwPutDataLen);
	AddHeader("Content-Length", szLength);
	
	return SendHttpRequest("PUT", pUrl, pPutData, dwPutDataLen, strHtml);
}

BOOL CWinHttp::Delete(LPCSTR pUrl, CString& strHtml)
{
	if (!ValidateUrl(pUrl))
	{
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}
	
	return SendHttpRequest("DELETE", pUrl, NULL, 0, strHtml);
}

CString CWinHttp::GetResponseHeader()
{
	return m_strResponseHeader;
}

/**
 * @brief 获取指定的HTTP响应头值
 * @param strKey 头部名称
 * @return 头部值字符串
 */
std::string CWinHttp::GetResponseHeaderValue(const std::string& strKey)
{
	if (strKey.empty() || !m_hRequest)
		return "";
	
	// 处理常见的HTTP头
	if (strKey == "Content-Type")
		return GetHeaderValueInternal(HTTP_QUERY_CONTENT_TYPE);
	else if (strKey == "Content-Length")
		return GetHeaderValueInternal(HTTP_QUERY_CONTENT_LENGTH);
	else if (strKey == "Content-Encoding")
		return GetHeaderValueInternal(HTTP_QUERY_CONTENT_ENCODING);
	else if (strKey == "Server")
		return GetHeaderValueInternal(HTTP_QUERY_SERVER);
	else if (strKey == "Location")
		return GetHeaderValueInternal(HTTP_QUERY_LOCATION);
	else if (strKey == "Last-Modified")
		return GetHeaderValueInternal(HTTP_QUERY_LAST_MODIFIED);
	else if (strKey == "Expires")
		return GetHeaderValueInternal(HTTP_QUERY_EXPIRES);
	else if (strKey == "ETag")
		return GetHeaderValueInternal(HTTP_QUERY_ETAG);
	
	// 对于自定义头，需要从原始响应中解析
	// 从完整响应头中查找指定的键
	CString header = GetResponseHeader();
	std::string headerStr = (LPCTSTR)header;
	
	std::string searchKey = strKey + ": ";
	size_t pos = headerStr.find(searchKey);
	if (pos != std::string::npos)
	{
		pos += searchKey.length();
		size_t endPos = headerStr.find("\r\n", pos);
		if (endPos != std::string::npos)
		{
			return headerStr.substr(pos, endPos - pos);
		}
	}
	
	return "";
}

// 内部方法，用于获取常见的HTTP头值
std::string CWinHttp::GetHeaderValueInternal(DWORD dwInfoLevel)
{
	if (!m_hRequest)
		return "";
		
	char szBuffer[1024] = { 0 };
	DWORD dwSize = 1024;
	
	if (!HttpQueryInfoA(m_hRequest, dwInfoLevel, szBuffer, &dwSize, NULL))
		return "";
		
	return std::string(szBuffer);
}

void CWinHttp::AddHeader(LPCSTR key, LPCSTR value)
{
	if (key && value)
	{
		m_header.addHeader(std::string(key), std::string(value));
	}
}

void CWinHttp::ReleaseHandle(HINTERNET& hInternet)
{
	if (hInternet) 
	{ 
		// 记录释放前的句柄值，便于调试
		//TRACE("释放句柄 0x%p\n", hInternet);
		
		BOOL bResult = InternetCloseHandle(hInternet); 
		if (!bResult)
		{
			DWORD dwError = GetLastError();
			TRACE("InternetCloseHandle失败，错误码: %d\n", dwError);
		}
		
		hInternet = NULL; 
	}
}

/**
 * @brief 关闭所有连接和句柄
 */
void CWinHttp::Close()
{
	//TRACE("开始关闭所有连接和句柄\n");
	
	// 保存当前句柄值用于调试
	HINTERNET hOldRequest = m_hRequest;
	HINTERNET hOldConnect = m_hConnect;
	HINTERNET hOldSession = m_hSession;
	
	m_bConnect = FALSE;
	
	// 按特定顺序释放句柄
	if (m_hRequest) 
	{
		ReleaseHandle(m_hRequest);
		if (m_hRequest != NULL) {
			TRACE("警告：ReleaseHandle未能将m_hRequest设置为NULL\n");
			m_hRequest = NULL;
		}
	}
	
	if (m_hConnect) 
	{
		ReleaseHandle(m_hConnect);
		if (m_hConnect != NULL) {
			TRACE("警告：ReleaseHandle未能将m_hConnect设置为NULL\n");
			m_hConnect = NULL;
		}
	}
	
	if (m_hSession) 
	{
		ReleaseHandle(m_hSession);
		if (m_hSession != NULL) {
			TRACE("警告：ReleaseHandle未能将m_hSession设置为NULL\n");
			m_hSession = NULL;
		}
	}
	
	//TRACE("已关闭所有连接和句柄 (Request: 0x%p, Connect: 0x%p, Session: 0x%p)\n", hOldRequest, hOldConnect, hOldSession);
	
	SetLastError(HTTP_ERROR_SUCCESS);
}

int CWinHttp::QueryStatusCode()
{
	int http_code = 0;
	if (!m_hRequest)
		return 0;
		
	wchar_t szBuffer[24] = { 0 };
	DWORD dwBufferSize = 24 * sizeof(wchar_t);
	if(HttpQueryInfo(m_hRequest, HTTP_QUERY_STATUS_CODE, szBuffer, &dwBufferSize, NULL)) {
		wchar_t *p = NULL;
		http_code = wcstoul(szBuffer, &p, 10);
	}
	return http_code;
}

/**
 * @brief 解析URL为主机名、路径和端口
 * @param lpUrl 输入URL
 * @param strHostName 接收主机名
 * @param strPage 接收页面路径
 * @param sPort 接收端口号
 */
void CWinHttp::ParseUrlA(LPCSTR lpUrl, string& strHostName, string& strPage, WORD& sPort)
{
	sPort = 80;  // 默认HTTP端口
	string strTemp(lpUrl);
	
	// 查找并移除协议头
	int nPos = strTemp.find("http://");
	if (string::npos != nPos)
		strTemp = strTemp.substr(nPos + 7, strTemp.size() - nPos - 7);
	else
	{
		nPos = strTemp.find("https://");
		if (wstring::npos != nPos)
		{
			sPort = 443;  // HTTPS默认端口
			strTemp = strTemp.substr(nPos + 8, strTemp.size() - nPos - 8);
		}
	}
	
	// 查找主机名与路径分隔符
	nPos = strTemp.find('/');
	if (string::npos == nPos)  // 没找到'/'，整个字符串是主机名
		strHostName = strTemp;
	else
		strHostName = strTemp.substr(0, nPos);
	
	// 查找主机名中的端口号
	int nPos1 = strHostName.find(':');
	if (nPos1 != string::npos)
	{
		string strPort = strTemp.substr(nPos1 + 1, strHostName.size() - nPos1 - 1);
		strHostName = strHostName.substr(0, nPos1);
		sPort = (WORD)atoi(strPort.c_str());
	}
	
	// 获取路径部分
	if (string::npos == nPos) {
		strPage = '/';  // 默认根路径
		return;
	}
	strPage = strTemp.substr(nPos, strTemp.size() - nPos);
}

// 检查数据是否为GZIP格式
BOOL CWinHttp::IsGZipContent(const BYTE* pBuffer, DWORD dwLen)
{
	if (!pBuffer || dwLen < 4)
		return FALSE;
		
	// GZIP文件头: 1F 8B 08 (第四个字节可能不是00)
	if (pBuffer[0] == 0x1F && pBuffer[1] == 0x8B && pBuffer[2] == 0x08)
		return TRUE;
		
	return FALSE;
}

// 解压GZIP数据
BOOL CWinHttp::UnGZipData(const BYTE* pGZipData, DWORD dwGZipDataLen, BYTE* pUnGZipData, DWORD& dwUnGZipDataLen)
{
#if HAS_GZIP_SUPPORT
	if (!pGZipData || !pUnGZipData || dwGZipDataLen == 0 || dwUnGZipDataLen == 0)
	{
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}
	
	// 使用zlib解压GZIP数据
	z_stream stream;
	memset(&stream, 0, sizeof(stream));
	
	stream.zalloc = Z_NULL;
	stream.zfree = Z_NULL;
	stream.opaque = Z_NULL;
	stream.avail_in = dwGZipDataLen;
	stream.next_in = (Bytef*)pGZipData;
	stream.avail_out = dwUnGZipDataLen;
	stream.next_out = (Bytef*)pUnGZipData;
	
	// 使用MAX_WBITS + 16进行GZIP解压，这是解析gzip头的标准方式
	int zRet = inflateInit2(&stream, MAX_WBITS + 16);
	if (zRet != Z_OK)
	{
		SetLastError(HTTP_ERROR_DECOMPRESS_FAILED);
		return FALSE;
	}
	
	// 执行解压
	zRet = inflate(&stream, Z_FINISH);
	
	// 无论如何都结束解压
	inflateEnd(&stream);
	
	// 检查解压结果
	if (zRet != Z_STREAM_END && zRet != Z_OK)
	{
		SetLastError(HTTP_ERROR_DECOMPRESS_FAILED);
		return FALSE;
	}
	
	// 更新解压后的实际大小
	dwUnGZipDataLen = stream.total_out;
	
	return TRUE;
#else
	SetLastError(HTTP_ERROR_DECOMPRESS_FAILED);
	return FALSE;
#endif
}

/**
 * @brief 处理GZIP响应
 * @param pData 压缩数据
 * @param dwDataLen 压缩数据长度
 * @param strHtml 接收解压后内容的字符串
 * @return 成功返回TRUE，失败返回FALSE
 */
BOOL CWinHttp::ProcessGZipResponse(const BYTE* pData, DWORD dwDataLen, CString& strHtml)
{
#if HAS_GZIP_SUPPORT
	if (!pData || dwDataLen == 0)
	{
		SetLastError(HTTP_ERROR_INVALID_PARAMETER);
		return FALSE;
	}
	
	// 检查是否是GZIP数据
	if (!IsGZipContent(pData, dwDataLen))
	{
		// 不是GZIP数据，直接作为普通文本处理
		try {
			// 使用安全的方式分配内存和处理字符串
			std::string strData((const char*)pData, dwDataLen);
			strHtml = strData.c_str();
			return TRUE;
		}
		catch (...)
		{
			SetLastError(HTTP_ERROR_MEMORY_ALLOC_FAILED);
			return FALSE;
		}
	}
	
	// 预估解压后的数据大小，保守估计是压缩数据的10倍
	// 但设置一个上限防止内存过度分配
	DWORD dwEstimatedSize = min(dwDataLen * 10, MAX_BUFFER_SIZE);
	BYTE* pUncompressed = (BYTE*)AllocMemory(dwEstimatedSize);
	if (!pUncompressed)
	{
		SetLastError(HTTP_ERROR_MEMORY_ALLOC_FAILED);
		return FALSE;
	}
	
	DWORD dwUncompressedSize = dwEstimatedSize;
	BOOL bSuccess = UnGZipData(pData, dwDataLen, pUncompressed, dwUncompressedSize);
	
	if (bSuccess)
	{
		try {
			// 使用安全的方式分配内存和处理字符串
			std::string strData((const char*)pUncompressed, dwUncompressedSize);
			strHtml = strData.c_str();
		}
		catch (...)
		{
			bSuccess = FALSE;
			SetLastError(HTTP_ERROR_MEMORY_ALLOC_FAILED);
		}
	}
	
	// 确保释放内存
	FreeMemory(pUncompressed);
	return bSuccess;
#else
	SetLastError(HTTP_ERROR_DECOMPRESS_FAILED);
	return FALSE;
#endif
}

// 读取HTTP响应数据
BOOL CWinHttp::ReadResponseData(CString& strHtml, BOOL bGZipContent)
{
	BOOL bRet = FALSE;
	
	if (bGZipContent)
	{
		// 如果是GZIP压缩内容，需要先读取所有数据，然后一次性解压
		BYTE* pCompressedData = NULL;
		DWORD dwTotalSize = 0;
		DWORD dwAllocSize = 1024 * 1024; // 初始分配1MB空间
		DWORD dwReadSize = 0;
		
		// 分配内存
		pCompressedData = (BYTE*)AllocMemory(dwAllocSize);
		if (!pCompressedData)
		{
			SetLastError(HTTP_ERROR_MEMORY_ALLOC_FAILED);
			return FALSE;
		}
		
		// 读取所有压缩数据
		while (TRUE)
		{
			if (dwTotalSize + DEFAULT_BUFFER_SIZE > dwAllocSize)
			{
				// 空间不足，重新分配，但不超过最大限制
				DWORD dwNewSize = min(dwAllocSize * 2, MAX_BUFFER_SIZE);
				if (dwNewSize <= dwAllocSize)
				{
					// 已达到最大限制
					SetLastError(HTTP_ERROR_MEMORY_ALLOC_FAILED);
					FreeMemory(pCompressedData);
					return FALSE;
				}
				
				BYTE* pNewData = (BYTE*)AllocMemory(dwNewSize);
				if (!pNewData)
				{
					SetLastError(HTTP_ERROR_MEMORY_ALLOC_FAILED);
					FreeMemory(pCompressedData);
					return FALSE;
				}
				
				// 复制原有数据
				memcpy(pNewData, pCompressedData, dwTotalSize);
				FreeMemory(pCompressedData);
				pCompressedData = pNewData;
				dwAllocSize = dwNewSize;
			}
			
			bRet = InternetReadFile(m_hRequest, pCompressedData + dwTotalSize, DEFAULT_BUFFER_SIZE, &dwReadSize);
			if (!bRet)
			{
				SetLastError(HTTP_ERROR_RECEIVE_FAILED);
				FreeMemory(pCompressedData);
				return FALSE;
			}
			
			if (0 == dwReadSize)
				break;
				
			dwTotalSize += dwReadSize;
		}
		
		if (dwTotalSize == 0)
		{
			// 没有数据
			FreeMemory(pCompressedData);
			return TRUE;
		}
		
		// 处理GZIP响应
		bRet = ProcessGZipResponse(pCompressedData, dwTotalSize, strHtml);
		
		// 释放内存
		FreeMemory(pCompressedData);
		
		if (!bRet)
		{
			SetLastError(HTTP_ERROR_DECOMPRESS_FAILED);
		}
		
		return bRet;
	}
	else
	{
		// 读取普通响应内容
		char szContentBuffer[DEFAULT_BUFFER_SIZE + 1] = { 0 };
		DWORD dwReadSize;
		while (TRUE)
		{
			ZeroMemory(szContentBuffer, DEFAULT_BUFFER_SIZE + 1);
			if (m_hRequest == NULL)
			{
				TRACE("读取响应数据前m_hRequest已为NULL\n");
				SetLastError(HTTP_ERROR_RECEIVE_FAILED);
				return FALSE;
			}
			
			bRet = InternetReadFile(m_hRequest, szContentBuffer, DEFAULT_BUFFER_SIZE, &dwReadSize);
			DWORD dwErr = GetLastError();
			
			if (m_hRequest == NULL)
			{
				TRACE("读取响应数据后m_hRequest变为NULL，错误码: %d\n", dwErr);
				SetLastError(HTTP_ERROR_RECEIVE_FAILED);
				return FALSE;
			}
			
			if (!bRet)
			{
				TRACE("InternetReadFile失败，错误码: %d\n", dwErr);
				SetLastError(HTTP_ERROR_RECEIVE_FAILED);
				return FALSE;
			}
			
			if (0 == dwReadSize)
				break;
				
			szContentBuffer[dwReadSize] = '\0';
			strHtml = strHtml + szContentBuffer;
		}
	}

	return TRUE;
}