﻿// CTabPage.cpp: CTabPage 类的实现
// 自绘选项卡控件：背景色为黑色，选项卡无边框，与整个控件等宽

#include "pch.h"
#include "framework.h"
#include "TabPage.h"

// CTabPage

IMPLEMENT_DYNAMIC(CTabPage, CWnd)

BEGIN_MESSAGE_MAP(CTabPage, CWnd)
	ON_WM_PAINT()
	ON_WM_ERASEBKGND()
	ON_WM_LBUTTONDOWN()
	ON_WM_CREATE()
	ON_WM_SIZE()
END_MESSAGE_MAP()

// CTabPage 构造/析构

CTabPage::CTabPage()
{
	m_nCurSel = -1;
	m_nTabHeight = 120;  // 增加选项卡高度，从80改为120
	m_clrBackground = RGB(0, 0, 0);  // 黑色背景
	m_clrText = RGB(180, 180, 180);  // 浅灰色文字
	m_clrSelText = RGB(255, 255, 255);  // 白色选中文字
	m_clrSelTab = RGB(64, 64, 64);  // 深灰色选中背景
	m_bInitialized = false;
	
	// 初始化分隔符属性
	m_clrSeparator = RGB(50, 50, 50);  // 深灰色分隔符
	m_bShowSeparator = TRUE;  // 默认显示分隔符
	
	// 初始化回调函数指针
	m_pfnCallback = NULL;
	m_lParamCallback = 0;
}

CTabPage::~CTabPage()
{
	// 清空子窗口数组，但不删除窗口对象
	// 窗口对象的销毁由框架负责，这里只清理数组
	for (int i = 0; i < m_arrChildWnds.GetSize(); i++)
	{
		// 将子窗口数组项设为 NULL，避免悬挂引用
		m_arrChildWnds.SetAt(i, NULL);
	}
	m_arrChildWnds.RemoveAll();
}

// CTabPage 操作

// 创建选项卡控件
BOOL CTabPage::Create(const RECT& rect, CWnd* pParentWnd, UINT nID)
{
	// 注册窗口类名
	WNDCLASS wndcls;
	HINSTANCE hInst = AfxGetInstanceHandle();
	
	// 检查窗口类是否已注册
	if (!(::GetClassInfo(hInst, _T("CTabPage"), &wndcls)))
	{
		wndcls.style = CS_DBLCLKS | CS_HREDRAW | CS_VREDRAW;
		wndcls.lpfnWndProc = ::DefWindowProc;
		wndcls.cbClsExtra = wndcls.cbWndExtra = 0;
		wndcls.hInstance = hInst;
		wndcls.hIcon = NULL;
		wndcls.hCursor = AfxGetApp()->LoadStandardCursor(IDC_ARROW);
		wndcls.hbrBackground = ::CreateSolidBrush(m_clrBackground);
		wndcls.lpszMenuName = NULL;
		wndcls.lpszClassName = _T("CTabPage");
		
		if (!AfxRegisterClass(&wndcls))
		{
			AfxThrowResourceException();
			return FALSE;
		}
	}
	
	// 创建窗口
	if (!CWnd::Create(_T("CTabPage"), NULL, WS_CHILD | WS_VISIBLE, rect, pParentWnd, nID))
		return FALSE;
	
	return TRUE;
}

// 添加一个选项卡页面
BOOL CTabPage::AddPage(LPCTSTR lpszTitle, CWnd* pChildWnd)
{
	// 添加标题到数组
	m_arrTabNames.Add(lpszTitle);
	
	// 添加子窗口到数组，即使是NULL也添加占位
	m_arrChildWnds.Add(pChildWnd);
	
	// 如果是第一个添加的页面，选中它
	if (m_nCurSel == -1)
		SetCurSel(0);
	
	// 需要重绘
	if (::IsWindow(GetSafeHwnd()))
		Invalidate();
	
	return TRUE;
}

// 设置选项卡点击回调函数
void CTabPage::SetTabClickCallback(TABPAGE_NOTIFY_CALLBACK pfnCallback, LPARAM lParam)
{
	m_pfnCallback = pfnCallback;
	m_lParamCallback = lParam;
}

// 触发回调函数
void CTabPage::NotifyTabClick(int nIndex)
{
	// 如果回调函数存在，则调用它
	if (m_pfnCallback != NULL)
	{
		m_pfnCallback(nIndex, m_lParamCallback);
	}
}

// 设置当前选中的选项卡
void CTabPage::SetCurSel(int nSel)
{
	// 确保索引在有效范围内
	if (nSel < 0 || nSel >= m_arrTabNames.GetSize())
	{
		TRACE(_T("CTabPage::SetCurSel - 无效的选项卡索引: %d\n"), nSel);
		return;
	}
	
	// 如果已经是当前选择，不需要处理
	if (nSel == m_nCurSel)
		return;
	
	// 保存新的选择
	m_nCurSel = nSel;
	
	// 确保两个数组长度一致
	if (m_arrChildWnds.GetSize() != m_arrTabNames.GetSize())
	{
		TRACE(_T("警告：CTabPage中子窗口数组与标签数组长度不一致!\n"));
	}
	
	// 显示当前页面
	ShowCurPage();
	
	// 触发回调函数
	NotifyTabClick(nSel);
	
	// 重绘
	Invalidate();
}

// 获取当前选中的选项卡
int CTabPage::GetCurSel() const
{
	return m_nCurSel;
}

// 获取选项卡总数
int CTabPage::GetPageCount() const
{
	return (int)m_arrTabNames.GetSize();
}

// 设置背景色
void CTabPage::SetBkColor(COLORREF clr)
{
	m_clrBackground = clr;
	if (::IsWindow(GetSafeHwnd()))
		Invalidate();
}

// 设置文字颜色
void CTabPage::SetTextColor(COLORREF clr)
{
	m_clrText = clr;
	if (::IsWindow(GetSafeHwnd()))
		Invalidate();
}

// 设置选中项文字颜色
void CTabPage::SetSelTextColor(COLORREF clr)
{
	m_clrSelText = clr;
	if (::IsWindow(GetSafeHwnd()))
		Invalidate();
}

// 设置选中项背景色
void CTabPage::SetSelTabColor(COLORREF clr)
{
	m_clrSelTab = clr;
	if (::IsWindow(GetSafeHwnd()))
		Invalidate();
}

// 设置选项卡高度
void CTabPage::SetTabHeight(int nHeight)
{
	// 设置一个合理的最小高度
	if (nHeight < 40)
		nHeight = 40;
	
	if (m_nTabHeight != nHeight)
	{
		m_nTabHeight = nHeight;
		
		// 如果窗口已创建，调整字体大小并重绘
		if (::IsWindow(GetSafeHwnd()))
		{
			// 根据选项卡高度调整字体
			if (m_nTabHeight <= 80)
				CreateTabFont(); // 使用默认字体大小
			else if (m_nTabHeight <= 120)
			{
				LOGFONT lf;
				memset(&lf, 0, sizeof(LOGFONT));
				lf.lfHeight = -20; // 中等字体大小
				lf.lfWeight = FW_NORMAL;
				lf.lfCharSet = DEFAULT_CHARSET;
				lf.lfOutPrecision = OUT_DEFAULT_PRECIS;
				lf.lfClipPrecision = CLIP_DEFAULT_PRECIS;
				lf.lfQuality = DEFAULT_QUALITY;
				lf.lfPitchAndFamily = DEFAULT_PITCH | FF_DONTCARE;
				_tcscpy_s(lf.lfFaceName, _T("微软雅黑"));
				m_fontTab.DeleteObject();
				m_fontTab.CreateFontIndirect(&lf);
			}
			else
			{
				LOGFONT lf;
				memset(&lf, 0, sizeof(LOGFONT));
				lf.lfHeight = -24; // 大字体
				lf.lfWeight = FW_NORMAL;
				lf.lfCharSet = DEFAULT_CHARSET;
				lf.lfOutPrecision = OUT_DEFAULT_PRECIS;
				lf.lfClipPrecision = CLIP_DEFAULT_PRECIS;
				lf.lfQuality = DEFAULT_QUALITY;
				lf.lfPitchAndFamily = DEFAULT_PITCH | FF_DONTCARE;
				_tcscpy_s(lf.lfFaceName, _T("微软雅黑"));
				m_fontTab.DeleteObject();
				m_fontTab.CreateFontIndirect(&lf);
			}
			
			// 重绘窗口
			Invalidate();
		}
	}
}

// 获取选项卡高度
int CTabPage::GetTabHeight() const
{
	return m_nTabHeight;
}

// 设置分隔符颜色
void CTabPage::SetSeparatorColor(COLORREF clr)
{
	if (m_clrSeparator != clr)
	{
		m_clrSeparator = clr;
		if (::IsWindow(GetSafeHwnd()))
			Invalidate();
	}
}

// 设置是否显示分隔符
void CTabPage::SetShowSeparator(BOOL bShow)
{
	if (m_bShowSeparator != bShow)
	{
		m_bShowSeparator = bShow;
		if (::IsWindow(GetSafeHwnd()))
			Invalidate();
	}
}

// CTabPage 实现

// 初始化控件
void CTabPage::Initialize()
{
	// 创建字体
	CreateTabFont();
	
	// 标记为已初始化
	m_bInitialized = true;
}

// 绘制选项卡
void CTabPage::DrawTabs(CDC* pDC)
{
	int nCount = GetPageCount();
	if (nCount <= 0)
		return;
	
	// 获取客户区大小
	CRect rect;
	GetClientRect(&rect);
	
	// 创建背景刷
	CBrush brushBack(m_clrBackground);
	CBrush brushSel(m_clrSelTab);
	
	// 设置字体和背景模式
	CFont* pOldFont = pDC->SelectObject(&m_fontTab);
	int nOldMode = pDC->SetBkMode(TRANSPARENT);
	
	// 为每个选项卡绘制
	for (int i = 0; i < nCount; i++)
	{
		CRect tabRect = GetTabRect(i);
		
		// 选择正确的刷子和颜色
		if (i == m_nCurSel)
		{
			pDC->FillRect(tabRect, &brushSel);
			DrawVerticalText(pDC, m_arrTabNames[i], tabRect, m_clrSelText);
		}
		else
		{
			pDC->FillRect(tabRect, &brushBack);
			DrawVerticalText(pDC, m_arrTabNames[i], tabRect, m_clrText);
		}
		
		// 如果不是最后一个选项卡，绘制分隔线
		if (i < nCount - 1 && m_bShowSeparator)
		{
			// 计算分隔线的位置（在当前选项卡的底部）
			CRect lineRect = tabRect;
			lineRect.top = lineRect.bottom - 1;  // 1像素高
			
			// 绘制分隔线（使用深灰色）
			pDC->FillSolidRect(lineRect, m_clrSeparator);  // 使用分隔符颜色
		}
	}
	
	// 恢复设置
	pDC->SetBkMode(nOldMode);
	pDC->SelectObject(pOldFont);
}

// 绘制垂直文字
void CTabPage::DrawVerticalText(CDC* pDC, const CString& text, CRect rect, COLORREF color)
{
	// 保存当前图形状态
	UINT nOldAlign = pDC->SetTextAlign(TA_LEFT);
	COLORREF oldColor = pDC->SetTextColor(color);
	
	// 获取文本尺寸，用于计算显示位置
	CSize textSize;
	textSize = pDC->GetTextExtent(text);
	
	// 创建垂直文字的变换矩阵（逆时针旋转90度）
	XFORM xform;
	xform.eM11 = 0.0f;
	xform.eM12 = -1.0f;  // 垂直向上
	xform.eM21 = 1.0f;   // 垂直向上
	xform.eM22 = 0.0f;
	
	// 水平位置 - 设置X坐标(eDx)，确保文本显示在选项卡内部
	const int LEFT_MARGIN = 2; // 左侧边距，控制文本距离选项卡左边的距离
	
	// 确保X坐标位于选项卡边界内
	// 由于LEFT_MARGIN已经很小，不再需要额外的安全边距计算
	xform.eDx = (FLOAT)(rect.left + LEFT_MARGIN);
	
	// 垂直位置 - 计算Y坐标(eDy)
	// 在垂直文本中，原始文本的宽度(textSize.cx)成为垂直方向的长度
	int textLength = textSize.cx;
	int tabHeight = rect.Height();
	
	// 安全边距，避免文本太靠近选项卡边缘
	const int VERTICAL_MARGIN = 8;
	int availableHeight = tabHeight - (2 * VERTICAL_MARGIN);
	
	// 根据文本长度和选项卡高度调整Y坐标
	if (textLength > availableHeight)
	{
		// 如果文本太长，确保它在选项卡中心，但被裁剪在可用区域内
		// 这里的关键是：由于垂直向上显示文本，起点在底部，所以eDy应为底部坐标
		xform.eDy = (FLOAT)(rect.top + tabHeight - VERTICAL_MARGIN);
	}
	else
	{
		// 文本长度适中，垂直居中显示
		// 计算方式：顶部坐标 + (选项卡高度 + 文本长度)/2
		// 由于垂直文本从下往上绘制，所以起点Y是选项卡中心点加上文本长度的一半
		xform.eDy = (FLOAT)(rect.top + (tabHeight + textLength) / 2.0f);
	}
	
	// 设置图形模式和变换矩阵
	int nOldGraphicsMode = pDC->SetGraphicsMode(GM_ADVANCED);
	pDC->SetWorldTransform(&xform);
	
	// 设置文本对齐方式为左对齐
	pDC->SetTextAlign(TA_LEFT);
	
	// 绘制文字
	pDC->TextOut(0, 0, text);
	
	// 恢复图形状态
	XFORM xformIdentity;
	xformIdentity.eM11 = 1.0f;
	xformIdentity.eM12 = 0.0f;
	xformIdentity.eM21 = 0.0f;
	xformIdentity.eM22 = 1.0f;
	xformIdentity.eDx = 0.0f;
	xformIdentity.eDy = 0.0f;
	pDC->SetWorldTransform(&xformIdentity);
	pDC->SetGraphicsMode(nOldGraphicsMode);
	pDC->SetTextAlign(nOldAlign);
	pDC->SetTextColor(oldColor);
}

// 获取选项卡矩形区域
CRect CTabPage::GetTabRect(int nIndex) const
{
	CRect rect;
	GetClientRect(&rect);
	
	int nCount = GetPageCount();
	if (nCount <= 0 || nIndex < 0 || nIndex >= nCount)
		return CRect(0, 0, 0, 0);
	
	// 计算单个选项卡的高度
	int nSingleTabHeight = rect.Height() / nCount;
	if (nSingleTabHeight > m_nTabHeight)
		nSingleTabHeight = m_nTabHeight;
	
	// 返回选项卡区域
	return CRect(rect.left, rect.top + nIndex * nSingleTabHeight,
	             rect.right, rect.top + (nIndex + 1) * nSingleTabHeight);
}

// 显示当前页面
void CTabPage::ShowCurPage()
{
	// 确保两个数组长度一致
	if (m_arrChildWnds.GetSize() != m_arrTabNames.GetSize())
	{
		TRACE(_T("警告：CTabPage中子窗口数组与标签数组长度不一致!\n"));
		return; // 避免继续处理可能导致的问题
	}

	// 隐藏所有子窗口
	for (int i = 0; i < m_arrChildWnds.GetSize(); i++)
	{
		CWnd* pWnd = (CWnd*)m_arrChildWnds[i];
		if (pWnd && ::IsWindow(pWnd->GetSafeHwnd()))
			pWnd->ShowWindow(SW_HIDE);
	}
	
	// 显示当前选中的子窗口
	if (m_nCurSel >= 0 && m_nCurSel < m_arrChildWnds.GetSize())
	{
		CWnd* pWnd = (CWnd*)m_arrChildWnds[m_nCurSel];
		if (pWnd && ::IsWindow(pWnd->GetSafeHwnd()))
		{
			// 定位子窗口
			CRect rect;
			GetClientRect(&rect);
			
			// 让子窗口填满整个区域
			pWnd->SetParent(this);
			pWnd->MoveWindow(rect);
			pWnd->ShowWindow(SW_SHOW);
		}
	}
}

// 创建选项卡字体
void CTabPage::CreateTabFont()
{
	// 创建垂直字体
	LOGFONT lf;
	memset(&lf, 0, sizeof(LOGFONT));
	
	lf.lfHeight = -20;  // 增加字体高度，从-16改为-20，使其与更大的选项卡匹配
	lf.lfWeight = FW_NORMAL;  // 字体粗细
	lf.lfCharSet = DEFAULT_CHARSET;
	lf.lfOutPrecision = OUT_DEFAULT_PRECIS;
	lf.lfClipPrecision = CLIP_DEFAULT_PRECIS;
	lf.lfQuality = DEFAULT_QUALITY;
	lf.lfPitchAndFamily = DEFAULT_PITCH | FF_DONTCARE;
	_tcscpy_s(lf.lfFaceName, _T("微软雅黑"));  // 字体名称
	
	// 先删除旧字体，然后创建新字体
	m_fontTab.DeleteObject();
	m_fontTab.CreateFontIndirect(&lf);
}

// CTabPage 消息处理程序

void CTabPage::OnPaint()
{
	// 确保初始化
	if (!m_bInitialized)
		Initialize();
	
	CPaintDC dc(this);
	
	// 获取客户区大小
	CRect rect;
	GetClientRect(&rect);
	
	// 创建内存DC以减少闪烁
	CDC memDC;
	memDC.CreateCompatibleDC(&dc);
	
	CBitmap memBitmap;
	memBitmap.CreateCompatibleBitmap(&dc, rect.Width(), rect.Height());
	CBitmap* pOldBitmap = memDC.SelectObject(&memBitmap);
	
	// 填充背景
	memDC.FillSolidRect(&rect, m_clrBackground);
	
	// 绘制选项卡
	DrawTabs(&memDC);
	
	// 复制到屏幕
	dc.BitBlt(0, 0, rect.Width(), rect.Height(), &memDC, 0, 0, SRCCOPY);
	
	// 清理
	memDC.SelectObject(pOldBitmap);
	memBitmap.DeleteObject();
	memDC.DeleteDC();
}

BOOL CTabPage::OnEraseBkgnd(CDC* pDC)
{
	return TRUE;  // 我们在OnPaint中处理背景
}

void CTabPage::OnLButtonDown(UINT nFlags, CPoint point)
{
	// 获取客户区大小
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 首先检查点击是否在选项卡区域内
	if (!rectClient.PtInRect(point))
	{
		CWnd::OnLButtonDown(nFlags, point);
		return;
	}
	
	// 检查点击的是哪个选项卡
	int nCount = GetPageCount();
	bool bTabClicked = false;
	
	for (int i = 0; i < nCount; i++)
	{
		CRect tabRect = GetTabRect(i);
		if (tabRect.PtInRect(point))
		{
			bTabClicked = true;
			
			// 如果点击的不是当前选中的选项卡，则切换
			if (i != m_nCurSel)
			{
				// 设置新的选中项并触发回调
				SetCurSel(i);
				// 注意：SetCurSel中已经调用了NotifyTabClick，所以这里不需要再调用
			}
			else
			{
				// 如果点击已经选中的选项卡，仍然触发回调，以便实现二次点击功能
				NotifyTabClick(i);
			}
			break;
		}
	}
	
	// 如果没有点击任何选项卡，但点击在控件区域内
	if (!bTabClicked && m_nCurSel >= 0)
	{
		// 这种情况下可能点击在了当前显示的子窗口中
		// 将消息传递给当前子窗口
		if (m_nCurSel < m_arrChildWnds.GetSize())
		{
			CWnd* pWnd = (CWnd*)m_arrChildWnds[m_nCurSel];
			if (pWnd && ::IsWindow(pWnd->GetSafeHwnd()))
			{
				// 将点击坐标转换为子窗口坐标
				CPoint ptChild = point;
				MapWindowPoints(pWnd, &ptChild, 1);
				
				// 发送消息给子窗口
				pWnd->SendMessage(WM_LBUTTONDOWN, nFlags, MAKELPARAM(ptChild.x, ptChild.y));
				
				// 不再调用基类方法，因为已经处理了消息
				return;
			}
		}
	}
	
	// 调用基类方法
	CWnd::OnLButtonDown(nFlags, point);
}

int CTabPage::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CWnd::OnCreate(lpCreateStruct) == -1)
		return -1;
	
	// 初始化控件
	Initialize();
	
	return 0;
}

void CTabPage::OnSize(UINT nType, int cx, int cy)
{
	CWnd::OnSize(nType, cx, cy);
	
	// 窗口最小化时不处理
	if (nType == SIZE_MINIMIZED)
		return;
	
	// 获取新的客户区大小
	CRect rect;
	GetClientRect(&rect);
	
	// 如果选项卡数组和子窗口数组长度不一致，记录警告但继续处理
	if (m_arrTabNames.GetSize() != m_arrChildWnds.GetSize())
	{
		TRACE(_T("CTabPage::OnSize - 标签数组和子窗口数组长度不一致!\n"));
	}
	
	// 调整当前子窗口大小
	if (m_nCurSel >= 0 && m_nCurSel < m_arrChildWnds.GetSize())
	{
		CWnd* pWnd = (CWnd*)m_arrChildWnds[m_nCurSel];
		if (pWnd && ::IsWindow(pWnd->GetSafeHwnd()))
		{
			// 调整当前显示的子窗口大小
			pWnd->MoveWindow(rect);
		}
	}
	
	// 重绘选项卡区域
	Invalidate();
} 