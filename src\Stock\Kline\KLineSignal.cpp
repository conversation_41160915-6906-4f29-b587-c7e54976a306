﻿// KLineIndicator.cpp: CKLineSignal 类的实现
//

#include "pch.h"
#include "..\framework.h"
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "KLineSignal.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CKLineSignal

IMPLEMENT_DYNCREATE(CKLineSignal, CView)

BEGIN_MESSAGE_MAP(CKLineSignal, CView)
	ON_WM_ERASEBKGND()
	ON_WM_SIZE()
	ON_WM_LBUTTONDOWN()
END_MESSAGE_MAP()

// CKLineSignal 构造/析构

CKLineSignal::CKLineSignal() noexcept
{
	// 初始化股票代码
	m_strCode = _T("600000");
	
	// 默认选择MACD指标
	m_nCurrentIndicator = 0;
}

CKLineSignal::~CKLineSignal()
{
}

// 获取文档指针
CStockDoc* CKLineSignal::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 设置股票代码
void CKLineSignal::SetStockCode(const CString& strCode)
{
	m_strCode = strCode;
	
	// 重绘视图
	Invalidate();
}

// 响应文档更新
void CKLineSignal::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 调用基类的OnUpdate方法
	CView::OnUpdate(pSender, lHint, pHint);
	
	// 从文档获取当前股票代码
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		CString strCurrentStock = CString(pDoc->GetCurrentStock().c_str());
		
		// 如果当前显示的股票代码与文档中的不同，则更新
		if (m_strCode != strCurrentStock)
		{
			// 设置新的股票代码，这会触发视图更新
			SetStockCode(strCurrentStock);
			
			TRACE(_T("CKLineSignal::OnUpdate - 股票代码已更新: %s\n"), strCurrentStock);
		}
	}
}

BOOL CKLineSignal::PreCreateWindow(CREATESTRUCT& cs)
{
	// 移除窗口边框
	cs.style &= ~WS_BORDER;
	return CView::PreCreateWindow(cs);
}

// 初始化视图
void CKLineSignal::OnInitialUpdate()
{
	CView::OnInitialUpdate();
}

// CKLineSignal 绘图
void CKLineSignal::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;
	
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 设置背景为黑色
	pDC->FillSolidRect(rectClient, RGB(0, 0, 0));
	
	// 指标选择区域的高度
	int nSelectorHeight = 30;
	
	// 计算指标显示区域 - 从顶部到底部选择区域的上方
	CRect rcIndicator = rectClient;
	rcIndicator.bottom -= nSelectorHeight;  // 预留底部空间给选择区域
	
	// 根据当前选择的指标绘制相应的图形
	switch (m_nCurrentIndicator)
	{
	case 0:  // MACD
		DrawMACD(pDC, rcIndicator);
		break;
	case 1:  // RSI
		DrawRSI(pDC, rcIndicator);
		break;
	case 2:  // KDJ
		DrawKDJ(pDC, rcIndicator);
		break;
	}
	
	// 绘制指标选择区域 - 现在在底部绘制
	DrawIndicatorSelector(pDC);
}

// 绘制指标选择区域
void CKLineSignal::DrawIndicatorSelector(CDC* pDC)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 定义选择区域 - 现在在底部
	int nSelectorHeight = 30;
	CRect rcSelector(rectClient.left, rectClient.bottom - nSelectorHeight, 
	                rectClient.right, rectClient.bottom);
	
	// 绘制背景
	pDC->FillSolidRect(rcSelector, RGB(30, 30, 30));
	
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(200, 200, 200));
	pDC->SetBkMode(TRANSPARENT);
	
	// 创建字体
	CFont font;
	font.CreateFont(16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, 
	               DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	               DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&font);
	
	// 定义每个选项的区域
	int width = rectClient.Width() / 3;
	CRect rcMACD(rectClient.left, rcSelector.top, rectClient.left + width, rcSelector.bottom);
	CRect rcRSI(rcMACD.right, rcSelector.top, rcMACD.right + width, rcSelector.bottom);
	CRect rcKDJ(rcRSI.right, rcSelector.top, rectClient.right, rcSelector.bottom);
	
	// 绘制选项
	pDC->DrawText(_T("MACD"), rcMACD, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	pDC->DrawText(_T("RSI"), rcRSI, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	pDC->DrawText(_T("KDJ"), rcKDJ, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	
	// 绘制选中项的高亮
	CPen penHighlight(PS_SOLID, 2, RGB(0, 162, 232));  // 蓝色高亮
	CPen* pOldPen = pDC->SelectObject(&penHighlight);
	
	// 根据当前选择的指标绘制上划线（因为在底部，所以使用上划线）
	CRect rcHighlight;
	switch (m_nCurrentIndicator)
	{
	case 0:  // MACD
		rcHighlight = rcMACD;
		break;
	case 1:  // RSI
		rcHighlight = rcRSI;
		break;
	case 2:  // KDJ
		rcHighlight = rcKDJ;
		break;
	}
	
	pDC->MoveTo(rcHighlight.left + 10, rcHighlight.top + 2);
	pDC->LineTo(rcHighlight.right - 10, rcHighlight.top + 2);
	
	// 恢复原笔和字体
	pDC->SelectObject(pOldPen);
	pDC->SelectObject(pOldFont);
	
	// 绘制分隔线 - 现在在选择区域顶部
	CPen linePen(PS_SOLID, 1, RGB(50, 50, 50));
	pOldPen = pDC->SelectObject(&linePen);
	
	pDC->MoveTo(rcSelector.left, rcSelector.top);
	pDC->LineTo(rcSelector.right, rcSelector.top);
	
	// 恢复原笔
	pDC->SelectObject(pOldPen);
}

// 绘制MACD指标
void CKLineSignal::DrawMACD(CDC* pDC, const CRect& rcArea)
{
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 绘制标题
	CFont titleFont;
	titleFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
	                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	CRect rcTitle(rcArea.left + 10, rcArea.top + 10, rcArea.right - 10, rcArea.top + 30);
	pDC->DrawText(_T("MACD(12,26,9)"), rcTitle, DT_LEFT | DT_VCENTER);
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
	
	// 绘制网格
	CPen gridPen(PS_SOLID, 1, RGB(50, 50, 50));
	CPen* pOldPen = pDC->SelectObject(&gridPen);
	
	// 绘制水平线
	int yStep = rcArea.Height() / 4;
	for (int i = 1; i < 4; i++)
	{
		int y = rcArea.top + i * yStep;
		pDC->MoveTo(rcArea.left, y);
		pDC->LineTo(rcArea.right, y);
	}
	
	// 绘制垂直线
	int xStep = rcArea.Width() / 5;
	for (int i = 1; i < 5; i++)
	{
		int x = rcArea.left + i * xStep;
		pDC->MoveTo(x, rcArea.top);
		pDC->LineTo(x, rcArea.bottom);
	}
	
	// 恢复原笔
	pDC->SelectObject(pOldPen);
	
	// 绘制示例文本
	CFont normalFont;
	normalFont.CreateFont(14, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, 
	                    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                    DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	pDC->SelectObject(&normalFont);
	CRect rcText(rcArea.left + 10, rcArea.top + 50, rcArea.right - 10, rcArea.top + 70);
	pDC->DrawText(_T("MACD 技术指标区域"), rcText, DT_LEFT | DT_VCENTER);
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
}

// 绘制RSI指标
void CKLineSignal::DrawRSI(CDC* pDC, const CRect& rcArea)
{
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 绘制标题
	CFont titleFont;
	titleFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
	                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	CRect rcTitle(rcArea.left + 10, rcArea.top + 10, rcArea.right - 10, rcArea.top + 30);
	pDC->DrawText(_T("RSI(6,12,24)"), rcTitle, DT_LEFT | DT_VCENTER);
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
	
	// 绘制网格
	CPen gridPen(PS_SOLID, 1, RGB(50, 50, 50));
	CPen* pOldPen = pDC->SelectObject(&gridPen);
	
	// 绘制水平线
	int yStep = rcArea.Height() / 4;
	for (int i = 1; i < 4; i++)
	{
		int y = rcArea.top + i * yStep;
		pDC->MoveTo(rcArea.left, y);
		pDC->LineTo(rcArea.right, y);
	}
	
	// 绘制垂直线
	int xStep = rcArea.Width() / 5;
	for (int i = 1; i < 5; i++)
	{
		int x = rcArea.left + i * xStep;
		pDC->MoveTo(x, rcArea.top);
		pDC->LineTo(x, rcArea.bottom);
	}
	
	// 恢复原笔
	pDC->SelectObject(pOldPen);
	
	// 绘制示例文本
	CFont normalFont;
	normalFont.CreateFont(14, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, 
	                    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                    DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	pDC->SelectObject(&normalFont);
	CRect rcText(rcArea.left + 10, rcArea.top + 50, rcArea.right - 10, rcArea.top + 70);
	pDC->DrawText(_T("RSI 技术指标区域"), rcText, DT_LEFT | DT_VCENTER);
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
}

// 绘制KDJ指标
void CKLineSignal::DrawKDJ(CDC* pDC, const CRect& rcArea)
{
	// 设置文本颜色和背景模式
	pDC->SetTextColor(RGB(255, 255, 255));
	pDC->SetBkMode(TRANSPARENT);
	
	// 绘制标题
	CFont titleFont;
	titleFont.CreateFont(16, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, 
	                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                   DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	CFont* pOldFont = pDC->SelectObject(&titleFont);
	
	CRect rcTitle(rcArea.left + 10, rcArea.top + 10, rcArea.right - 10, rcArea.top + 30);
	pDC->DrawText(_T("KDJ(9,3,3)"), rcTitle, DT_LEFT | DT_VCENTER);
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
	
	// 绘制网格
	CPen gridPen(PS_SOLID, 1, RGB(50, 50, 50));
	CPen* pOldPen = pDC->SelectObject(&gridPen);
	
	// 绘制水平线
	int yStep = rcArea.Height() / 4;
	for (int i = 1; i < 4; i++)
	{
		int y = rcArea.top + i * yStep;
		pDC->MoveTo(rcArea.left, y);
		pDC->LineTo(rcArea.right, y);
	}
	
	// 绘制垂直线
	int xStep = rcArea.Width() / 5;
	for (int i = 1; i < 5; i++)
	{
		int x = rcArea.left + i * xStep;
		pDC->MoveTo(x, rcArea.top);
		pDC->LineTo(x, rcArea.bottom);
	}
	
	// 恢复原笔
	pDC->SelectObject(pOldPen);
	
	// 绘制示例文本
	CFont normalFont;
	normalFont.CreateFont(14, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, 
	                    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, 
	                    DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, _T("微软雅黑"));
	
	pDC->SelectObject(&normalFont);
	CRect rcText(rcArea.left + 10, rcArea.top + 50, rcArea.right - 10, rcArea.top + 70);
	pDC->DrawText(_T("KDJ 技术指标区域"), rcText, DT_LEFT | DT_VCENTER);
	
	// 恢复原字体
	pDC->SelectObject(pOldFont);
}

// 防止闪烁
BOOL CKLineSignal::OnEraseBkgnd(CDC* pDC)
{
	return TRUE;
}

// 处理窗口大小变化
void CKLineSignal::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);
	
	// 强制重绘
	Invalidate();
}

// 处理鼠标左键点击
void CKLineSignal::OnLButtonDown(UINT nFlags, CPoint point)
{
	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(&rectClient);
	
	// 检查点击是否在底部的指标选择区域内
	int nSelectorHeight = 30;
	if (point.y >= (rectClient.bottom - nSelectorHeight))
	{
		// 计算选择了哪个指标
		int width = rectClient.Width() / 3;
		int index = point.x / width;
		
		// 检查索引有效性并更新当前指标
		if (index >= 0 && index <= 2 && index != m_nCurrentIndicator)
		{
			m_nCurrentIndicator = index;
			// 重绘视图
			Invalidate();
		}
	}
	
	CView::OnLButtonDown(nFlags, point);
} 