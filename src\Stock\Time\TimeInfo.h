﻿// KLineInfo.h: CTimeInfo 类的接口
//

#pragma once

#include <vector>
#include "../StockDef.h" // 添加StockDef.h头文件，获取Level2Data的定义


class CStockDoc;

// CTimeInfo 视图 - 用于显示K线股票信息
class CTimeInfo : public CView
{
protected: // 仅从序列化创建
	CTimeInfo() noexcept;
	DECLARE_DYNCREATE(CTimeInfo)

// 特性
public:
	CStockDoc* GetDocument() const;

// 操作
public:
	// 设置股票代码和名称
	void SetStockInfo(const CString& strCode, const CString& strName);
	
	// 添加行业概念数据
	void AddIndustryData(const CString& strType, const CString& strName, 
	                     const CString& strChange, BOOL bPositive);
	
	// 清空行业概念数据
	void ClearIndustryData();

// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnInitialUpdate();
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint); // 重写以处理文档更新

// 实现
public:
	virtual ~CTimeInfo();

protected:
	// 股票基本信息
	CString m_strCode;    // 股票代码
	CString m_strName;    // 股票名称
	
	// 分时数据
	CTime m_timeCurrent;  // 当前时间
	CTime m_dateCurrent;  // 当前日期
	double m_dblPrice;    // 当前价格
	double m_dblAvgPrice; // 均价
	double m_dblOpen;     // 开盘价
	double m_dblPreClose; // 昨收价
	double m_dblVolume;   // 成交量
	double m_dblAmount;   // 成交额
	double m_dblChange;   // 涨跌额
	double m_dblChangePercent; // 涨跌幅
	
	// 扩展数据 - 从m_vecStocks获取
	double m_dblHigh;        // 最高价
	double m_dblLow;         // 最低价
	double m_dblVolumeRatio; // 量比
	double m_dblTurnover;    // 换手率
	int m_nBuyVolume;        // 内盘(手)
	int m_nSellVolume;       // 外盘(手)
	double m_dblCirculatingValue; // 流通市值(亿元)
	double m_dblMainInflow;  // 主力买入(元)
	double m_dblMainOutflow; // 主力卖出(元)
	
	// 五档报价数据
	Level2Data m_level2Data; // 五档报价数据
	
	// 行业和概念
	std::vector<CString> m_vecIndustry; // 行业
	std::vector<CString> m_vecConcept;  // 概念
	std::vector<IndustryData> m_vecIndustryData; // 行业概念列表
	int m_nIndustryScrollPos;           // 行业概念滚动位置
	int m_nIndustryTargetScrollPos;     // 行业概念目标滚动位置
	double m_dblIndustryScrollOffset;   // 行业概念平滑滚动偏移
	
	// 预警信息
	std::vector<AlertInfo> m_vecAlerts; // 预警信息列表
	int m_nAlertScrollPos;              // 预警信息滚动位置
	int m_nAlertTargetScrollPos;        // 预警信息目标滚动位置
	double m_dblAlertScrollOffset;      // 预警信息平滑滚动偏移
	
	// 定时器标识
	static const UINT_PTR TIMER_ALERT_SCROLL = 1;      // 预警自动滚动定时器
	static const UINT_PTR TIMER_SMOOTH_SCROLL = 2;     // 平滑滚动动画定时器
	
	// 绘制函数
	void DrawBasicInfo(CDC* pDC);   // 绘制基本信息
	void DrawPriceInfo(CDC* pDC);   // 绘制价格信息
	void DrawLevel2Info(CDC* pDC);  // 绘制五档报价信息
	void DrawIndustryInfo(CDC* pDC); // 绘制行业概念信息
	void DrawAlertInfo(CDC* pDC);    // 绘制预警信息

// 生成的消息映射函数
protected:
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	afx_msg BOOL OnMouseWheel(UINT nFlags, short zDelta, CPoint pt);
	DECLARE_MESSAGE_MAP()

private:
	// 辅助函数 - 检测点是否在预警区域的特定行内
	int HitTestAlertItem(CPoint point);
	
	// 辅助函数 - 检测点是否在行业概念区域的特定行内
	int HitTestIndustryItem(CPoint point);
	
	// 开始平滑滚动动画
	void StartSmoothScroll();
	
	// 更新平滑滚动
	void UpdateSmoothScroll();
	
	// 上次鼠标位置
	CPoint m_ptLastMouse;
	
	// 更新时间戳 - 用于防止过于频繁的刷新
	DWORD m_dwLastUpdateTime;
};

