﻿#include "pch.h"
#include "StockStatusBar.h"

// 定义统一使用的深红色常量
static const COLORREF STATUSBAR_DEEPRED = RGB(180, 0, 0);

// 颜色常量已在ColorDef.h中定义，无需重复定义

IMPLEMENT_DYNAMIC(CStockStatusBar, CStatusBar)

BEGIN_MESSAGE_MAP(CStockStatusBar, CStatusBar)
	ON_WM_PAINT()
	ON_WM_ERASEBKGND()
	ON_WM_SIZE()
	ON_WM_CTLCOLOR()
	ON_WM_TIMER()
END_MESSAGE_MAP()

// 构造函数
CStockStatusBar::CStockStatusBar()
{
	// 设置默认的背景色和边框色
	m_bgColor = STATUSBAR_DEEPRED;  // 深红色背景
	m_borderColor = STATUSBAR_DEEPRED; // 深红色边框

	// 初始化大盘指数数据
	m_marketIndices.resize(5);  // 上证A、深证A、创业板、科创板、北证A

	// 初始化市场统计数据
	m_marketStats = MarketStatData();

	// 创建背景画刷
	m_bgBrush.CreateSolidBrush(m_bgColor);

	// 初始化日期时间
	UpdateDateTime();
}

// 析构函数
CStockStatusBar::~CStockStatusBar()
{
	// 停止定时器
	if (::IsWindow(m_hWnd))
		KillTimer(TIMER_ID);

	if (m_font.GetSafeHandle())
		m_font.DeleteObject();

	if (m_bgBrush.GetSafeHandle())
		m_bgBrush.DeleteObject();
}

// 创建状态栏
BOOL CStockStatusBar::Create(CWnd* pParentWnd, DWORD dwStyle, UINT nID)
{
	// 调用基类创建方法
	if (!CStatusBar::Create(pParentWnd, dwStyle, nID))
		return FALSE;

	// 创建字体
	LOGFONT lf;
	memset(&lf, 0, sizeof(LOGFONT));
	lf.lfHeight = -15;  // 字体大小，从-12增大到-15
	lf.lfWeight = FW_BOLD;  // 粗体
	_tcscpy_s(lf.lfFaceName, LF_FACESIZE, _T("微软雅黑"));
	m_font.CreateFontIndirect(&lf);

	// 创建状态栏面板
	CreatePanels();

	// 设置状态栏背景色
	SetBkColor(m_bgColor);

	// 启动定时器，每秒更新一次时间
	SetTimer(TIMER_ID, 1000, NULL);

	return TRUE;
}

// 处理定时器消息
void CStockStatusBar::OnTimer(UINT_PTR nIDEvent)
{
	if (nIDEvent == TIMER_ID)
	{
		// 更新日期时间
		UpdateDateTime();

		// 刷新显示
		Invalidate(FALSE);
	}

	CStatusBar::OnTimer(nIDEvent);
}

// 更新日期时间
void CStockStatusBar::UpdateDateTime()
{
	CTime time = CTime::GetCurrentTime();
	m_strDateTime = time.Format(_T("%Y-%m-%d %H:%M:%S"));
}

// 设置背景色
BOOL CStockStatusBar::SetBkColor(COLORREF crBk)
{
	m_bgColor = crBk;

	if (m_bgBrush.GetSafeHandle())
		m_bgBrush.DeleteObject();

	m_bgBrush.CreateSolidBrush(m_bgColor);

	if (::IsWindow(m_hWnd))
	{
		Invalidate();
		UpdateWindow();
	}

	return TRUE;
}

// 创建状态栏面板
void CStockStatusBar::CreatePanels()
{
	// 设置面板指示器信息数组
	UINT* indicators = new UINT[PANEL_COUNT];
	for (int i = 0; i < PANEL_COUNT; i++)
		indicators[i] = ID_SEPARATOR + i + 1;  // 自定义ID

	// 设置面板数量
	SetIndicators(indicators, PANEL_COUNT);
	delete[] indicators;

	// 初始化面板矩形数组
	m_panelRects.SetSize(PANEL_COUNT);

	// 计算平均宽度 - 在这里不立即调用，等OnSize事件触发时再计算
	// 移除此处的调用可避免过早访问未完全初始化的面板
	// RecalculatePanelWidths();

	// 手动设置初始宽度，避免在未完全初始化前调用RecalculatePanelWidths
	CRect rectClient;
	GetClientRect(rectClient);
	int nTotalWidth = rectClient.Width();
	int nPanelWidth = nTotalWidth / PANEL_COUNT;

	for (int i = 0; i < PANEL_COUNT; i++)
	{
		int width = (i == PANEL_COUNT - 1) ?
			nTotalWidth - nPanelWidth * (PANEL_COUNT - 1) : nPanelWidth;
		SetPaneInfo(i, ID_SEPARATOR + i + 1, SBPS_NORMAL, width);
	}
}

// 重新计算面板宽度
void CStockStatusBar::RecalculatePanelWidths()
{
	if (!::IsWindow(m_hWnd))
		return;

	// 检查状态栏是否已经创建了面板
	// GetCount()返回状态栏中的面板数量
	int nCount = GetCount();
	if (nCount <= 0 || nCount != PANEL_COUNT)
		return;  // 面板尚未创建或数量不匹配，不进行操作

	CRect rectClient;
	GetClientRect(rectClient);

	// 计算每个面板的平均宽度
	int nTotalWidth = rectClient.Width();
	int nPanelWidth = nTotalWidth / PANEL_COUNT;

	// 最后一个面板使用剩余空间
	int nLastPanelWidth = nTotalWidth - nPanelWidth * (PANEL_COUNT - 1);

	// 设置各个面板的宽度
	for (int i = 0; i < PANEL_COUNT - 1; i++)
	{
		if (i < nCount)  // 确保索引在有效范围内
			SetPaneInfo(i, ID_SEPARATOR + i + 1, SBPS_NORMAL, nPanelWidth);
	}

	// 最后一个面板单独设置
	if (PANEL_COUNT - 1 < nCount)
		SetPaneInfo(PANEL_COUNT - 1, ID_SEPARATOR + PANEL_COUNT, SBPS_NORMAL, nLastPanelWidth);
}

// 设置大盘指数数据
void CStockStatusBar::SetMarketIndex(const MarketIndexData& indexData)
{
	// 确保集合大小为5
	if (m_marketIndices.size() != 5)
	{
		m_marketIndices.resize(5);
	}

	// 根据指数代码或名称确定位置
	int index = GetIndexPosition(indexData);

	if (index >= 0 && index < 5)
	{
		m_marketIndices[index] = indexData;
	}

	UpdateDisplay();
}

// 根据指数数据确定在状态栏中的位置
int CStockStatusBar::GetIndexPosition(const MarketIndexData& indexData)
{
	// 根据指数代码确定位置
	std::string code = indexData._Code;
	std::string name = indexData._Name;

	// 上证指数 - 位置0
	if (code == "000001" || code == "1A0001" || name.find("上证") != std::string::npos)
		return 0;

	// 深证成指 - 位置1
	if (code == "399001" || name.find("深证") != std::string::npos || name.find("深成") != std::string::npos)
		return 1;

	// 创业板指 - 位置2
	if (code == "399006" || name.find("创业") != std::string::npos)
		return 2;

	// 科创50 - 位置3
	if (code == "1B0688" || code == "000688" || name.find("科创") != std::string::npos)
		return 3;

	// 北证50 - 位置4
	if (code == "899050" || code == "151_899050" || name.find("北证") != std::string::npos)
		return 4;

	// 默认返回第一个空位置
	for (int i = 0; i < 5; i++)
	{
		if (m_marketIndices[i]._Code.empty())
			return i;
	}

	return 0; // 默认位置
}

// 批量设置多个指数数据
void CStockStatusBar::SetMarketIndices(const std::vector<MarketIndexData>& indices)
{
	// 确保集合大小为5
	m_marketIndices.resize(5);

	// 清空现有数据
	for (int i = 0; i < 5; i++)
	{
		m_marketIndices[i] = MarketIndexData();
	}

	// 设置新数据
	for (const auto& indexData : indices)
	{
		int index = GetIndexPosition(indexData);
		if (index >= 0 && index < 5)
		{
			m_marketIndices[index] = indexData;
		}
	}

	UpdateDisplay();
}

// 设置市场统计数据
void CStockStatusBar::SetMarketStats(const MarketStatData& statsData)
{
	m_marketStats = statsData;
	UpdateDisplay();
}

// 刷新状态栏显示
void CStockStatusBar::UpdateDisplay()
{
	// 标记需要重绘
	Invalidate();
	UpdateWindow();
}

// 背景擦除 - 返回TRUE表示我们处理了背景绘制
BOOL CStockStatusBar::OnEraseBkgnd(CDC* pDC)
{
	// 填充背景
	CRect rect;
	GetClientRect(rect);
	pDC->FillSolidRect(rect, m_bgColor);

	return TRUE;  // 返回TRUE表示已经处理了背景擦除
}

// 处理WM_CTLCOLOR消息，设置状态栏各个面板的背景色
HBRUSH CStockStatusBar::OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor)
{
	// 对于所有子控件，设置背景透明和使用我们的背景画刷
	pDC->SetBkMode(TRANSPARENT);
	return (HBRUSH)m_bgBrush.GetSafeHandle();
}

// 处理重绘消息
void CStockStatusBar::OnPaint()
{
	CPaintDC dc(this);

	// 获取客户区矩形
	CRect rectClient;
	GetClientRect(rectClient);

	// 创建内存DC进行双缓冲绘制
	CDC memDC;
	memDC.CreateCompatibleDC(&dc);
	CBitmap memBitmap;
	memBitmap.CreateCompatibleBitmap(&dc, rectClient.Width(), rectClient.Height());
	CBitmap* pOldBitmap = memDC.SelectObject(&memBitmap);

	// 设置绘图参数
	memDC.SetBkMode(TRANSPARENT);
	CFont* pOldFont = memDC.SelectObject(&m_font);

	// 填充背景 - 用黑色填充整个状态栏
	memDC.FillSolidRect(rectClient, m_bgColor);

	// 在此处声明pOldPen变量，避免后续使用时出错
	CPen* pOldPen = NULL;

	// 获取各个面板的矩形区域
	CRect rectPane;
	for (int i = 0; i < PANEL_COUNT; i++)
	{
		GetItemRect(i, rectPane);
		m_panelRects[i] = rectPane;
	}

	// 绘制指数信息
	CString strText;

	// 定义亮蓝色作为指数值颜色
	COLORREF indexValueColor = RGB(0, 180, 255); // 亮蓝色
	// 定义灰色作为指数名称颜色
	COLORREF nameColor = RGB(180, 180, 180); // 灰色
	// 指数面板背景色 - 纯黑色
	COLORREF indexBgColor = RGB(0, 0, 0); // 纯黑色，保持指数区域黑色背景

	// 默认指数名称
	CString defaultNames[5] = {_T("上证"), _T("深证"), _T("创业"), _T("科创"), _T("北证")};

	// 绘制所有5个指数面板
	for (int i = 0; i < 5; i++)
	{
		// 先填充面板背景为纯黑色
		memDC.FillSolidRect(m_panelRects[i], indexBgColor);

		if (i < (int)m_marketIndices.size())
		{
			// 有数据的情况 - 显示实际数据
			// 计算涨跌情况
			COLORREF changeColor = COLOUR_WHITE; // 默认白色（不涨不跌）
			float change = m_marketIndices[i].GetChange();
			float changePercent = m_marketIndices[i].GetChangePercent();

			if (change > 0)
				changeColor = COLOUR_RED; // 上涨为红色
			else if (change < 0)
				changeColor = COLOUR_GREEN; // 下跌为绿色

			// 绘制指数名称（灰色）
			CString strName;
			if (!m_marketIndices[i]._Name.empty()) {
				strName = CString(m_marketIndices[i]._Name.c_str());
			} else {
				strName = defaultNames[i];
			}
			DrawPanelSegment(&memDC, i, strName, nameColor, DT_LEFT, 0);

			// 绘制指数值（亮蓝色）
			CString strValue;
			strValue.Format(_T("%.2f"), m_marketIndices[i]._currIndex);
			DrawPanelSegment(&memDC, i, strValue, indexValueColor, DT_CENTER, 1);

			// 绘制涨跌额和涨跌幅（颜色根据涨跌情况）
			CString strChange;
			strChange.Format(_T("%+.2f %+.2f%%"), change, changePercent);
			DrawPanelSegment(&memDC, i, strChange, changeColor, DT_RIGHT, 2);
		}
		else
		{
			// 无数据的情况 - 显示默认名称和占位符
			DrawPanelSegment(&memDC, i, defaultNames[i], nameColor, DT_LEFT, 0);
			DrawPanelSegment(&memDC, i, _T("--"), COLOUR_WHITE, DT_CENTER, 1);
			DrawPanelSegment(&memDC, i, _T("--"), COLOUR_WHITE, DT_RIGHT, 2);
		}
	}

	// 绘制市场统计数据
	// 使用暗灰色背景
	COLORREF statsBgColor = RGB(30, 30, 30); // 暗灰色，保持统计区域深灰色背景

	// 两市成交额
	if (PANEL_COUNT > 5) {
		memDC.FillSolidRect(m_panelRects[5], statsBgColor);
		strText.Format(_T("两市:%.2f亿"), m_marketStats._totalTurnover);
		DrawPanel(&memDC, 5, strText, STATUSBAR_DEEPRED);
	}

	// 上涨家数
	if (PANEL_COUNT > 6) {
		memDC.FillSolidRect(m_panelRects[6], statsBgColor);
		strText.Format(_T("上涨:%d"), m_marketStats._upCount);
		DrawPanel(&memDC, 6, strText, COLOUR_RED);
	}

	// 下跌家数
	if (PANEL_COUNT > 7) {
		memDC.FillSolidRect(m_panelRects[7], statsBgColor);
		strText.Format(_T("下跌:%d"), m_marketStats._downCount);
		DrawPanel(&memDC, 7, strText, COLOUR_GREEN);
	}

	// 涨停家数
	if (PANEL_COUNT > 8) {
		memDC.FillSolidRect(m_panelRects[8], statsBgColor);
		strText.Format(_T("涨停:%d"), m_marketStats._limitUpCount);
		DrawPanel(&memDC, 8, strText, COLOUR_RED);
	}

	// 跌停家数
	if (PANEL_COUNT > 9) {
		memDC.FillSolidRect(m_panelRects[9], statsBgColor);
		strText.Format(_T("跌停:%d"), m_marketStats._limitDownCount);
		DrawPanel(&memDC, 9, strText, COLOUR_GREEN);
	}

	// 绘制日期时间
	if (PANEL_COUNT > 10) {
		memDC.FillSolidRect(m_panelRects[10], statsBgColor);
		DrawPanel(&memDC, 10, m_strDateTime, STATUSBAR_DEEPRED);
	}

	// 恢复原来的DC对象
	if (pOldFont) {
		memDC.SelectObject(pOldFont);
	}

	// 将内存DC的内容复制到屏幕DC
	dc.BitBlt(0, 0, rectClient.Width(), rectClient.Height(), &memDC, 0, 0, SRCCOPY);

	// 清理资源
	memDC.SelectObject(pOldBitmap);
	memBitmap.DeleteObject();
	memDC.DeleteDC();
}

// 绘制单个面板
void CStockStatusBar::DrawPanel(CDC* pDC, int nIndex, const CString& strText, COLORREF textColor)
{
	if (nIndex >= 0 && nIndex < PANEL_COUNT)
	{
		// 注释掉背景填充，背景已在OnPaint中预先填充
		// pDC->FillSolidRect(m_panelRects[nIndex], m_bgColor);

		// 设置文本颜色
		pDC->SetTextColor(textColor);

		// 设置透明背景模式
		int nOldMode = pDC->SetBkMode(TRANSPARENT);

		// 绘制文本，居中对齐
		CRect rcText = m_panelRects[nIndex];
		rcText.DeflateRect(5, 0);  // 留出边距
		pDC->DrawText(strText, rcText, DT_VCENTER | DT_SINGLELINE | DT_END_ELLIPSIS | DT_CENTER);

		// 恢复原背景模式
		pDC->SetBkMode(nOldMode);
	}
}

// 绘制面板中的一部分文本（根据对齐方式）
void CStockStatusBar::DrawPanelSegment(CDC* pDC, int nIndex, const CString& strText, COLORREF textColor,
	UINT nFormat, int nPart)
{
	if (nIndex >= 0 && nIndex < PANEL_COUNT)
	{
		// 设置文本颜色
		pDC->SetTextColor(textColor);

		// 获取面板矩形
		CRect rcText = m_panelRects[nIndex];
		rcText.DeflateRect(5, 0);  // 留出边距

		// 根据部分编号调整矩形位置，重新计算以防止文字重叠
		int totalWidth = rcText.Width();

		switch (nPart) {
		case 0: // 左部分（占30%宽度）
			rcText.right = rcText.left + (totalWidth * 3 / 10);
			break;
		case 1: // 中间部分（占35%宽度）
			{
				int leftThird = rcText.left + (totalWidth * 3 / 10);
				rcText.left = leftThird;
				rcText.right = rcText.left + (totalWidth * 35 / 100);
			}
			break;
		case 2: // 右部分（占35%宽度）
			rcText.left = rcText.right - (totalWidth * 35 / 100);
			break;
		default: // 参数错误，使用整个区域
			break;
		}

		// 设置透明背景模式，避免覆盖已设置的背景颜色
		int nOldMode = pDC->SetBkMode(TRANSPARENT);

		// 绘制文本，使用指定的对齐方式
		pDC->DrawText(strText, rcText, DT_VCENTER | DT_SINGLELINE | DT_END_ELLIPSIS | nFormat);

		// 恢复原背景模式
		pDC->SetBkMode(nOldMode);
	}
}

// 处理大小变化
void CStockStatusBar::OnSize(UINT nType, int cx, int cy)
{
	CStatusBar::OnSize(nType, cx, cy);

	// 当窗口第一次显示或大小变化时，重新计算面板宽度并更新面板矩形
	// 确保状态栏已经完全初始化
	if (::IsWindow(m_hWnd) && GetCount() == PANEL_COUNT)
	{
		RecalculatePanelWidths();

		if (m_panelRects.GetSize() == PANEL_COUNT)
		{
			for (int i = 0; i < PANEL_COUNT; i++)
			{
				GetItemRect(i, m_panelRects[i]);
			}
		}
	}
}