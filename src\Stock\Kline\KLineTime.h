﻿// TimeHistory.h: CKLineTime 类的接口
// 历史分时视图

#pragma once

class CStockDoc;

// 分时图显示区域结构
struct KLineChartRect
{
    CRect infoRect;      // 信息栏区域
    CRect priceRect;     // 价格图区域
    CRect volumeRect;    // 成交量区域
    CRect timeAxisRect;  // 时间轴区域
};

class CKLineTime : public CView
{
protected: // 仅从序列化创建
	CKLineTime() noexcept;
	DECLARE_DYNCREATE(CKLineTime)

// 特性
public:
    CStockDoc* GetDocument() const;

// 操作
public:
	// 设置股票代码
	void SetStockCode(const CString& strCode);

// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnInitialUpdate();
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint); // 添加OnUpdate方法声明

// 实现
public:
	virtual ~CKLineTime();

protected:
    CString m_strCode;

    double m_basePrice;                          // 基准价格
    double m_highestPrice;                       // 最高价
    double m_lowestPrice;                        // 最低价
    double m_maxVolume;                          // 最大成交量
    double m_totalVolume;                        // 总成交量
    double m_totalAmount;                        // 总成交额
    bool m_bLimitCoordinate;                     // 是否使用涨停板坐标
    
    // 十字光标相关
    bool m_bShowCrossCursor;                     // 是否显示十字光标
    CPoint m_crossCursorPos;                     // 十字光标位置
    int m_crossDataIndex;                        // 十字光标对应的数据索引
    
    // 动态演示相关
    int m_nDemoIndex;                           // 当前演示位置
    bool m_bDemoRunning;                        // 是否正在演示
    static const UINT TIMER_DEMO = 1001;        // 演示定时器ID
    
    // 集合竞价区域显示控制
    bool m_bShowAuctionArea;                    // 是否显示集合竞价区域

    // 显示参数
    KLineChartRect m_chartRect;                  // 图表区域
    COLORREF m_bgColor;                          // 背景色
    COLORREF m_priceLinesColor;                  // 价格线颜色
    COLORREF m_avgLineColor;                     // 均线颜色
    COLORREF m_nowPriceLineColor;                // 当前价格线颜色
    COLORREF m_upVolumeColor;                    // 上涨量柱颜色
    COLORREF m_downVolumeColor;                  // 下跌量柱颜色
    COLORREF m_flatVolumeColor;                  // 平盘量柱颜色
    
    // 网格线相关
    std::vector<int> m_vertLinePositions;        // 垂直分隔线位置

    // 绘图函数
    void DrawBackground(CDC* pDC, const CRect& rect);
    virtual void DrawInfoBar(CDC* pDC);
    void DrawPriceChart(CDC* pDC);
    void DrawVolumeChart(CDC* pDC);
    void DrawTimeAxis(CDC* pDC);
    void DrawCoordinates(CDC* pDC);
    void CalculateChartRect(CDC* pDC, const CRect& clientRect);
    void DrawAuctionArea(CDC* pDC, const CRect& priceChartRect, const CRect& volumeChartRect, int auctionEndIndex); // 绘制集合竞价区域
    
   
    // 消息处理
    afx_msg void OnSize(UINT nType, int cx, int cy);
    afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
    afx_msg void OnMouseMove(UINT nFlags, CPoint point);
    //afx_msg void OnRButtonDown(UINT nFlags, CPoint point);
    afx_msg BOOL OnEraseBkgnd(CDC* pDC);
    //afx_msg void OnSwitchCoordinate();
    afx_msg void OnTimer(UINT_PTR nIDEvent);
    afx_msg void OnStartStopDemo();
    afx_msg void OnToggleAuctionArea();
    afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);  // 添加键盘消息处理
    afx_msg BOOL OnMouseWheel(UINT nFlags, short zDelta, CPoint pt);  // 添加鼠标滚轮消息处理
    void DrawCrossCursor(CDC* pDC);  // 绘制十字光标
    
    // 键盘控制功能
    void SwitchToKLineView();  // 切换到K线视图
    void SwitchToNextStock();  // 切换到下一支股票
    void SwitchToPrevStock();  // 切换到上一支股票
    void SwitchToNextDay();    // 切换到后一日分时图
    void SwitchToPrevDay();    // 切换到前一日分时图

// 生成的消息映射函数
protected:
	DECLARE_MESSAGE_MAP()
};



