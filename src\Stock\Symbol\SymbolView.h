﻿// SymbolView.h: CSymbolView 类的接口
//

#pragma once
#include "SymbolBar.h" // 添加导航栏头文件
#include "..\StockSplitter.h" // 添加分割窗口头文件
#include "SymbolGrid.h" // 添加网格控件头文件
#include "..\StockDef.h" // 添加通用定义头文件

class CStockDoc;

class CSymbolView : public CView
{
protected: // 仅从序列化创建
	CSymbolView() noexcept;
	DECLARE_DYNCREATE(CSymbolView)

// 特性
public:
	CStockDoc* GetDocument() const;
	
	// 获取网格控件
	CSymbolGrid* GetSymbolGrid() { return &m_SymbolGrid; }

// 操作
public:

	// 重写
public:
	virtual void OnDraw(CDC* pDC);  // 重写以绘制该视图
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnInitialUpdate();

// 实现
public:
	virtual ~CSymbolView();

protected:
	int m_nSymbolBarHeight; // 导航栏高度
	// 分割窗口
	CStockSplitter m_wndSplitter;
	// 股票网格控件
	CSymbolGrid m_SymbolGrid;

// 生成的消息映射函数
protected:

	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnDestroy();
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
	DECLARE_MESSAGE_MAP()
}; 