﻿// CStockSplitter.cpp: CStockSplitter 类的实现
// 自定义分割窗口类，实现无边框，深红色分隔条

#include "pch.h"
#include "framework.h"
#include "StockSplitter.h"

// CStockSplitter

IMPLEMENT_DYNAMIC(CStockSplitter, CSplitterWnd)

BEGIN_MESSAGE_MAP(CStockSplitter, CSplitterWnd)
	ON_WM_SETCURSOR()
END_MESSAGE_MAP()

// CStockSplitter 构造/析构

CStockSplitter::CStockSplitter()
{
	// 设置分隔条为深红色
	m_clrSplitterBar = RGB(128, 0, 0);  // 深红色
	
	// 设置分隔条宽度 (CY 垂直方向, CX 水平方向)
	// 注意：为解决视觉差异问题，将水平分隔条宽度设置为垂直分隔条的1.5倍
	m_cxSplitter = 4;  // 垂直分隔条宽度，保持4像素
	m_cySplitter = 2;  // 水平分隔条宽度，减小至2像素
	
	// 设置边框宽度为0以实现无边框效果
	m_cxBorder = 0;
	m_cyBorder = 0;
	
	// 设置分隔条的拖动感觉
	m_cxSplitterGap = 4;  // 拖动区域宽度
	m_cySplitterGap = 4;  // 拖动区域高度
	
	// 初始化锁定状态为未锁定
	m_bLocked = FALSE;
    
    // 初始化视图ID计数器
    m_nIDCounter = 1; // 从1开始，因为0通常被视为无效ID
}

CStockSplitter::~CStockSplitter()
{
}

// 设置分隔条锁定状态
void CStockSplitter::SetLocked(BOOL bLocked)
{
	m_bLocked = bLocked;
	// 强制重绘分隔条
	RecalcLayout();
	Invalidate();
}

// 获取分隔条锁定状态
BOOL CStockSplitter::IsLocked() const
{
	return m_bLocked;
}

// CStockSplitter 成员函数

// 重写CreateStatic函数以实现无边框效果
BOOL CStockSplitter::CreateStatic(CWnd* pParentWnd, int nRows, int nCols, 
                                 DWORD dwStyle, UINT nID)
{
	// 确保添加无边框样式
	dwStyle &= ~WS_BORDER;
	dwStyle |= WS_CHILD | WS_VISIBLE;
	
	// 调用基类方法
	return CSplitterWnd::CreateStatic(pParentWnd, nRows, nCols, dwStyle, nID);
}

// 重写OnDrawSplitter函数以自定义分隔条外观
void CStockSplitter::OnDrawSplitter(CDC* pDC, ESplitType nType, const CRect& rect)
{
	if (pDC == NULL)
	{
		// 如果没有DC，调用基类处理重绘消息
		CSplitterWnd::OnDrawSplitter(pDC, nType, rect);
		return;
	}
	
	// 使用相同的分隔条颜色，不再区分锁定状态
	COLORREF clrBar = m_clrSplitterBar;  // 统一使用设置的分隔条颜色
	
	// 定义最终使用的颜色变量（默认为基本分隔条颜色）
	COLORREF finalColor = clrBar;
	bool isHorizontal = false;
	
	switch (nType)
	{
	case splitBorder:  // 边框
		// 不绘制边框以实现无边框效果
		break;
		
	case splitBox:  // 交叉点
		// 绘制交叉点处的方块
		pDC->FillSolidRect(rect, clrBar);
		break;
		
	case splitBar:  // 分隔条
		// 判断是水平分隔条还是垂直分隔条
		isHorizontal = (rect.Width() > rect.Height());
		
		// 水平分隔条需要更明显一些
		if (isHorizontal) {
			// 使水平分隔条颜色略微增强，增加视觉一致性
			int r = GetRValue(finalColor);
			int g = GetGValue(finalColor);
			int b = GetBValue(finalColor);
			
			// 稍微增强红色分量，但不超过255
			r = min(r + 15, 255);
			finalColor = RGB(r, g, b);
		}
		
		// 绘制分隔条，使用计算后的颜色
		pDC->FillSolidRect(rect, finalColor);
		break;
		
	default:
		// 其他类型交给基类处理
		CSplitterWnd::OnDrawSplitter(pDC, nType, rect);
		break;
	}
}

// 重写OnInvertTracker函数，在锁定状态下不显示跟踪反馈
void CStockSplitter::OnInvertTracker(const CRect& rect)
{
	// 如果锁定状态，则不显示拖动效果
	if (m_bLocked)
		return;
		
	// 否则调用基类方法
	CSplitterWnd::OnInvertTracker(rect);
}

// 重写StartTracking函数，在锁定状态下不启动跟踪
void CStockSplitter::StartTracking(int ht)
{
	// 如果锁定状态，则不允许拖动分隔条
	if (m_bLocked)
		return;
		
	// 否则调用基类方法
	CSplitterWnd::StartTracking(ht);
}

// 重写StopTracking函数，在锁定状态下不响应拖动结束事件
void CStockSplitter::StopTracking(BOOL bAccept)
{
	// 如果锁定状态，则忽略拖动结束事件
	if (m_bLocked)
		return;
		
	// 否则调用基类方法
	CSplitterWnd::StopTracking(bAccept);
}

// 重写HitTest函数，在锁定状态下始终返回没有命中分隔条
int CStockSplitter::HitTest(CPoint pt) const
{
	// 如果锁定状态，始终返回0（表示没有命中任何分隔条）
	if (m_bLocked)
		return 0;  // 0表示没有命中分隔条
		
	// 否则调用基类方法
	return CSplitterWnd::HitTest(pt);
}

// 重写OnSetCursor函数，在锁定状态下不改变鼠标样式
BOOL CStockSplitter::OnSetCursor(CWnd* pWnd, UINT nHitTest, UINT message)
{
	// 如果锁定状态，则始终使用普通箭头光标，无论鼠标位于何处
	if (m_bLocked)
	{
		// 强制设置为标准箭头光标
		::SetCursor(::LoadCursor(NULL, IDC_ARROW));
		return TRUE;  // 返回TRUE表示已处理此消息
	}
	
	// 否则调用基类方法
	return CSplitterWnd::OnSetCursor(pWnd, nHitTest, message);
} 


int CStockSplitter::AddView(int nRow, int nCol, CRuntimeClass* pViewClass,
	CCreateContext* pContext)
{
	// hide the current view of the pane
	int PreviousID = HideCurrentView(nRow, nCol);

	// create the new view, if fail, set the previous view current 
	if (CreateView(nRow, nCol, pViewClass, CSize(10, 10), pContext) == 0)
	{

		if (PreviousID != -1)
			SetCurrentView(nRow, nCol, PreviousID);
		return -1;
	}
	//::MessageBox (NULL, "ds", "da", 0);

	// get and store the niew view
	int NewViewID = m_nIDCounter;
	CWnd* pNewWnd = GetPane(nRow, nCol);
	CPoint pane(nRow, nCol);
	long paneID = MAKELONG(pane.x, pane.y);
	m_mapViewPane.insert(map<int, long>::value_type(NewViewID, paneID));
	m_mapIDViews.insert(map<int, CWnd*>::value_type(NewViewID, pNewWnd));

	// set the new view current
	SetCurrentView(nRow, nCol, NewViewID);

	RedrawWindow();
	m_nIDCounter++;
	return NewViewID;
}


void CStockSplitter::SwitchView(int nViewID)
{
	if (GetView(nViewID) == NULL)
		return;

	// find the pane containing the view 
	CPoint pane;
	GetPaneFromViewID(nViewID, &pane);

	// switch views
	HideCurrentView(pane.x, pane.y);
	SetCurrentView(pane.x, pane.y, nViewID);

	RecalcLayout();
}


CWnd* CStockSplitter::GetView(int nViewID)
{
	map<int, CWnd*>::iterator itView;

	itView = m_mapIDViews.find(nViewID);
	if (itView == m_mapIDViews.end())
		return NULL;
	else
		return (*itView).second;
}


CWnd* CStockSplitter::GetCurrentView(int nRow, int nCol, int* nCurID)
{
	long paneID = MAKELONG(nRow, nCol);

	map<long, int>::iterator itCur;
	itCur = m_mapCurrentViews.find(paneID);
	if (itCur == m_mapCurrentViews.end())
		return NULL;
	else
	{
		int PreviousID = (*itCur).second;
		*nCurID = PreviousID;
		return GetView(PreviousID);
	}
}


void CStockSplitter::SetCurrentView(int nRow, int nCol, int nViewID)
{
	long paneID = MAKELONG(nRow, nCol);

	map<long, int>::iterator itCur;
	itCur = m_mapCurrentViews.find(paneID);
	if (itCur != m_mapCurrentViews.end())
		(*itCur).second = nViewID;
	else
		m_mapCurrentViews.insert(map<long, int>::value_type(paneID, nViewID));

	CWnd* pView = GetView(nViewID);
	pView->SetDlgCtrlID(IdFromRowCol(nRow, nCol));
	pView->ShowWindow(SW_SHOW);
}


int CStockSplitter::HideCurrentView(int nRow, int nCol)
{
	int prevID;
	CWnd* pCurView = GetCurrentView(nRow, nCol, &prevID);
	if (pCurView == NULL)
		return -1;
	else
	{
		pCurView->SetDlgCtrlID(0);
		pCurView->ShowWindow(SW_HIDE);
		return prevID;
	}
}


void CStockSplitter::GetPaneFromViewID(int nViewID, CPoint* pane)
{
	map<int, long>::iterator itPane;
	itPane = m_mapViewPane.find(nViewID);
	if (itPane == m_mapViewPane.end())
	{
		pane = NULL;
		return;
	}
	long paneID = (*itPane).second;
	CPoint p(paneID);
	pane->x = p.x;
	pane->y = p.y;
}