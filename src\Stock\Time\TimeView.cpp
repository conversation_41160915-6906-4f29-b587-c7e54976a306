﻿// TimeView.cpp: CTimeView 类的实现
//

#include "pch.h"
#include "..\framework.h"
#ifndef SHARED_HANDLERS
#include "..\Stock.h"
#endif

#include "..\StockDoc.h"
#include "TimeView.h"
#include "..\MainFrm.h"
#include <vector>
#include <algorithm>
#include <random>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// CTimeView

IMPLEMENT_DYNCREATE(CTimeView, CView)

BEGIN_MESSAGE_MAP(CTimeView, CView)
	ON_WM_CREATE()
	ON_WM_SIZE()
	ON_WM_ERASEBKGND()
	ON_WM_LBUTTONDOWN()
	ON_WM_MOUSEMOVE()
	ON_WM_KEYDOWN()
END_MESSAGE_MAP()

// CTimeView 构造/析构

CTimeView::CTimeView() noexcept
{
	//// 初始化股票基本信息
	//m_strCode = _T("600000");
	//m_strName = _T("鸿图证券");
	//m_dblPreClose = 10.0;
	//m_dblOpen = 10.1;
	//m_dblHigh = 10.5;
	//m_dblLow = 9.8;
	//m_dblVolume = 12345678;
	
	// 初始化图表参数
	//m_nDataCount = 241;  // 默认240个数据点（对应交易日4小时）
	
	// 初始化十字光标
	m_bShowCrossCursor = false;
	m_ptCrossCursor = CPoint(0, 0);

	
	// 初始化子视图ID
	m_nTimeLineViewID = 0;
	m_nTimeInfoViewID = 0;
}

CTimeView::~CTimeView()
{
}

// 获取文档指针
CStockDoc* CTimeView::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

// 设置股票代码
void CTimeView::SetStockCode(const std::string& strCode)
{
	// 参数验证
	if (strCode.empty())
	{
		TRACE(_T("SetStockCode: 股票代码为空\n"));
		return;
	}

	// 更新本视图的股票代码
	CString cstrCode(strCode.c_str());
	
	// 从文档获取股票信息
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
	{
		TRACE(_T("SetStockCode: 无法获取文档指针\n"));
		return;
	}

	// 获取股票数据
	int stockIndex = pDoc->GetStockIndex(strCode);
	CString strName = _T("未知股票");
	
	if (stockIndex >= 0)
	{
		const StockData* pStock = pDoc->GetStock(stockIndex);
		if (pStock)
		{
			strName = CString(pStock->_Name.c_str());
		}
	}

	// 传递给子视图
	if (::IsWindow(m_wndSplitter.GetSafeHwnd()))
	{
		// 传递给TimeLine视图
		CTimeLine* pTimeLine = GetTimeLineView();
		if (pTimeLine)
		{
			pTimeLine->SetStockCode(strCode);
			TRACE(_T("SetStockCode: 已设置TimeLine股票代码为 %s\n"), cstrCode);
		}
		else
		{
			TRACE(_T("SetStockCode: 无法获取TimeLine视图\n"));
		}
		
		// 传递给TimeInfo视图
		if (m_nTimeInfoViewID > 0)
		{
			CTimeInfo* pTimeInfo = (CTimeInfo*)m_wndSplitter.GetView(m_nTimeInfoViewID);
			if (pTimeInfo)
			{
				pTimeInfo->SetStockInfo(cstrCode, strName);
				TRACE(_T("SetStockCode: 已设置TimeInfo股票信息 %s - %s\n"), cstrCode, strName);
			}
			else
			{
				TRACE(_T("SetStockCode: 无法获取TimeInfo视图\n"));
			}
		}
	}
	else
	{
		TRACE(_T("SetStockCode: 分割窗口尚未创建\n"));
	}

	// 重绘视图
	Invalidate();
}

BOOL CTimeView::PreCreateWindow(CREATESTRUCT& cs)
{
	return CView::PreCreateWindow(cs);
}

// CTimeView 绘图
void CTimeView::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;
	
	// 视图内容由子视图处理
}

// 初始化视图
void CTimeView::OnInitialUpdate()
{
	CView::OnInitialUpdate();
}

// CTimeView 消息处理程序

int CTimeView::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CView::OnCreate(lpCreateStruct) == -1)
		return -1;
	
	// 创建分割窗口
	if (!CreateSplitterWindow())
	{
		TRACE0("Failed to create splitter window\n");
		return -1;
	}
	
	return 0;
}

// 创建分割窗口
BOOL CTimeView::CreateSplitterWindow()
{
	// 获取客户区大小
	CRect rcClient;
	GetClientRect(&rcClient);
	
	// 创建1行2列的分割窗口（左右分割）
	if (!m_wndSplitter.CreateStatic(this, 1, 2, WS_CHILD | WS_VISIBLE))
	{
		TRACE0("Failed to create splitter window\n");
		return FALSE;
	}
	
	// 计算合理的布局比例，避免溢出
	const int MIN_LEFT_WIDTH = 400;    // 左侧最小宽度
	const int MIN_RIGHT_WIDTH = 200;   // 右侧最小宽度
	const int PREFERRED_RIGHT_WIDTH = 300; // 右侧首选宽度
	const int MIN_TOTAL_WIDTH = MIN_LEFT_WIDTH + MIN_RIGHT_WIDTH;
	
	int rightWidth, leftWidth;
	
	if (rcClient.Width() < MIN_TOTAL_WIDTH)
	{
		// 窗口太小时，按比例分配，确保左侧不少于2/3
		leftWidth = (rcClient.Width() * 2) / 3;
		rightWidth = rcClient.Width() - leftWidth;
		
		// 确保最小宽度
		if (leftWidth < MIN_LEFT_WIDTH / 2) leftWidth = MIN_LEFT_WIDTH / 2;
		if (rightWidth < MIN_RIGHT_WIDTH / 2) 
		{
			rightWidth = MIN_RIGHT_WIDTH / 2;
			leftWidth = rcClient.Width() - rightWidth;
		}
	}
	else
	{
		// 窗口足够大时，使用首选宽度
		rightWidth = PREFERRED_RIGHT_WIDTH;
		leftWidth = rcClient.Width() - rightWidth;
		
		// 确保左侧宽度不会过小
		if (leftWidth < MIN_LEFT_WIDTH)
		{
			leftWidth = MIN_LEFT_WIDTH;
			rightWidth = rcClient.Width() - leftWidth;
		}
	}
	
	// 最终验证确保不会溢出
	if (leftWidth + rightWidth > rcClient.Width())
	{
		// 按比例缩放
		double scale = (double)rcClient.Width() / (leftWidth + rightWidth);
		leftWidth = (int)(leftWidth * scale);
		rightWidth = rcClient.Width() - leftWidth;
	}
	
	// 创建上下文信息
	CCreateContext context;
	context.m_pCurrentDoc = GetDocument();
	context.m_pCurrentFrame = GetParentFrame();
	context.m_pLastView = this;
	context.m_pNewDocTemplate = NULL;
	context.m_pNewViewClass = NULL;
	
	// 为分割窗口创建子视图
	// 左侧视图 - 分时线视图
	m_nTimeLineViewID = m_wndSplitter.AddView(0, 0, RUNTIME_CLASS(CTimeLine), &context);
	
	// 右侧视图 - 信息视图
	m_nTimeInfoViewID = m_wndSplitter.AddView(0, 1, RUNTIME_CLASS(CTimeInfo), &context);
	
	// 设置列信息 - 必须在创建子视图之后
	m_wndSplitter.SetColumnInfo(0, leftWidth, 100);
	m_wndSplitter.SetColumnInfo(1, rightWidth, 100);
	
	// 强制重新计算布局
	m_wndSplitter.RecalcLayout();
	
	// 将股票代码设置给子视图
	// 注意：在分割窗口创建时，子视图还没有股票代码，所以这里不设置
	// 股票代码将通过SetStockCode方法设置
	
	return TRUE;
}

// 获取分时线视图指针
CTimeLine* CTimeView::GetTimeLineView()
{
	if (::IsWindow(m_wndSplitter.GetSafeHwnd()))
	{
		CWnd* pWnd = m_wndSplitter.GetPane(0, 0);
		if (pWnd && pWnd->IsKindOf(RUNTIME_CLASS(CTimeLine)))
		{
			return static_cast<CTimeLine*>(pWnd);
		}
	}
	return NULL;
}

// 处理窗口大小变化
void CTimeView::OnSize(UINT nType, int cx, int cy)
{
	CView::OnSize(nType, cx, cy);
	
	// 调整分割窗口大小
	if (m_wndSplitter.GetSafeHwnd())
	{
		m_wndSplitter.MoveWindow(0, 0, cx, cy);
		
		// 使用与CreateSplitterWindow相同的布局计算逻辑
		const int MIN_LEFT_WIDTH = 400;
		const int MIN_RIGHT_WIDTH = 200;
		const int PREFERRED_RIGHT_WIDTH = 300;
		const int MIN_TOTAL_WIDTH = MIN_LEFT_WIDTH + MIN_RIGHT_WIDTH;
		
		int rightWidth, leftWidth;
		
		if (cx < MIN_TOTAL_WIDTH)
		{
			// 窗口太小时，按比例分配
			leftWidth = (cx * 2) / 3;
			rightWidth = cx - leftWidth;
			
			if (leftWidth < MIN_LEFT_WIDTH / 2) leftWidth = MIN_LEFT_WIDTH / 2;
			if (rightWidth < MIN_RIGHT_WIDTH / 2) 
			{
				rightWidth = MIN_RIGHT_WIDTH / 2;
				leftWidth = cx - rightWidth;
			}
		}
		else
		{
			// 窗口足够大时，使用首选宽度
			rightWidth = PREFERRED_RIGHT_WIDTH;
			leftWidth = cx - rightWidth;
			
			if (leftWidth < MIN_LEFT_WIDTH)
			{
				leftWidth = MIN_LEFT_WIDTH;
				rightWidth = cx - leftWidth;
			}
		}
		
		// 最终验证确保不会溢出
		if (leftWidth + rightWidth > cx)
		{
			double scale = (double)cx / (leftWidth + rightWidth);
			leftWidth = (int)(leftWidth * scale);
			rightWidth = cx - leftWidth;
		}
		
		// 设置新的列宽度
		m_wndSplitter.SetColumnInfo(0, leftWidth, 100);
		m_wndSplitter.SetColumnInfo(1, rightWidth, 100);
		
		// 强制重新计算布局
		m_wndSplitter.RecalcLayout();
	}
}

// 防止闪烁
BOOL CTimeView::OnEraseBkgnd(CDC* pDC)
{
	return TRUE;
}

// 处理鼠标左键点击
void CTimeView::OnLButtonDown(UINT nFlags, CPoint point)
{
	// 消息将被传递到子视图
	CView::OnLButtonDown(nFlags, point);
}

// 处理鼠标移动
void CTimeView::OnMouseMove(UINT nFlags, CPoint point)
{
	// 将鼠标事件传递给子视图
	CView::OnMouseMove(nFlags, point);
}

// 响应文档更新
void CTimeView::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint)
{
	// 获取文档
	CStockDoc* pDoc = GetDocument();
	if (!pDoc)
		return;

	// 检查当前股票代码是否变更
	//std::string stockCode = pDoc->GetCurrentStock();
	//if (m_strCode != stockCode.c_str())
	//{
	//	// 更新股票代码
	//	SetStockCode(stockCode);
	//}

	// 调用基类方法
	CView::OnUpdate(pSender, lHint, pHint);
}

// 处理按键事件
void CTimeView::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags)
{
	// 当按下回车键时，切换到技术分析视图
	if (nChar == VK_RETURN)
	{
		// 获取主框架指针
		CMainFrame* pMainFrame = (CMainFrame*)AfxGetMainWnd();
		if (pMainFrame)
		{
			// 切换到K线图视图
			pMainFrame->SwitchView(pMainFrame->m_nKLineViewID);
			
			// 不能直接访问m_wndSplitter，因为它是protected成员
			// 只进行视图切换，焦点会自动设置
			TRACE(_T("TimeView: 按回车键切换到K线图视图\n"));
		}
	}
	
	CView::OnKeyDown(nChar, nRepCnt, nFlags);
}

