﻿#pragma once

#include <vector>
#include <string>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>
#include <map>
#include <future>
#include "StockDef.h"

// 定义用户消息，用于视图更新通知
#define WM_USER_DATA_UPDATED (WM_USER + 100)
#define WM_UPDATE_STOCK_DATA (WM_USER + 101)

// 前向声明
class CWinHttp;
class CStockDoc;

// 定义下载任务类型
enum class TaskType {
    UNKNOWN = 0,
    STOCK_REALTIME,        // 实时行情数据
    STOCK_TIMELINE,        // 个股分时数据
    STOCK_KLINE,           // K线数据
    STOCK_FUND_FLOW,       // 资金流向数据
    MARKET_INDEX,          // 大盘指数数据
    MARKET_TIMELINE,       // 大盘分时数据
    MAIN_FUND_FLOW,        // 主力资金流向数据
    AUCTION_DATA           // 集合竞价数据
};


// 网络数据下载类
class CNetData
{
public:
    CNetData();
    ~CNetData();

    // 初始化网络数据下载模块
    bool Initialize(CStockDoc* pDoc, int threadCount = 4);
    
    // 检查是否已初始化
    bool IsInitialized() const { return m_pDoc != nullptr; }
    
    // 关闭网络数据下载模块
    void Shutdown();
    
    // 检查是否就绪
    bool IsReadyForDownload() const { return m_readyForDownload; }
    
    // 下载实时股票数据 - 根据文档对象
    bool DownloadRealtimeData(const CStockDoc* pDocument);
    
    // 下载实时股票数据 - 根据股票代码列表
    bool DownloadRealtimeData(const std::vector<std::string>& codes);
    
    // 下载个股分时数据
    bool DownloadTimelineData(const std::string& stockCode);
    
    // 下载大盘分时数据
    bool DownloadMarketTimelineData();
    
    // 下载单个大盘指数的分时数据
    bool DownloadSingleMarketTimelineData(const std::string& marketCode, MarketIndexType indexType);
    
    // 下载主力资金流向数据
    bool DownloadMainFundFlowData();
    
    // 下载市场分时数据
    bool DownloadMarketTimeline(const std::string& market);
    
    // 下载集合竞价数据
    bool DownloadAuctionData();
    
    // 批量下载集合竞价数据
    bool DownloadBatchAuctionData(const std::vector<std::string>& stockCodes);
    
    // 解析实时股票数据
    bool ParseRealtimeData(const std::string& data);
    
    // 解析分时数据
    bool ParseTimelineData(const std::string& stockCode, const std::string& data);
    
    // 解析大盘分时数据
    bool ParseMarketTimelineData(const std::string& data, MarketIndexType indexType);
    
    // 解析市场分时数据
    bool ParseMarketTimeline(const std::string& market, const std::string& data);
    
    // 解析主力资金流向数据
    bool ParseMainFundFlowData(const std::string& data);
    
    // 解析集合竞价数据
    bool ParseAuctionData(const std::string& data);
    

    // 启动定时下载
    void StartTimedDownload();
    
    // 停止定时下载
    void StopTimedDownload();
    
    // 设置文档指针
    void SetDocument(CStockDoc* pDoc) { m_pDoc = pDoc; }
    
    // 线程池工作线程函数 - 改为public以便全局函数调用
   // void WorkerThread();
    
    // 定时器线程函数 - 改为public以便全局函数调用
    void TimerThread();

    
private:
    // 成员变量
    CStockDoc* m_pDoc;                      // 文档指针
    std::unique_ptr<CWinHttp> m_pHttp;                      // 默认HTTP对象
    std::unique_ptr<CWinHttp> m_pRealtimeHttp;              // 实时行情数据专用
    std::unique_ptr<CWinHttp> m_pTimelineHttp;              // 分时数据专用
    std::unique_ptr<CWinHttp> m_pMarketTimelineHttp;        // 大盘分时数据专用
    std::unique_ptr<CWinHttp> m_pMainFundFlowHttp;          // 主力资金流专用
    std::unique_ptr<CWinHttp> m_pAuctionHttp;               // 集合竞价数据专用
    
    // 延迟初始化标志
    std::atomic<bool> m_readyForDownload;   // 指示是否准备好开始数据下载
    std::atomic<bool> m_initializedFlag;    // 指示是否完成初始化

    bool InitializeHttpObjects();
    // 释放所有HTTP对象
    void ReleaseHttpObjects();

    // HTTP对象初始化辅助函数
    std::unique_ptr<CWinHttp> CreateHttpClient(const char* serverUrl, bool addCommonHeaders = true);
    void AddCommonHeaders(CWinHttp* httpClient);
    
    std::vector<std::thread> m_threads;     // 工作线程
    std::vector<std::thread> m_workThreads; // 其他工作线程
   // std::queue<DownloadTask> m_taskQueue;   // 任务队列
    std::mutex m_queueMutex;                // 队列互斥锁
    std::condition_variable m_condition;    // 队列条件变量
    std::atomic<bool> m_running;            // 运行标志
    
    int m_threadCount;                      // 工作线程数 
    std::mutex m_threadCountMutex;          // 线程计数互斥锁
    std::atomic<int> m_activeThreads;       // 活动线程数
    
    std::thread m_timerThread;              // 定时器线程
    std::atomic<bool> m_timerRunning;       // 定时器运行标志
    
    // 股票实时数据批次大小（每次请求的股票数量）
    static const int BATCH_SIZE = 500;
};

