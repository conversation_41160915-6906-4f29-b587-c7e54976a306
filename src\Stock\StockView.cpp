﻿// StockView.cpp: CStockView 类的实现
//

#include "pch.h"
#include "framework.h"
// SHARED_HANDLERS 可以在实现预览、缩略图和搜索筛选器句柄的
// ATL 项目中进行定义，并允许与该项目共享文档代码。
#ifndef SHARED_HANDLERS
#include "Stock.h"
#endif

#include "StockDoc.h"
#include "StockView.h"
#include "MainFrm.h"
#include <vector>
#include <algorithm>
#include <random>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// CStockView

IMPLEMENT_DYNCREATE(CStockView, CView)

BEGIN_MESSAGE_MAP(CStockView, CView)
	ON_WM_LBUTTONDOWN()
END_MESSAGE_MAP()

// CStockView 构造/析构

CStockView::CStockView() noexcept
{

}

CStockView::~CStockView()
{
}

// 获取文档指针
CStockDoc* CStockView::GetDocument() const
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CStockDoc)));
	return (CStockDoc*)m_pDocument;
}

BOOL CStockView::PreCreateWindow(CREATESTRUCT& cs)
{
	return CView::PreCreateWindow(cs);
}

// CStockView 绘图

void CStockView::OnDraw(CDC* pDC)
{
	CStockDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;

}

// 初始化视图
void CStockView::OnInitialUpdate()
{
	// 调用基类的OnInitialUpdate
	CView::OnInitialUpdate();

	// 获取文档
	CStockDoc* pDoc = GetDocument();
	if (pDoc)
	{
		// 初始化视图数据
		// 可以根据需要从文档获取数据
	}

	// 通知主框架视图初始化完成
	CMainFrame* pMainFrame = dynamic_cast<CMainFrame*>(AfxGetMainWnd());
	if (pMainFrame)
	{
		TRACE("CStockView::OnInitialUpdate - 通知主框架视图初始化完成\n");
		
		// 使用PostMessage发送初始化完成消息
		::PostMessage(pMainFrame->GetSafeHwnd(), WM_APP + 200, 0, 0);
	}
}

// CStockView 消息处理程序
